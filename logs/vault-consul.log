=== 2025-06-24 14:33:50 - Starting vault-consul ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/vault-consul']
INFO:     Uvicorn running on http://0.0.0.0:7200 (Press CTRL+C to quit)
INFO:     Started reloader process [74521] using WatchFiles
INFO:     Started server process [74524]
INFO:     Waiting for application startup.
INFO:main:🚀 Starting Vault-Consul Service...
INFO:app.vault_client:🔐 Initializing Vault client for vault-consul
INFO:app.vault_client:🔧 Vault URL: http://localhost:8200
INFO:app.vault_client:🔧 Auth method: token
INFO:app.vault_client:🔧 Environment: development
INFO:app.vault_client:🔍 Checking Vault connectivity...
INFO:app.vault_client:✅ Vault server is reachable
INFO:app.vault_client:🔑 Vault is reachable, attempting authentication...
ERROR:app.vault_client:❌ Token validation failed: 503
WARNING:app.vault_client:⚠️ Vault authentication failed - switching to fallback mode
INFO:app.vault_client:🔄 Initializing in fallback mode...
INFO:app.vault_client:💡 Fallback mode uses:
INFO:app.vault_client:   - Environment variables from your system
INFO:app.vault_client:   - Default configuration values
INFO:app.vault_client:   - Local .env file values (if available)
INFO:app.vault_client:💡 To enable Vault integration:
INFO:app.vault_client:   - Ensure Vault server is running and accessible
INFO:app.vault_client:   - Set VAULT_TOKEN environment variable for token auth
INFO:app.vault_client:   - Or deploy in Kubernetes for service account auth
INFO:app.vault_client:   - Check Vault URL configuration
INFO:app.vault_client:🔧 Current Vault URL: http://localhost:8200
INFO:app.vault_client:🔧 Current Auth Method: token
INFO:app.vault_client:🔧 VAULT_TOKEN is set
INFO:main:✅ Vault client initialized successfully
INFO:main:📋 Consul integration ready
INFO:main:🎯 Vault-Consul Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:60070 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60072 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-24 14:34:06.713 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: mongodb-cluster
INFO:app.config_manager:⚙️ Initializing configuration manager for mongodb-cluster
INFO:app.config_manager:✅ Configuration manager initialized for mongodb-cluster
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-24 14:34:06.727 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for mongodb-cluster
INFO:     127.0.0.1:60084 - "GET /api/v1/secrets/mongodb-cluster HTTP/1.1" 200 OK
INFO:     127.0.0.1:53042 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-24 14:34:15.077 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: postgresql-cluster
INFO:app.config_manager:⚙️ Initializing configuration manager for postgresql-cluster
INFO:app.config_manager:✅ Configuration manager initialized for postgresql-cluster
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-24 14:34:15.111 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for postgresql-cluster
INFO:     127.0.0.1:53050 - "GET /api/v1/secrets/postgresql-cluster HTTP/1.1" 200 OK
INFO:     127.0.0.1:42904 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-24 14:34:24.726 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: redis-cluster
INFO:app.config_manager:⚙️ Initializing configuration manager for redis-cluster
INFO:app.config_manager:✅ Configuration manager initialized for redis-cluster
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-24 14:34:24.737 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for redis-cluster
INFO:     127.0.0.1:42914 - "GET /api/v1/secrets/redis-cluster HTTP/1.1" 200 OK
INFO:     127.0.0.1:33176 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-24 14:34:33.340 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: kafka-cluster
INFO:app.config_manager:⚙️ Initializing configuration manager for kafka-cluster
INFO:app.config_manager:✅ Configuration manager initialized for kafka-cluster
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-24 14:34:33.349 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for kafka-cluster
INFO:     127.0.0.1:33178 - "GET /api/v1/secrets/kafka-cluster HTTP/1.1" 200 OK
INFO:     127.0.0.1:33184 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54584 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-24 14:34:46.203 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: minio-storage
INFO:app.config_manager:⚙️ Initializing configuration manager for minio-storage
INFO:app.config_manager:✅ Configuration manager initialized for minio-storage
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-24 14:34:46.219 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for minio-storage
INFO:     127.0.0.1:54586 - "GET /api/v1/secrets/minio-storage HTTP/1.1" 200 OK
INFO:     127.0.0.1:36474 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-24 14:34:56.370 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: weaviate-cluster
INFO:app.config_manager:⚙️ Initializing configuration manager for weaviate-cluster
INFO:app.config_manager:✅ Configuration manager initialized for weaviate-cluster
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-24 14:34:56.389 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for weaviate-cluster
INFO:     127.0.0.1:36490 - "GET /api/v1/secrets/weaviate-cluster HTTP/1.1" 200 OK
INFO:     127.0.0.1:45716 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-24 14:35:03.847 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: monitoring-stack
INFO:app.config_manager:⚙️ Initializing configuration manager for monitoring-stack
INFO:app.config_manager:✅ Configuration manager initialized for monitoring-stack
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-24 14:35:03.868 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for monitoring-stack
INFO:     127.0.0.1:45722 - "GET /api/v1/secrets/monitoring-stack HTTP/1.1" 200 OK
INFO:     127.0.0.1:45738 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53568 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-24 14:35:17.178 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: elk-stack
INFO:app.config_manager:⚙️ Initializing configuration manager for elk-stack
INFO:app.config_manager:✅ Configuration manager initialized for elk-stack
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-24 14:35:17.189 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for elk-stack
INFO:     127.0.0.1:53570 - "GET /api/v1/secrets/elk-stack HTTP/1.1" 200 OK
INFO:     127.0.0.1:59426 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-24 14:35:25.791 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: performance-optimizer
INFO:app.config_manager:⚙️ Initializing configuration manager for performance-optimizer
INFO:app.config_manager:✅ Configuration manager initialized for performance-optimizer
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-24 14:35:25.803 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for performance-optimizer
INFO:     127.0.0.1:59438 - "GET /api/v1/secrets/performance-optimizer HTTP/1.1" 200 OK
INFO:     127.0.0.1:58598 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33704 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45200 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39190 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52940 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46234 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34320 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
=== 2025-06-24 15:00:26 - Starting vault-consul ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/vault-consul']
INFO:     Uvicorn running on http://0.0.0.0:7200 (Press CTRL+C to quit)
INFO:     Started reloader process [9445] using WatchFiles
INFO:     Started server process [9448]
INFO:     Waiting for application startup.
INFO:main:🚀 Starting Vault-Consul Service...
INFO:app.vault_client:🔐 Initializing Vault client for vault-consul
INFO:app.vault_client:🔧 Vault URL: http://localhost:8200
INFO:app.vault_client:🔧 Auth method: token
INFO:app.vault_client:🔧 Environment: development
INFO:app.vault_client:🔍 Checking Vault connectivity...
INFO:app.vault_client:✅ Vault server is reachable
INFO:app.vault_client:🔑 Vault is reachable, attempting authentication...
ERROR:app.vault_client:❌ Token validation failed: 503
WARNING:app.vault_client:⚠️ Vault authentication failed - switching to fallback mode
INFO:app.vault_client:🔄 Initializing in fallback mode...
INFO:app.vault_client:💡 Fallback mode uses:
INFO:app.vault_client:   - Environment variables from your system
INFO:app.vault_client:   - Default configuration values
INFO:app.vault_client:   - Local .env file values (if available)
INFO:app.vault_client:💡 To enable Vault integration:
INFO:app.vault_client:   - Ensure Vault server is running and accessible
INFO:app.vault_client:   - Set VAULT_TOKEN environment variable for token auth
INFO:app.vault_client:   - Or deploy in Kubernetes for service account auth
INFO:app.vault_client:   - Check Vault URL configuration
INFO:app.vault_client:🔧 Current Vault URL: http://localhost:8200
INFO:app.vault_client:🔧 Current Auth Method: token
INFO:app.vault_client:🔧 VAULT_TOKEN is set
INFO:main:✅ Vault client initialized successfully
INFO:main:📋 Consul integration ready
INFO:main:🎯 Vault-Consul Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:46276 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46280 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-24 15:00:37.041 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: mongodb-cluster
INFO:app.config_manager:⚙️ Initializing configuration manager for mongodb-cluster
INFO:app.config_manager:✅ Configuration manager initialized for mongodb-cluster
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-24 15:00:37.051 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for mongodb-cluster
INFO:     127.0.0.1:46286 - "GET /api/v1/secrets/mongodb-cluster HTTP/1.1" 200 OK
INFO:     127.0.0.1:53710 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-24 15:00:44.357 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: postgresql-cluster
INFO:app.config_manager:⚙️ Initializing configuration manager for postgresql-cluster
INFO:app.config_manager:✅ Configuration manager initialized for postgresql-cluster
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-24 15:00:44.364 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for postgresql-cluster
INFO:     127.0.0.1:53718 - "GET /api/v1/secrets/postgresql-cluster HTTP/1.1" 200 OK
INFO:     127.0.0.1:55042 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-24 15:00:52.259 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: redis-cluster
INFO:app.config_manager:⚙️ Initializing configuration manager for redis-cluster
INFO:app.config_manager:✅ Configuration manager initialized for redis-cluster
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-24 15:00:52.266 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for redis-cluster
INFO:     127.0.0.1:55058 - "GET /api/v1/secrets/redis-cluster HTTP/1.1" 200 OK
INFO:     127.0.0.1:55062 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-24 15:01:00.189 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: kafka-cluster
INFO:app.config_manager:⚙️ Initializing configuration manager for kafka-cluster
INFO:app.config_manager:✅ Configuration manager initialized for kafka-cluster
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-24 15:01:00.200 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for kafka-cluster
INFO:     127.0.0.1:55072 - "GET /api/v1/secrets/kafka-cluster HTTP/1.1" 200 OK
INFO:     127.0.0.1:50124 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50126 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-24 15:01:08.112 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: minio-storage
INFO:app.config_manager:⚙️ Initializing configuration manager for minio-storage
INFO:app.config_manager:✅ Configuration manager initialized for minio-storage
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-24 15:01:08.119 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for minio-storage
INFO:     127.0.0.1:50136 - "GET /api/v1/secrets/minio-storage HTTP/1.1" 200 OK
INFO:     127.0.0.1:52078 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-24 15:01:16.119 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: weaviate-cluster
INFO:app.config_manager:⚙️ Initializing configuration manager for weaviate-cluster
INFO:app.config_manager:✅ Configuration manager initialized for weaviate-cluster
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-24 15:01:16.127 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for weaviate-cluster
INFO:     127.0.0.1:52080 - "GET /api/v1/secrets/weaviate-cluster HTTP/1.1" 200 OK
INFO:     127.0.0.1:54038 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-24 15:01:23.620 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: monitoring-stack
INFO:app.config_manager:⚙️ Initializing configuration manager for monitoring-stack
INFO:app.config_manager:✅ Configuration manager initialized for monitoring-stack
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-24 15:01:23.627 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for monitoring-stack
INFO:     127.0.0.1:54046 - "GET /api/v1/secrets/monitoring-stack HTTP/1.1" 200 OK
INFO:     127.0.0.1:58684 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-24 15:01:32.482 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: elk-stack
INFO:app.config_manager:⚙️ Initializing configuration manager for elk-stack
INFO:app.config_manager:✅ Configuration manager initialized for elk-stack
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-24 15:01:32.488 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for elk-stack
INFO:     127.0.0.1:58698 - "GET /api/v1/secrets/elk-stack HTTP/1.1" 200 OK
INFO:     127.0.0.1:58712 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58728 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-24 15:01:39.350 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: performance-optimizer
INFO:app.config_manager:⚙️ Initializing configuration manager for performance-optimizer
INFO:app.config_manager:✅ Configuration manager initialized for performance-optimizer
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-24 15:01:39.357 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for performance-optimizer
INFO:     127.0.0.1:58744 - "GET /api/v1/secrets/performance-optimizer HTTP/1.1" 200 OK
INFO:     127.0.0.1:41738 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:41746 - "GET /api/v1/config/user-management HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41762 - "GET /api/v1/secrets/user-management HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:54276 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50954 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57260 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48550 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33278 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46272 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58060 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44870 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:60890 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:43858 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:44774 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:44778 - "GET /api/v1/config/user-management HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44788 - "GET /api/v1/secrets/user-management HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:51550 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37506 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35318 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37998 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42918 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60088 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39662 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54536 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:47412 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-24 15:10:47.205 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: postgresql-cluster
INFO:app.config_manager:⚙️ Initializing configuration manager for postgresql-cluster
INFO:app.config_manager:✅ Configuration manager initialized for postgresql-cluster
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-24 15:10:47.221 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for postgresql-cluster
INFO:     127.0.0.1:47414 - "GET /api/v1/secrets/postgresql-cluster HTTP/1.1" 200 OK
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:60712 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:41784 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:59424 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55114 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33718 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47572 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48522 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35374 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43036 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55864 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-24 15:15:17.096 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: postgresql-cluster
INFO:app.config_manager:⚙️ Initializing configuration manager for postgresql-cluster
INFO:app.config_manager:✅ Configuration manager initialized for postgresql-cluster
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-24 15:15:17.136 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for postgresql-cluster
INFO:     127.0.0.1:55880 - "GET /api/v1/secrets/postgresql-cluster HTTP/1.1" 200 OK
INFO:     127.0.0.1:46880 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:45560 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:47354 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:49142 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50482 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59066 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37104 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50046 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38094 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50036 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:60202 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:60212 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-24 15:20:37.433 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: postgresql-cluster
INFO:app.config_manager:⚙️ Initializing configuration manager for postgresql-cluster
INFO:app.config_manager:✅ Configuration manager initialized for postgresql-cluster
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-24 15:20:37.442 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for postgresql-cluster
INFO:     127.0.0.1:60228 - "GET /api/v1/secrets/postgresql-cluster HTTP/1.1" 200 OK
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:40332 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:51264 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:51192 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48618 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56612 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-24 15:22:43.042 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: postgresql-cluster
INFO:app.config_manager:⚙️ Initializing configuration manager for postgresql-cluster
INFO:app.config_manager:✅ Configuration manager initialized for postgresql-cluster
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-24 15:22:43.060 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for postgresql-cluster
INFO:     127.0.0.1:56618 - "GET /api/v1/secrets/postgresql-cluster HTTP/1.1" 200 OK
INFO:     127.0.0.1:48872 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-24 15:22:59.778 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: postgresql-cluster
INFO:app.config_manager:⚙️ Initializing configuration manager for postgresql-cluster
INFO:app.config_manager:✅ Configuration manager initialized for postgresql-cluster
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-24 15:22:59.808 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for postgresql-cluster
INFO:     127.0.0.1:48884 - "GET /api/v1/secrets/postgresql-cluster HTTP/1.1" 200 OK
INFO:     127.0.0.1:49744 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53672 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38336 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41016 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45364 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:43720 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:56804 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:60746 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:40576 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60578 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:47310 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35930 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57850 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47414 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36836 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:36084 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:43078 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:54206 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:50180 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37046 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:55342 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58272 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57176 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48758 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46736 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:55024 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:52000 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:35436 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:56818 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:56834 - "GET /api/v1/config/user-management HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56836 - "GET /api/v1/secrets/user-management HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:46514 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60850 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:60854 - "GET /api/v1/config/user-management HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60864 - "GET /api/v1/secrets/user-management HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:40938 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:40764 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:40772 - "GET /api/v1/config/user-management HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40784 - "GET /api/v1/secrets/user-management HTTP/1.1" 401 Unauthorized
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:51088 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45340 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60154 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44722 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33956 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:33568 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:50952 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:49072 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:43134 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:43136 - "GET /api/v1/config/user-management HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43150 - "GET /api/v1/secrets/user-management HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:43158 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46356 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:46372 - "GET /api/v1/config/user-management HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46388 - "GET /api/v1/secrets/user-management HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:41094 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:46978 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59110 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45528 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51460 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52578 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:59752 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:59584 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:33120 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:34360 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38956 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:50208 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39594 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35972 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56608 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45068 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:45070 - "GET /api/v1/config/user-management HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45084 - "GET /api/v1/secrets/user-management HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:34012 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:59920 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:38492 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:53590 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53594 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-24 15:51:38.494 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: postgresql-cluster
INFO:app.config_manager:⚙️ Initializing configuration manager for postgresql-cluster
INFO:app.config_manager:✅ Configuration manager initialized for postgresql-cluster
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-24 15:51:38.530 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for postgresql-cluster
INFO:     127.0.0.1:53604 - "GET /api/v1/secrets/postgresql-cluster HTTP/1.1" 200 OK
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:59118 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-24 15:51:48.329 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: postgresql-cluster
INFO:app.config_manager:⚙️ Initializing configuration manager for postgresql-cluster
INFO:app.config_manager:✅ Configuration manager initialized for postgresql-cluster
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-24 15:51:48.342 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for postgresql-cluster
INFO:     127.0.0.1:59122 - "GET /api/v1/secrets/postgresql-cluster HTTP/1.1" 200 OK
INFO:     127.0.0.1:58662 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-24 15:52:00.322 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: postgresql-cluster
INFO:app.config_manager:⚙️ Initializing configuration manager for postgresql-cluster
INFO:app.config_manager:✅ Configuration manager initialized for postgresql-cluster
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-24 15:52:00.332 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for postgresql-cluster
INFO:     127.0.0.1:58674 - "GET /api/v1/secrets/postgresql-cluster HTTP/1.1" 200 OK
INFO:     127.0.0.1:55682 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44036 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-24 15:52:14.881 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: postgresql-cluster
INFO:app.config_manager:⚙️ Initializing configuration manager for postgresql-cluster
INFO:app.config_manager:✅ Configuration manager initialized for postgresql-cluster
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-24 15:52:14.894 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for postgresql-cluster
INFO:     127.0.0.1:44048 - "GET /api/v1/secrets/postgresql-cluster HTTP/1.1" 200 OK
INFO:     127.0.0.1:40752 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41180 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:41188 - "GET /api/v1/config/user-management HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41192 - "GET /api/v1/secrets/user-management HTTP/1.1" 401 Unauthorized
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:46020 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46036 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:46042 - "GET /api/v1/config/user-management HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46054 - "GET /api/v1/secrets/user-management HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:39626 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50914 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45962 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55748 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-24 15:55:01.498 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: postgresql-cluster
INFO:app.config_manager:⚙️ Initializing configuration manager for postgresql-cluster
INFO:app.config_manager:✅ Configuration manager initialized for postgresql-cluster
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-24 15:55:01.522 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for postgresql-cluster
INFO:     127.0.0.1:55754 - "GET /api/v1/secrets/postgresql-cluster HTTP/1.1" 200 OK
INFO:     127.0.0.1:55770 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:36396 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:46534 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:48676 - "GET /api/v1/secrets/health HTTP/1.1" 200 OK
2025-06-24 15:56:14.926 | INFO     | app.api.secrets_api:get_service_secrets:61 - 🔐 Getting secrets for service: postgresql-cluster
INFO:app.config_manager:⚙️ Initializing configuration manager for postgresql-cluster
INFO:app.config_manager:✅ Configuration manager initialized for postgresql-cluster
WARNING:app.config_manager:⚠️ Secret JWT_SECRET_KEY not found in configuration
WARNING:app.config_manager:⚠️ Secret HUGGINGFACE_TOKEN not found in configuration
WARNING:app.config_manager:⚠️ Secret WEAVIATE_API_KEY not found in configuration
2025-06-24 15:56:14.949 | INFO     | app.api.secrets_api:get_service_secrets:123 - ✅ Retrieved 20 config items for postgresql-cluster
INFO:     127.0.0.1:48682 - "GET /api/v1/secrets/postgresql-cluster HTTP/1.1" 200 OK
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:58328 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:33450 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:59268 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:app.config_manager:🔄 Vault configuration refreshed
INFO:     127.0.0.1:60014 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59508 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35178 - "GET /metrics HTTP/1.1" 404 Not Found
