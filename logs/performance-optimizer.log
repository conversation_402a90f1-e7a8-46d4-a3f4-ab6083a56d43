=== 2025-06-24 14:35:20 - Starting performance-optimizer ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/performance-optimizer']
INFO:     Uvicorn running on http://0.0.0.0:7274 (Press CTRL+C to quit)
INFO:     Started reloader process [75124] using WatchFiles
INFO:     Started server process [75146]
INFO:     Waiting for application startup.
2025-06-24 14:35:25.403 | INFO     | main:startup_event:748 - 🚀 Starting Performance Optimizer Service...
2025-06-24 14:35:25.405 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for performance-optimizer
2025-06-24 14:35:25.407 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for performance-optimizer
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-24 14:35:25.745 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for performance-optimizer
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/performance-optimizer "HTTP/1.1 200 OK"
2025-06-24 14:35:25.807 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for performance-optimizer
2025-06-24 14:35:25.809 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for performance-optimizer
2025-06-24 14:35:25.810 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for performance-optimizer
2025-06-24 14:35:25.811 | INFO     | main:startup_event:756 - 🎯 Performance Optimizer Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:37340 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39470 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60372 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55150 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59752 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38130 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55990 - "GET /metrics HTTP/1.1" 404 Not Found
=== 2025-06-24 15:01:36 - Starting performance-optimizer ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/performance-optimizer']
INFO:     Uvicorn running on http://0.0.0.0:7274 (Press CTRL+C to quit)
INFO:     Started reloader process [9873] using WatchFiles
INFO:     Started server process [9876]
INFO:     Waiting for application startup.
2025-06-24 15:01:39.184 | INFO     | main:startup_event:748 - 🚀 Starting Performance Optimizer Service...
2025-06-24 15:01:39.185 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for performance-optimizer
2025-06-24 15:01:39.185 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for performance-optimizer
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-24 15:01:39.320 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for performance-optimizer
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/performance-optimizer "HTTP/1.1 200 OK"
2025-06-24 15:01:39.360 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for performance-optimizer
2025-06-24 15:01:39.361 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for performance-optimizer
2025-06-24 15:01:39.361 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for performance-optimizer
2025-06-24 15:01:39.362 | INFO     | main:startup_event:756 - 🎯 Performance Optimizer Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:36898 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56554 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38504 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35274 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38452 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46340 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60346 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56492 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39434 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55478 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54776 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48580 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40490 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33716 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45492 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55748 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35780 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52086 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59564 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38924 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60360 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56716 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37992 - "GET /metrics HTTP/1.1" 404 Not Found
