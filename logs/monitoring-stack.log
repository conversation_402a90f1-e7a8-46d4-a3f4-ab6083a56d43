=== 2025-06-25 04:05:32 - Starting monitoring-stack ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/monitoring-stack']
INFO:     Uvicorn running on http://0.0.0.0:7290 (Press CTRL+C to quit)
INFO:     Started reloader process [11455] using WatchFiles
/home/<USER>/Documents/simba-micro-services/services/shared-services/monitoring-stack/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:128: UserWarning: Field "model_name" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
INFO:     Started server process [11459]
INFO:     Waiting for application startup.
2025-06-25 04:05:37.573 | INFO     | main:startup_event:1509 - 🚀 Starting Monitoring Stack Service...
2025-06-25 04:05:37.574 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for monitoring-stack
2025-06-25 04:05:37.575 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for monitoring-stack
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-25 04:05:37.727 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for monitoring-stack
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/monitoring-stack "HTTP/1.1 200 OK"
2025-06-25 04:05:37.843 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for monitoring-stack
2025-06-25 04:05:37.845 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for monitoring-stack
2025-06-25 04:05:37.847 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for monitoring-stack
2025-06-25 04:05:37.848 | INFO     | main:startup_event:1517 - 📊 Prometheus URL: http://localhost:9090
2025-06-25 04:05:37.849 | INFO     | main:startup_event:1518 - 📈 Grafana URL: http://localhost:3000
2025-06-25 04:05:37.850 | INFO     | main:startup_event:1522 - 🔧 Auto-configuring Grafana...
2025-06-25 04:05:37.850 | INFO     | main:setup_grafana_datasource:119 - 🔗 Setting up Grafana data source...
INFO:httpx:HTTP Request: GET http://localhost:3000/api/datasources/name/Prometheus "HTTP/1.1 200 OK"
2025-06-25 04:05:38.102 | INFO     | main:setup_grafana_datasource:133 - ✅ Prometheus data source already exists
2025-06-25 04:05:38.103 | INFO     | main:create_simbaai_dashboard:535 - 📊 Creating SimbaAI overview dashboard...
INFO:httpx:HTTP Request: POST http://localhost:3000/api/dashboards/db "HTTP/1.1 200 OK"
2025-06-25 04:05:38.285 | INFO     | main:create_simbaai_dashboard:639 - ✅ SimbaAI dashboard created successfully
2025-06-25 04:05:38.286 | INFO     | main:create_simbaai_dashboard:640 - 🔗 Dashboard URL: http://localhost:3000/d/b4677663-23d4-443d-b843-c4db0cff46d6/simbaai-llm-ai-system-overview
2025-06-25 04:05:38.287 | INFO     | main:startup_event:1525 - ✅ Grafana auto-configuration completed
2025-06-25 04:05:38.288 | INFO     | main:startup_event:1532 - 🔧 Auto-generating Prometheus configurations...
2025-06-25 04:05:38.289 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-25 04:05:38.477 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for local
2025-06-25 04:05:38.479 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for local with 41 services
2025-06-25 04:05:38.481 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for local
2025-06-25 04:05:38.482 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-25 04:05:38.672 | INFO     | main:create_docker_prometheus_override:482 - ✅ Created Docker Compose override for Prometheus
2025-06-25 04:05:38.673 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for docker
2025-06-25 04:05:38.674 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for docker with 41 services
2025-06-25 04:05:38.675 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for docker
2025-06-25 04:05:38.676 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-25 04:05:38.937 | INFO     | main:create_kubernetes_configmap:527 - ✅ Created Kubernetes ConfigMap for Prometheus
2025-06-25 04:05:38.937 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for kubernetes
2025-06-25 04:05:38.938 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for kubernetes with 41 services
2025-06-25 04:05:38.938 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for kubernetes
2025-06-25 04:05:38.938 | INFO     | main:startup_event:1543 - ✅ Prometheus configurations auto-generated
2025-06-25 04:05:38.939 | INFO     | main:startup_event:1544 - 💡 Use /api/v1/setup/prometheus?deployment_type=docker to apply Docker configuration
2025-06-25 04:05:38.939 | INFO     | main:startup_event:1550 - 🎯 Monitoring Stack Service ready to serve microservices
INFO:     Application startup complete.
