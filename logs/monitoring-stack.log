=== 2025-06-24 14:34:59 - Starting monitoring-stack ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/monitoring-stack']
INFO:     Uvicorn running on http://0.0.0.0:7290 (Press CTRL+C to quit)
INFO:     Started reloader process [75055] using WatchFiles
/home/<USER>/Documents/simba-micro-services/services/shared-services/monitoring-stack/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:128: UserWarning: Field "model_name" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
INFO:     Started server process [75059]
INFO:     Waiting for application startup.
2025-06-24 14:35:03.693 | INFO     | main:startup_event:1509 - 🚀 Starting Monitoring Stack Service...
2025-06-24 14:35:03.694 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for monitoring-stack
2025-06-24 14:35:03.695 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for monitoring-stack
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-24 14:35:03.783 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for monitoring-stack
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/monitoring-stack "HTTP/1.1 200 OK"
2025-06-24 14:35:03.873 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for monitoring-stack
2025-06-24 14:35:03.875 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for monitoring-stack
2025-06-24 14:35:03.875 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for monitoring-stack
2025-06-24 14:35:03.876 | INFO     | main:startup_event:1517 - 📊 Prometheus URL: http://localhost:9090
2025-06-24 14:35:03.876 | INFO     | main:startup_event:1518 - 📈 Grafana URL: http://localhost:3000
2025-06-24 14:35:03.877 | INFO     | main:startup_event:1522 - 🔧 Auto-configuring Grafana...
2025-06-24 14:35:03.878 | INFO     | main:setup_grafana_datasource:119 - 🔗 Setting up Grafana data source...
INFO:httpx:HTTP Request: GET http://localhost:3000/api/datasources/name/Prometheus "HTTP/1.1 200 OK"
2025-06-24 14:35:04.221 | INFO     | main:setup_grafana_datasource:133 - ✅ Prometheus data source already exists
2025-06-24 14:35:04.222 | INFO     | main:create_simbaai_dashboard:535 - 📊 Creating SimbaAI overview dashboard...
INFO:httpx:HTTP Request: POST http://localhost:3000/api/dashboards/db "HTTP/1.1 200 OK"
2025-06-24 14:35:04.382 | INFO     | main:create_simbaai_dashboard:639 - ✅ SimbaAI dashboard created successfully
2025-06-24 14:35:04.383 | INFO     | main:create_simbaai_dashboard:640 - 🔗 Dashboard URL: http://localhost:3000/d/b4677663-23d4-443d-b843-c4db0cff46d6/simbaai-llm-ai-system-overview
2025-06-24 14:35:04.384 | INFO     | main:startup_event:1525 - ✅ Grafana auto-configuration completed
2025-06-24 14:35:04.384 | INFO     | main:startup_event:1532 - 🔧 Auto-generating Prometheus configurations...
2025-06-24 14:35:04.385 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-24 14:35:04.481 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for local
2025-06-24 14:35:04.482 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for local with 41 services
2025-06-24 14:35:04.482 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for local
2025-06-24 14:35:04.483 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-24 14:35:04.583 | INFO     | main:create_docker_prometheus_override:482 - ✅ Created Docker Compose override for Prometheus
2025-06-24 14:35:04.583 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for docker
2025-06-24 14:35:04.584 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for docker with 41 services
2025-06-24 14:35:04.584 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for docker
2025-06-24 14:35:04.585 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-24 14:35:04.864 | INFO     | main:create_kubernetes_configmap:527 - ✅ Created Kubernetes ConfigMap for Prometheus
2025-06-24 14:35:04.865 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for kubernetes
2025-06-24 14:35:04.866 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for kubernetes with 41 services
2025-06-24 14:35:04.867 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for kubernetes
2025-06-24 14:35:04.868 | INFO     | main:startup_event:1543 - ✅ Prometheus configurations auto-generated
2025-06-24 14:35:04.869 | INFO     | main:startup_event:1544 - 💡 Use /api/v1/setup/prometheus?deployment_type=docker to apply Docker configuration
2025-06-24 14:35:04.870 | INFO     | main:startup_event:1550 - 🎯 Monitoring Stack Service ready to serve microservices
INFO:     Application startup complete.
=== 2025-06-24 15:01:20 - Starting monitoring-stack ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/monitoring-stack']
INFO:     Uvicorn running on http://0.0.0.0:7290 (Press CTRL+C to quit)
INFO:     Started reloader process [9814] using WatchFiles
/home/<USER>/Documents/simba-micro-services/services/shared-services/monitoring-stack/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:128: UserWarning: Field "model_name" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
INFO:     Started server process [9817]
INFO:     Waiting for application startup.
2025-06-24 15:01:23.537 | INFO     | main:startup_event:1509 - 🚀 Starting Monitoring Stack Service...
2025-06-24 15:01:23.538 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for monitoring-stack
2025-06-24 15:01:23.538 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for monitoring-stack
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-24 15:01:23.592 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for monitoring-stack
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/monitoring-stack "HTTP/1.1 200 OK"
2025-06-24 15:01:23.630 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for monitoring-stack
2025-06-24 15:01:23.631 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for monitoring-stack
2025-06-24 15:01:23.631 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for monitoring-stack
2025-06-24 15:01:23.632 | INFO     | main:startup_event:1517 - 📊 Prometheus URL: http://localhost:9090
2025-06-24 15:01:23.632 | INFO     | main:startup_event:1518 - 📈 Grafana URL: http://localhost:3000
2025-06-24 15:01:23.632 | INFO     | main:startup_event:1522 - 🔧 Auto-configuring Grafana...
2025-06-24 15:01:23.633 | INFO     | main:setup_grafana_datasource:119 - 🔗 Setting up Grafana data source...
INFO:httpx:HTTP Request: GET http://localhost:3000/api/datasources/name/Prometheus "HTTP/1.1 200 OK"
2025-06-24 15:01:23.726 | INFO     | main:setup_grafana_datasource:133 - ✅ Prometheus data source already exists
2025-06-24 15:01:23.726 | INFO     | main:create_simbaai_dashboard:535 - 📊 Creating SimbaAI overview dashboard...
INFO:httpx:HTTP Request: POST http://localhost:3000/api/dashboards/db "HTTP/1.1 200 OK"
2025-06-24 15:01:23.815 | INFO     | main:create_simbaai_dashboard:639 - ✅ SimbaAI dashboard created successfully
2025-06-24 15:01:23.816 | INFO     | main:create_simbaai_dashboard:640 - 🔗 Dashboard URL: http://localhost:3000/d/b4677663-23d4-443d-b843-c4db0cff46d6/simbaai-llm-ai-system-overview
2025-06-24 15:01:23.816 | INFO     | main:startup_event:1525 - ✅ Grafana auto-configuration completed
2025-06-24 15:01:23.816 | INFO     | main:startup_event:1532 - 🔧 Auto-generating Prometheus configurations...
2025-06-24 15:01:23.817 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-24 15:01:23.882 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for local
2025-06-24 15:01:23.882 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for local with 41 services
2025-06-24 15:01:23.882 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for local
2025-06-24 15:01:23.883 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-24 15:01:23.942 | INFO     | main:create_docker_prometheus_override:482 - ✅ Created Docker Compose override for Prometheus
2025-06-24 15:01:23.942 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for docker
2025-06-24 15:01:23.942 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for docker with 41 services
2025-06-24 15:01:23.943 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for docker
2025-06-24 15:01:23.943 | INFO     | main:generate_prometheus_config:172 - 🔧 Generating Prometheus configuration...
2025-06-24 15:01:24.064 | INFO     | main:create_kubernetes_configmap:527 - ✅ Created Kubernetes ConfigMap for Prometheus
2025-06-24 15:01:24.064 | INFO     | main:save_prometheus_configs:452 - ✅ Saved Prometheus configurations for kubernetes
2025-06-24 15:01:24.064 | INFO     | main:generate_prometheus_config:241 - ✅ Generated Prometheus configuration for kubernetes with 41 services
2025-06-24 15:01:24.065 | INFO     | main:startup_event:1539 - ✅ Generated Prometheus config for kubernetes
2025-06-24 15:01:24.065 | INFO     | main:startup_event:1543 - ✅ Prometheus configurations auto-generated
2025-06-24 15:01:24.065 | INFO     | main:startup_event:1544 - 💡 Use /api/v1/setup/prometheus?deployment_type=docker to apply Docker configuration
2025-06-24 15:01:24.066 | INFO     | main:startup_event:1550 - 🎯 Monitoring Stack Service ready to serve microservices
INFO:     Application startup complete.
