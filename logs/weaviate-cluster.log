=== 2025-06-24 14:34:49 - Starting weaviate-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/weaviate-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7281 (Press CTRL+C to quit)
INFO:     Started reloader process [74930] using WatchFiles
INFO:     Started server process [74933]
INFO:     Waiting for application startup.
2025-06-24 14:34:56.084 | INFO     | main:lifespan:39 - 🚀 Starting Weaviate Cluster Service...
2025-06-24 14:34:56.085 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for weaviate-cluster
2025-06-24 14:34:56.086 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for weaviate-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-24 14:34:56.320 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for weaviate-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/weaviate-cluster "HTTP/1.1 200 OK"
2025-06-24 14:34:56.395 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for weaviate-cluster
2025-06-24 14:34:56.396 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for weaviate-cluster
2025-06-24 14:34:56.396 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for weaviate-cluster
/home/<USER>/Documents/simba-micro-services/services/shared-services/weaviate-cluster/.venv/lib/python3.11/site-packages/weaviate/warnings.py:121: DeprecationWarning: Dep005: You are using weaviate-client version 3.25.3. The latest version is 4.15.3.
            Please consider upgrading to the latest version. See https://weaviate.io/developers/weaviate/client-libraries/python for details.
  warnings.warn(
2025-06-24 14:34:57.149 | ERROR    | main:create_schema:281 - ❌ Weaviate error creating schema Document: Create class! Unexpected status code: 422, with response body: {'error': [{'message': "Property 'metadata': At least one nested property is required for data type object/object[]"}]}.
2025-06-24 14:34:57.161 | INFO     | main:create_schema:251 - ✅ Schema class ChatMessage already exists
2025-06-24 14:34:57.162 | INFO     | main:create_default_schemas:112 - ✅ Default schemas created/verified
2025-06-24 14:34:57.162 | INFO     | main:lifespan:59 - ✅ Weaviate client initialized successfully
2025-06-24 14:34:57.163 | INFO     | main:lifespan:60 - 🎯 Weaviate Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:39754 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33504 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38396 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54600 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47292 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56592 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58726 - "GET /metrics HTTP/1.1" 404 Not Found
=== 2025-06-24 15:01:12 - Starting weaviate-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/weaviate-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7281 (Press CTRL+C to quit)
INFO:     Started reloader process [9732] using WatchFiles
INFO:     Started server process [9735]
INFO:     Waiting for application startup.
2025-06-24 15:01:15.948 | INFO     | main:lifespan:39 - 🚀 Starting Weaviate Cluster Service...
2025-06-24 15:01:15.949 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for weaviate-cluster
2025-06-24 15:01:15.950 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for weaviate-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-24 15:01:16.090 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for weaviate-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/weaviate-cluster "HTTP/1.1 200 OK"
2025-06-24 15:01:16.130 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for weaviate-cluster
2025-06-24 15:01:16.130 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for weaviate-cluster
2025-06-24 15:01:16.131 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for weaviate-cluster
/home/<USER>/Documents/simba-micro-services/services/shared-services/weaviate-cluster/.venv/lib/python3.11/site-packages/weaviate/warnings.py:121: DeprecationWarning: Dep005: You are using weaviate-client version 3.25.3. The latest version is 4.15.3.
            Please consider upgrading to the latest version. See https://weaviate.io/developers/weaviate/client-libraries/python for details.
  warnings.warn(
2025-06-24 15:01:16.487 | ERROR    | main:create_schema:281 - ❌ Weaviate error creating schema Document: Create class! Unexpected status code: 422, with response body: {'error': [{'message': "Property 'metadata': At least one nested property is required for data type object/object[]"}]}.
2025-06-24 15:01:16.495 | INFO     | main:create_schema:251 - ✅ Schema class ChatMessage already exists
2025-06-24 15:01:16.496 | INFO     | main:create_default_schemas:112 - ✅ Default schemas created/verified
2025-06-24 15:01:16.496 | INFO     | main:lifespan:59 - ✅ Weaviate client initialized successfully
2025-06-24 15:01:16.497 | INFO     | main:lifespan:60 - 🎯 Weaviate Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:44256 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58196 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38140 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51682 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52604 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42078 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43758 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38856 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36538 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52434 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60692 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33192 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38168 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44956 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51716 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36562 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36970 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34512 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43858 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53706 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59278 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47574 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41086 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50066 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35304 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35038 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51110 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40206 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52940 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40774 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39014 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50284 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46678 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60248 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48360 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50028 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55788 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52774 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49936 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35428 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46424 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41640 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51108 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40908 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40598 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48084 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40364 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47170 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53272 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35762 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41456 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46234 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50702 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49426 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35514 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48420 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38862 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48652 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47762 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45860 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57780 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54534 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57302 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:32970 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50952 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48610 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43482 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35864 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52408 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46042 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35868 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56216 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46030 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51120 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55184 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41782 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41190 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44986 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51822 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48244 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60066 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42240 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33324 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42138 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41318 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58422 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54938 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40046 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50590 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55952 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50812 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41902 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50284 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49248 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35716 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37718 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57424 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48742 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43266 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54466 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52544 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40720 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49004 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34582 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45764 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45446 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59630 - "GET /metrics HTTP/1.1" 404 Not Found
