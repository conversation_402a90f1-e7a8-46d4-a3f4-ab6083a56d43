=== 2025-06-25 04:09:45 - Starting billing ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/billing']
INFO:     Uvicorn running on http://0.0.0.0:7309 (Press CTRL+C to quit)
INFO:     Started reloader process [13899] using WatchFiles
Process SpawnProcess-1:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 13, in <module>
    from .routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/billing.py", line 58, in <module>
    @rate_limit_payment
     ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/security/middleware.py", line 250, in rate_limit_payment
    return limiter.limit(SECURITY_CONFIG["rate_limits"]["payment"])(func)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/slowapi/extension.py", line 713, in decorator
    raise Exception(
Exception: No "request" or "websocket" argument on function "<function require_permission.<locals>.decorator.<locals>.wrapper at 0x7fee0814a340>"
WARNING:  WatchFiles detected changes in 'app/security/auth.py'. Reloading...
Process SpawnProcess-2:
Traceback (most recent call last):
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 190, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.pyenv/versions/3.11.13/lib/python3.11/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/main.py", line 22, in <module>
    from app.api.routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/__init__.py", line 13, in <module>
    from .routes.billing import router as billing_router
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/api/routes/billing.py", line 58, in <module>
    @rate_limit_payment
     ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/app/security/middleware.py", line 250, in rate_limit_payment
    return limiter.limit(SECURITY_CONFIG["rate_limits"]["payment"])(func)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/billing/.venv/lib/python3.11/site-packages/slowapi/extension.py", line 713, in decorator
    raise Exception(
Exception: No "request" or "websocket" argument on function "<function require_permission.<locals>.decorator.<locals>.wrapper at 0x7fc033352340>"
