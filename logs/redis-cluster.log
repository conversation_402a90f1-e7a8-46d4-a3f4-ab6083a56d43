=== 2025-06-24 14:34:20 - Starting redis-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/redis-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7280 (Press CTRL+C to quit)
INFO:     Started reloader process [74778] using WatchFiles
INFO:     Started server process [74782]
INFO:     Waiting for application startup.
2025-06-24 14:34:24.455 | INFO     | main:startup_event:554 - 🚀 Starting Redis Cluster Service...
2025-06-24 14:34:24.456 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for redis-cluster
2025-06-24 14:34:24.456 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for redis-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-24 14:34:24.679 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for redis-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/redis-cluster "HTTP/1.1 200 OK"
2025-06-24 14:34:24.741 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for redis-cluster
2025-06-24 14:34:24.743 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for redis-cluster
2025-06-24 14:34:24.744 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for redis-cluster
2025-06-24 14:34:24.765 | INFO     | main:startup_event:580 - ✅ Redis client initialized successfully
2025-06-24 14:34:24.766 | INFO     | main:startup_event:581 - 🎯 Redis Cluster Service ready to serve microservices
INFO:     Application startup complete.
=== 2025-06-24 15:00:49 - Starting redis-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/redis-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7280 (Press CTRL+C to quit)
INFO:     Started reloader process [9605] using WatchFiles
INFO:     Started server process [9608]
INFO:     Waiting for application startup.
2025-06-24 15:00:52.098 | INFO     | main:startup_event:554 - 🚀 Starting Redis Cluster Service...
2025-06-24 15:00:52.098 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for redis-cluster
2025-06-24 15:00:52.099 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for redis-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-24 15:00:52.230 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for redis-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/redis-cluster "HTTP/1.1 200 OK"
2025-06-24 15:00:52.269 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for redis-cluster
2025-06-24 15:00:52.270 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for redis-cluster
2025-06-24 15:00:52.270 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for redis-cluster
2025-06-24 15:00:52.279 | ERROR    | main:startup_event:584 - ❌ Failed to initialize Redis Cluster Service: Error 111 connecting to localhost:6379. Connection refused.
INFO:     Application startup complete.
