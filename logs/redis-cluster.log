=== 2025-06-25 04:04:49 - Starting redis-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/redis-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7280 (Press CTRL+C to quit)
INFO:     Started reloader process [10905] using WatchFiles
INFO:     Started server process [10918]
INFO:     Waiting for application startup.
2025-06-25 04:04:59.099 | INFO     | main:startup_event:554 - 🚀 Starting Redis Cluster Service...
2025-06-25 04:04:59.100 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for redis-cluster
2025-06-25 04:04:59.103 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for redis-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-25 04:04:59.705 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for redis-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/redis-cluster "HTTP/1.1 200 OK"
2025-06-25 04:04:59.876 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for redis-cluster
2025-06-25 04:04:59.880 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for redis-cluster
2025-06-25 04:04:59.887 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for redis-cluster
2025-06-25 04:04:59.936 | INFO     | main:startup_event:580 - ✅ Redis client initialized successfully
2025-06-25 04:04:59.937 | INFO     | main:startup_event:581 - 🎯 Redis Cluster Service ready to serve microservices
INFO:     Application startup complete.
