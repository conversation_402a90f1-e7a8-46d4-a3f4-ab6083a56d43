=== 2025-06-24 14:33:40 - Starting service-registry ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/service-registry']
INFO:     Uvicorn running on http://0.0.0.0:7010 (Press CTRL+C to quit)
INFO:     Started reloader process [74448] using WatchFiles
INFO:     Started server process [74469]
INFO:     Waiting for application startup.
INFO:main:🚀 Starting Service Registry...
2025-06-24 14:33:43.978 | INFO     | app.service_registry:start:61 - 🚀 Service Registry started
INFO:main:🎯 Service Registry ready to serve microservices
INFO:     Application startup complete.
=== 2025-06-24 15:00:17 - Starting service-registry ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/service-registry']
INFO:     Uvicorn running on http://0.0.0.0:7010 (Press CTRL+C to quit)
INFO:     Started reloader process [9393] using WatchFiles
INFO:     Started server process [9396]
INFO:     Waiting for application startup.
INFO:main:🚀 Starting Service Registry...
2025-06-24 15:00:20.534 | INFO     | app.service_registry:start:61 - 🚀 Service Registry started
INFO:main:🎯 Service Registry ready to serve microservices
INFO:     Application startup complete.
2025-06-24 15:01:59.935 | INFO     | app.service_registry:register_service:113 - ✅ Registered service: user-management at http://user-management:7301:7301 (status: unhealthy)
INFO:     127.0.0.1:55968 - "POST /api/v1/registry/register HTTP/1.1" 200 OK
2025-06-24 15:02:20.536 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:02:54.435 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:03:28.436 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:04:02.438 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:04:36.186 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:05:10.186 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:05:44.190 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:06:18.186 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:06:49.935 | INFO     | app.service_registry:register_service:113 - ✅ Registered service: user-management at http://user-management:7301:7301 (status: unhealthy)
INFO:     127.0.0.1:50280 - "POST /api/v1/registry/register HTTP/1.1" 200 OK
2025-06-24 15:06:51.934 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:07:25.438 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:07:59.186 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:08:33.058 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:09:06.687 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:09:40.437 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:10:14.187 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:10:48.181 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:11:21.935 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:11:55.936 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:12:29.687 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:13:03.685 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:13:37.686 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:14:11.685 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:14:45.339 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:15:19.337 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:15:52.938 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:16:26.436 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:17:00.186 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:17:33.880 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:18:07.692 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:18:41.186 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:19:14.937 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:19:48.937 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:20:22.687 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:20:56.430 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:21:30.437 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:22:04.185 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:22:38.186 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:23:11.691 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:23:45.275 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:24:18.824 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:24:52.469 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:25:26.437 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:26:00.186 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:26:33.981 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:27:07.572 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:27:41.186 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:28:14.936 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:28:48.936 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:29:22.587 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:29:56.436 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:30:30.437 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:31:04.120 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:31:38.074 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:32:12.186 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:32:46.150 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:33:19.973 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:33:53.936 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:34:27.686 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:35:01.686 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:35:35.686 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:36:09.207 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:36:42.936 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:36:59.936 | INFO     | app.service_registry:register_service:113 - ✅ Registered service: user-management at http://user-management:7301:7301 (status: unhealthy)
INFO:     127.0.0.1:38530 - "POST /api/v1/registry/register HTTP/1.1" 200 OK
2025-06-24 15:37:16.492 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:37:28.686 | INFO     | app.service_registry:register_service:113 - ✅ Registered service: user-management at http://user-management:7301:7301 (status: unhealthy)
INFO:     127.0.0.1:57092 - "POST /api/v1/registry/register HTTP/1.1" 200 OK
2025-06-24 15:37:49.795 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:37:52.188 | INFO     | app.service_registry:register_service:113 - ✅ Registered service: user-management at http://user-management:7301:7301 (status: unhealthy)
INFO:     127.0.0.1:49258 - "POST /api/v1/registry/register HTTP/1.1" 200 OK
2025-06-24 15:38:22.199 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:38:55.725 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:39:29.247 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:40:03.186 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:40:36.748 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:41:10.437 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:41:43.992 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:42:07.687 | INFO     | app.service_registry:register_service:113 - ✅ Registered service: user-management at http://user-management:7301:7301 (status: unhealthy)
INFO:     127.0.0.1:52920 - "POST /api/v1/registry/register HTTP/1.1" 200 OK
2025-06-24 15:42:17.440 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:42:20.485 | INFO     | app.service_registry:register_service:113 - ✅ Registered service: user-management at http://user-management:7301:7301 (status: unhealthy)
INFO:     127.0.0.1:52432 - "POST /api/v1/registry/register HTTP/1.1" 200 OK
2025-06-24 15:42:50.489 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:43:24.435 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:43:58.038 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:44:31.685 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:45:05.685 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:45:39.436 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:46:13.185 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:46:46.936 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:47:20.485 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:47:54.436 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:48:28.436 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:49:02.186 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:49:35.687 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:49:57.935 | INFO     | app.service_registry:register_service:113 - ✅ Registered service: user-management at http://user-management:7301:7301 (status: unhealthy)
INFO:     127.0.0.1:55534 - "POST /api/v1/registry/register HTTP/1.1" 200 OK
2025-06-24 15:50:09.436 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
2025-06-24 15:50:43.049 | DEBUG    | app.service_registry:_perform_health_checks:197 - 🔍 Performing health checks on 1 services
