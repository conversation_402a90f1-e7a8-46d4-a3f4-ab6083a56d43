=== 2025-06-24 14:35:10 - Starting elk-stack ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/elk-stack']
INFO:     Uvicorn running on http://0.0.0.0:7291 (Press CTRL+C to quit)
INFO:     Started reloader process [75092] using WatchFiles
INFO:     Started server process [75095]
INFO:     Waiting for application startup.
2025-06-24 14:35:16.912 | INFO     | main:lifespan:63 - 🚀 Starting ELK Stack Service...
2025-06-24 14:35:16.913 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for elk-stack
2025-06-24 14:35:16.913 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for elk-stack
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-24 14:35:17.131 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for elk-stack
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/elk-stack "HTTP/1.1 200 OK"
2025-06-24 14:35:17.194 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for elk-stack
2025-06-24 14:35:17.196 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for elk-stack
2025-06-24 14:35:17.197 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for elk-stack
INFO:elastic_transport.transport:HEAD http://localhost:9200/ [status:200 duration:0.031s]
INFO:elastic_transport.transport:PUT http://localhost:9200/_index_template/simbaai-logs-template [status:200 duration:0.030s]
2025-06-24 14:35:17.262 | INFO     | main:create_default_templates:811 - ✅ Default index templates created/verified
2025-06-24 14:35:17.263 | INFO     | main:lifespan:83 - ✅ Elasticsearch client initialized successfully
2025-06-24 14:35:17.264 | INFO     | main:lifespan:84 - 🎯 ELK Stack Service ready to serve microservices
INFO:     Application startup complete.
=== 2025-06-24 15:01:28 - Starting elk-stack ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/elk-stack']
INFO:     Uvicorn running on http://0.0.0.0:7291 (Press CTRL+C to quit)
INFO:     Started reloader process [9841] using WatchFiles
INFO:     Started server process [9848]
INFO:     Waiting for application startup.
2025-06-24 15:01:32.340 | INFO     | main:lifespan:63 - 🚀 Starting ELK Stack Service...
2025-06-24 15:01:32.340 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for elk-stack
2025-06-24 15:01:32.341 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for elk-stack
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-24 15:01:32.455 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for elk-stack
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/elk-stack "HTTP/1.1 200 OK"
2025-06-24 15:01:32.491 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for elk-stack
2025-06-24 15:01:32.492 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for elk-stack
2025-06-24 15:01:32.492 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for elk-stack
INFO:elastic_transport.transport:HEAD http://localhost:9200/ [status:200 duration:0.007s]
INFO:elastic_transport.transport:PUT http://localhost:9200/_index_template/simbaai-logs-template [status:200 duration:0.008s]
2025-06-24 15:01:32.510 | INFO     | main:create_default_templates:811 - ✅ Default index templates created/verified
2025-06-24 15:01:32.511 | INFO     | main:lifespan:83 - ✅ Elasticsearch client initialized successfully
2025-06-24 15:01:32.511 | INFO     | main:lifespan:84 - 🎯 ELK Stack Service ready to serve microservices
INFO:     Application startup complete.
