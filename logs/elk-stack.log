=== 2025-06-25 04:05:42 - Starting elk-stack ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/elk-stack']
INFO:     Uvicorn running on http://0.0.0.0:7291 (Press CTRL+C to quit)
INFO:     Started reloader process [11517] using WatchFiles
INFO:     Started server process [11520]
INFO:     Waiting for application startup.
2025-06-25 04:05:49.916 | INFO     | main:lifespan:63 - 🚀 Starting ELK Stack Service...
2025-06-25 04:05:49.916 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for elk-stack
2025-06-25 04:05:49.917 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for elk-stack
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-25 04:05:50.128 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for elk-stack
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/elk-stack "HTTP/1.1 200 OK"
2025-06-25 04:05:50.195 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for elk-stack
2025-06-25 04:05:50.196 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for elk-stack
2025-06-25 04:05:50.196 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for elk-stack
INFO:elastic_transport.transport:HEAD http://localhost:9200/ [status:200 duration:0.364s]
INFO:elastic_transport.transport:PUT http://localhost:9200/_index_template/simbaai-logs-template [status:200 duration:0.058s]
2025-06-25 04:05:50.623 | INFO     | main:create_default_templates:811 - ✅ Default index templates created/verified
2025-06-25 04:05:50.623 | INFO     | main:lifespan:83 - ✅ Elasticsearch client initialized successfully
2025-06-25 04:05:50.624 | INFO     | main:lifespan:84 - 🎯 ELK Stack Service ready to serve microservices
INFO:     Application startup complete.
