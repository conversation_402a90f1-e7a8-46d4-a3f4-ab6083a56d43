=== 2025-06-24 16:15:57 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [41417] using WatchFiles
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [41420]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 16:16:05.449 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 16:16:05.450 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/create "HTTP/1.1 200 OK"
INFO:app.api.auth_endpoints:User <EMAIL> registered successfully without email verification
INFO:     127.0.0.1:39260 - "POST /api/v1/auth/register HTTP/1.1" 400 Bad Request
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/create "HTTP/1.1 200 OK"
INFO:app.api.auth_endpoints:User <EMAIL> registered successfully without email verification
INFO:     127.0.0.1:36700 - "POST /api/v1/auth/register HTTP/1.1" 400 Bad Request
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/create "HTTP/1.1 200 OK"
INFO:app.api.auth_endpoints:User <EMAIL> registered successfully without email verification
INFO:     127.0.0.1:36392 - "POST /api/v1/auth/register HTTP/1.1" 400 Bad Request
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [41420]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [45335]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 16:20:29.442 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 16:20:29.443 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/create "HTTP/1.1 500 Internal Server Error"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 500 - invalid input value for enum userstatus: "active"
INFO:     127.0.0.1:50002 - "POST /api/v1/auth/register HTTP/1.1" 500 Internal Server Error
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [45335]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [46761]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 16:23:58.405 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 16:23:58.407 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/create "HTTP/1.1 500 Internal Server Error"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 500 - invalid input value for enum userstatus: "active"
INFO:     127.0.0.1:40618 - "POST /api/v1/auth/register HTTP/1.1" 500 Internal Server Error
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [46761]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [49325]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 16:25:27.454 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 16:25:27.456 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [49325]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [49570]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 16:26:46.445 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 16:26:46.446 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/create "HTTP/1.1 500 Internal Server Error"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 500 - invalid input value for enum userstatus: "active"
INFO:     127.0.0.1:58504 - "POST /api/v1/auth/register HTTP/1.1" 500 Internal Server Error
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [49570]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [50065]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 16:27:32.324 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 16:27:32.325 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/create "HTTP/1.1 500 Internal Server Error"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 500 - invalid input value for enum userstatus: "active"
INFO:     127.0.0.1:60900 - "POST /api/v1/auth/register HTTP/1.1" 500 Internal Server Error
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/create "HTTP/1.1 500 Internal Server Error"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 500 - invalid input value for enum userstatus: "active"
INFO:     127.0.0.1:60278 - "POST /api/v1/auth/register HTTP/1.1" 500 Internal Server Error
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/create "HTTP/1.1 500 Internal Server Error"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 500 - invalid input value for enum userstatus: "active"
INFO:     127.0.0.1:44012 - "POST /api/v1/auth/register HTTP/1.1" 500 Internal Server Error
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [50065]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [55316]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 16:33:24.691 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 16:33:24.692 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [55316]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [55384]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 16:33:42.236 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 16:33:42.237 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/create "HTTP/1.1 200 OK"
INFO:app.api.auth_endpoints:User <EMAIL> registered successfully without email verification
INFO:     127.0.0.1:58160 - "POST /api/v1/auth/register HTTP/1.1" 201 Created
INFO:     127.0.0.1:43224 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:32974 - "POST /api/v1/auth/login HTTP/1.1" 422 Unprocessable Entity
ERROR:app.services.business.user_management.services.cluster_auth_service:Authentication error: 'PostgreSQLClusterClient' object has no attribute 'authenticate_user'
INFO:     127.0.0.1:36930 - "POST /api/v1/auth/login HTTP/1.1" 401 Unauthorized
ERROR:app.services.business.user_management.services.cluster_auth_service:Authentication error: 'PostgreSQLClusterClient' object has no attribute 'authenticate_user'
INFO:     127.0.0.1:43566 - "POST /api/v1/auth/login HTTP/1.1" 401 Unauthorized
