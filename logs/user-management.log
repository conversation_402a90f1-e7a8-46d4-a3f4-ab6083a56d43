=== 2025-06-24 16:15:57 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [41417] using WatchFiles
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [41420]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 16:16:05.449 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 16:16:05.450 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/create "HTTP/1.1 200 OK"
INFO:app.api.auth_endpoints:User <EMAIL> registered successfully without email verification
INFO:     127.0.0.1:39260 - "POST /api/v1/auth/register HTTP/1.1" 400 Bad Request
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/create "HTTP/1.1 200 OK"
INFO:app.api.auth_endpoints:User <EMAIL> registered successfully without email verification
INFO:     127.0.0.1:36700 - "POST /api/v1/auth/register HTTP/1.1" 400 Bad Request
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/create "HTTP/1.1 200 OK"
INFO:app.api.auth_endpoints:User <EMAIL> registered successfully without email verification
INFO:     127.0.0.1:36392 - "POST /api/v1/auth/register HTTP/1.1" 400 Bad Request
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [41420]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [45335]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 16:20:29.442 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 16:20:29.443 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/create "HTTP/1.1 500 Internal Server Error"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 500 - invalid input value for enum userstatus: "active"
INFO:     127.0.0.1:50002 - "POST /api/v1/auth/register HTTP/1.1" 500 Internal Server Error
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [45335]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [46761]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 16:23:58.405 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 16:23:58.407 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/create "HTTP/1.1 500 Internal Server Error"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 500 - invalid input value for enum userstatus: "active"
INFO:     127.0.0.1:40618 - "POST /api/v1/auth/register HTTP/1.1" 500 Internal Server Error
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [46761]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [49325]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 16:25:27.454 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 16:25:27.456 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [49325]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [49570]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 16:26:46.445 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 16:26:46.446 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/create "HTTP/1.1 500 Internal Server Error"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 500 - invalid input value for enum userstatus: "active"
INFO:     127.0.0.1:58504 - "POST /api/v1/auth/register HTTP/1.1" 500 Internal Server Error
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [49570]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [50065]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 16:27:32.324 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 16:27:32.325 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/create "HTTP/1.1 500 Internal Server Error"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 500 - invalid input value for enum userstatus: "active"
INFO:     127.0.0.1:60900 - "POST /api/v1/auth/register HTTP/1.1" 500 Internal Server Error
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/create "HTTP/1.1 500 Internal Server Error"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 500 - invalid input value for enum userstatus: "active"
INFO:     127.0.0.1:60278 - "POST /api/v1/auth/register HTTP/1.1" 500 Internal Server Error
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/create "HTTP/1.1 500 Internal Server Error"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 500 - invalid input value for enum userstatus: "active"
INFO:     127.0.0.1:44012 - "POST /api/v1/auth/register HTTP/1.1" 500 Internal Server Error
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [50065]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [55316]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 16:33:24.691 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 16:33:24.692 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [55316]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [55384]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 16:33:42.236 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 16:33:42.237 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/create "HTTP/1.1 200 OK"
INFO:app.api.auth_endpoints:User <EMAIL> registered successfully without email verification
INFO:     127.0.0.1:58160 - "POST /api/v1/auth/register HTTP/1.1" 201 Created
INFO:     127.0.0.1:43224 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:32974 - "POST /api/v1/auth/login HTTP/1.1" 422 Unprocessable Entity
ERROR:app.services.business.user_management.services.cluster_auth_service:Authentication error: 'PostgreSQLClusterClient' object has no attribute 'authenticate_user'
INFO:     127.0.0.1:36930 - "POST /api/v1/auth/login HTTP/1.1" 401 Unauthorized
ERROR:app.services.business.user_management.services.cluster_auth_service:Authentication error: 'PostgreSQLClusterClient' object has no attribute 'authenticate_user'
INFO:     127.0.0.1:43566 - "POST /api/v1/auth/login HTTP/1.1" 401 Unauthorized
ERROR:app.services.business.user_management.services.cluster_auth_service:Authentication error: 'PostgreSQLClusterClient' object has no attribute 'authenticate_user'
INFO:     127.0.0.1:56060 - "POST /api/v1/auth/login HTTP/1.1" 401 Unauthorized
ERROR:app.services.business.user_management.services.cluster_auth_service:Authentication error: 'PostgreSQLClusterClient' object has no attribute 'authenticate_user'
INFO:     127.0.0.1:50358 - "POST /api/v1/auth/login HTTP/1.1" 401 Unauthorized
ERROR:app.services.business.user_management.services.cluster_auth_service:Authentication error: 'PostgreSQLClusterClient' object has no attribute 'authenticate_user'
INFO:     127.0.0.1:59358 - "POST /api/v1/auth/login HTTP/1.1" 401 Unauthorized
WARNING:  WatchFiles detected changes in 'app/services/business/user_management/services/cluster_auth_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [55384]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [64187]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 16:50:51.552 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 16:50:51.553 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
ERROR:app.services.business.user_management.services.cluster_auth_service:Authentication error: 'PostgreSQLClusterClient' object has no attribute 'authenticate_user'
INFO:     127.0.0.1:37888 - "POST /api/v1/auth/login HTTP/1.1" 401 Unauthorized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/create "HTTP/1.1 200 OK"
INFO:app.api.auth_endpoints:User <EMAIL> registered successfully without email verification
INFO:     127.0.0.1:57320 - "POST /api/v1/auth/register HTTP/1.1" 201 Created
WARNING:  WatchFiles detected changes in 'app/services/postgresql_client.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [64187]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [66344]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 16:54:07.110 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 16:54:07.112 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
WARNING:  WatchFiles detected changes in 'app/services/postgresql_client.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [66344]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [66443]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 16:54:39.691 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 16:54:39.692 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
WARNING:  WatchFiles detected changes in 'app/services/postgresql_client.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [66443]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [66589]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 16:55:19.584 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 16:55:19.586 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/authenticate "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/update-login?user_id=5ab59205-a551-4c58-b641-a4ea727a8905&success=True "HTTP/1.1 200 OK"
INFO:app.services.business.user_management.services.cluster_auth_service:User authenticated successfully: <EMAIL>
INFO:app.api.auth_endpoints:User <EMAIL> logged in successfully
INFO:     127.0.0.1:33456 - "POST /api/v1/auth/login HTTP/1.1" 200 OK
INFO:     127.0.0.1:41424 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:41430 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55224 - "GET /docs HTTP/1.1" 200 OK
/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID test_authentication_system_api_v1_auth_test_get for function test_authentication_system at /home/<USER>/Documents/simba-micro-services/services/core/user-management/app/api/auth_endpoints.py
  warnings.warn(message, stacklevel=1)
INFO:     127.0.0.1:55224 - "GET /openapi.json HTTP/1.1" 200 OK
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/authenticate "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/update-login?user_id=5ab59205-a551-4c58-b641-a4ea727a8905&success=True "HTTP/1.1 200 OK"
INFO:app.services.business.user_management.services.cluster_auth_service:User authenticated successfully: <EMAIL>
INFO:app.api.auth_endpoints:User <EMAIL> logged in successfully
INFO:     127.0.0.1:55054 - "POST /api/v1/auth/login HTTP/1.1" 200 OK
INFO:     127.0.0.1:39750 - "POST /api/v1/auth/logout HTTP/1.1" 401 Unauthorized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/authenticate "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/update-login?user_id=5ab59205-a551-4c58-b641-a4ea727a8905&success=True "HTTP/1.1 200 OK"
INFO:app.services.business.user_management.services.cluster_auth_service:User authenticated successfully: <EMAIL>
INFO:app.api.auth_endpoints:User <EMAIL> logged in successfully
INFO:     127.0.0.1:47970 - "POST /api/v1/auth/login HTTP/1.1" 200 OK
INFO:     127.0.0.1:47808 - "POST /api/v1/auth/login HTTP/1.1" 422 Unprocessable Entity
INFO:     127.0.0.1:39476 - "POST /api/v1/auth/logout HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:51536 - "GET /api/v1/auth/me HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:38268 - "POST /api/v1/auth/logout HTTP/1.1" 401 Unauthorized
INFO:     127.0.0.1:54818 - "POST /api/v1/auth/logout HTTP/1.1" 503 Service Unavailable
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [66589]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [70809]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 17:07:50.690 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 17:07:50.692 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [70809]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [70883]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 17:08:10.689 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 17:08:10.690 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
WARNING:  WatchFiles detected changes in 'app/services/business/user_management/services/cluster_auth_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [70883]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [70982]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 17:08:38.498 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 17:08:38.500 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
WARNING:app.services.business.user_management.services.cluster_auth_service:User not found in cluster service: 5ab59205-a551-4c58-b641-a4ea727a8905
INFO:     127.0.0.1:41492 - "GET /api/v1/auth/me HTTP/1.1" 401 Unauthorized
=== 2025-06-24 17:12:50 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
ERROR:    [Errno 98] Address already in use
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [70982]
INFO:     Stopping reloader process [41417]
=== 2025-06-24 17:13:23 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [75712] using WatchFiles
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [75715]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 17:13:33.195 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 17:13:33.197 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
WARNING:app.services.business.user_management.services.cluster_auth_service:User not found in cluster service: 5ab59205-a551-4c58-b641-a4ea727a8905
INFO:     127.0.0.1:49934 - "GET /api/v1/auth/me HTTP/1.1" 401 Unauthorized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/authenticate "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/update-login?user_id=5ab59205-a551-4c58-b641-a4ea727a8905&success=True "HTTP/1.1 200 OK"
INFO:app.services.business.user_management.services.cluster_auth_service:User authenticated successfully: <EMAIL>
INFO:app.api.auth_endpoints:User <EMAIL> logged in successfully
INFO:     127.0.0.1:39416 - "POST /api/v1/auth/login HTTP/1.1" 200 OK
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
WARNING:app.services.business.user_management.services.cluster_auth_service:User not found in cluster service: 5ab59205-a551-4c58-b641-a4ea727a8905
INFO:     127.0.0.1:47862 - "GET /api/v1/auth/me HTTP/1.1" 401 Unauthorized
WARNING:  WatchFiles detected changes in 'app/services/business/user_management/services/cluster_auth_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [75715]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [82346]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 17:20:23.952 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 17:20:23.954 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
WARNING:  WatchFiles detected changes in 'app/services/postgresql_client.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [82346]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [82399]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 17:20:40.525 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 17:20:40.527 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:httpx:HTTP Request: GET http://localhost:7233/api/v1/users/5ab59205-a551-4c58-b641-a4ea727a8905 "HTTP/1.1 404 Not Found"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 404 - Not Found
INFO:app.services.business.user_management.services.cluster_auth_service:Fallback: trying select_records for user_id: 5ab59205-a551-4c58-b641-a4ea727a8905
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
WARNING:app.services.business.user_management.services.cluster_auth_service:User not found in cluster service: 5ab59205-a551-4c58-b641-a4ea727a8905
ERROR:app.services.business.user_management.services.cluster_auth_service:Error in get_user, trying select_records: 
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
WARNING:app.services.business.user_management.services.cluster_auth_service:User not found in cluster service: 5ab59205-a551-4c58-b641-a4ea727a8905
INFO:     127.0.0.1:36492 - "GET /api/v1/auth/me HTTP/1.1" 401 Unauthorized
INFO:httpx:HTTP Request: GET http://localhost:7233/api/v1/users/5ab59205-a551-4c58-b641-a4ea727a8905 "HTTP/1.1 404 Not Found"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 404 - Not Found
INFO:app.services.business.user_management.services.cluster_auth_service:Fallback: trying select_records for user_id: 5ab59205-a551-4c58-b641-a4ea727a8905
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
WARNING:app.services.business.user_management.services.cluster_auth_service:User not found in cluster service: 5ab59205-a551-4c58-b641-a4ea727a8905
ERROR:app.services.business.user_management.services.cluster_auth_service:Error in get_user, trying select_records: 
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
WARNING:app.services.business.user_management.services.cluster_auth_service:User not found in cluster service: 5ab59205-a551-4c58-b641-a4ea727a8905
INFO:     127.0.0.1:40720 - "GET /api/v1/auth/me HTTP/1.1" 401 Unauthorized
WARNING:  WatchFiles detected changes in 'app/services/business/user_management/services/cluster_auth_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [82399]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [85142]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 17:23:59.715 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 17:23:59.716 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
WARNING:  WatchFiles detected changes in 'app/services/business/user_management/services/cluster_auth_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [85142]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [85222]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 17:24:20.210 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 17:24:20.212 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:httpx:HTTP Request: GET http://localhost:7233/api/v1/users/5ab59205-a551-4c58-b641-a4ea727a8905 "HTTP/1.1 404 Not Found"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 404 - Not Found
INFO:app.services.business.user_management.services.cluster_auth_service:Fallback: trying select_records for user_id: 5ab59205-a551-4c58-b641-a4ea727a8905
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.business.user_management.services.cluster_auth_service:Fallback select records response: {'success': True, 'table_name': 'users', 'records': [{'id': '5ab59205-a551-4c58-b641-a4ea727a8905', 'username': 'testuser54', 'email': '<EMAIL>', 'first_name': 'Test', 'last_name': 'User', 'is_active': True, 'is_verified': True, 'status': 'ACTIVE', 'created_at': '2025-06-24T16:33:50.115402', 'updated_at': '2025-06-24T17:16:43.079986'}], 'count': 1, 'timestamp': '2025-06-24T17:24:36.025744'}
WARNING:app.services.business.user_management.services.cluster_auth_service:User not found in cluster service: 5ab59205-a551-4c58-b641-a4ea727a8905
ERROR:app.services.business.user_management.services.cluster_auth_service:Error in get_user, trying select_records: 
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.business.user_management.services.cluster_auth_service:Select records response: {'success': True, 'table_name': 'users', 'records': [{'id': '5ab59205-a551-4c58-b641-a4ea727a8905', 'username': 'testuser54', 'email': '<EMAIL>', 'first_name': 'Test', 'last_name': 'User', 'is_active': True, 'is_verified': True, 'status': 'ACTIVE', 'created_at': '2025-06-24T16:33:50.115402', 'updated_at': '2025-06-24T17:16:43.079986'}], 'count': 1, 'timestamp': '2025-06-24T17:24:36.082419'}
WARNING:app.services.business.user_management.services.cluster_auth_service:User not found in cluster service: 5ab59205-a551-4c58-b641-a4ea727a8905
INFO:     127.0.0.1:46352 - "GET /api/v1/auth/me HTTP/1.1" 401 Unauthorized
WARNING:  WatchFiles detected changes in 'app/services/business/user_management/services/cluster_auth_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [85222]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [86182]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 17:25:34.441 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 17:25:34.442 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
WARNING:  WatchFiles detected changes in 'app/services/business/user_management/services/cluster_auth_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [86182]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [86246]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 17:25:51.145 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 17:25:51.147 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:httpx:HTTP Request: GET http://localhost:7233/api/v1/users/5ab59205-a551-4c58-b641-a4ea727a8905 "HTTP/1.1 404 Not Found"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 404 - Not Found
INFO:app.services.business.user_management.services.cluster_auth_service:Fallback: trying select_records for user_id: 5ab59205-a551-4c58-b641-a4ea727a8905
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.business.user_management.services.cluster_auth_service:Fallback select records response: {'success': True, 'table_name': 'users', 'records': [{'id': '5ab59205-a551-4c58-b641-a4ea727a8905', 'username': 'testuser54', 'email': '<EMAIL>', 'first_name': 'Test', 'last_name': 'User', 'is_active': True, 'is_verified': True, 'status': 'ACTIVE', 'created_at': '2025-06-24T16:33:50.115402', 'updated_at': '2025-06-24T17:16:43.079986'}], 'count': 1, 'timestamp': '2025-06-24T17:27:08.196207'}
INFO:     127.0.0.1:39726 - "GET /api/v1/auth/me HTTP/1.1" 200 OK
INFO:httpx:HTTP Request: GET http://localhost:7233/api/v1/users/5ab59205-a551-4c58-b641-a4ea727a8905 "HTTP/1.1 404 Not Found"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 404 - Not Found
INFO:app.services.business.user_management.services.cluster_auth_service:Fallback: trying select_records for user_id: 5ab59205-a551-4c58-b641-a4ea727a8905
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.business.user_management.services.cluster_auth_service:Fallback select records response: {'success': True, 'table_name': 'users', 'records': [{'id': '5ab59205-a551-4c58-b641-a4ea727a8905', 'username': 'testuser54', 'email': '<EMAIL>', 'first_name': 'Test', 'last_name': 'User', 'is_active': True, 'is_verified': True, 'status': 'ACTIVE', 'created_at': '2025-06-24T16:33:50.115402', 'updated_at': '2025-06-24T17:16:43.079986'}], 'count': 1, 'timestamp': '2025-06-24T17:28:15.126821'}
INFO:     127.0.0.1:36030 - "GET /api/v1/auth/me HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/services/business/user_management/services/cluster_auth_service.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [86246]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [89328]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 17:28:46.856 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 17:28:46.857 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:     127.0.0.1:39990 - "POST /api/v1/auth/logout HTTP/1.1" 503 Service Unavailable
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [89328]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [90327]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 17:31:09.492 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 17:31:09.494 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:httpx:HTTP Request: GET http://localhost:7233/api/v1/users/5ab59205-a551-4c58-b641-a4ea727a8905 "HTTP/1.1 404 Not Found"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 404 - Not Found
INFO:app.services.business.user_management.services.cluster_auth_service:Fallback: trying select_records for user_id: 5ab59205-a551-4c58-b641-a4ea727a8905
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
ERROR:app.api.auth_endpoints:Logout error: 'dict' object has no attribute 'id'
INFO:     127.0.0.1:57280 - "POST /api/v1/auth/logout HTTP/1.1" 500 Internal Server Error
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [90327]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [90923]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 17:31:28.695 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 17:31:28.697 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:httpx:HTTP Request: GET http://localhost:7233/api/v1/users/5ab59205-a551-4c58-b641-a4ea727a8905 "HTTP/1.1 404 Not Found"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 404 - Not Found
INFO:app.services.business.user_management.services.cluster_auth_service:Fallback: trying select_records for user_id: 5ab59205-a551-4c58-b641-a4ea727a8905
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
ERROR:app.services.business.user_management.services.token_blacklist_service:Token does not contain jti - cannot blacklist
WARNING:app.api.auth_endpoints:⚠️ User <EMAIL> logged out - token blacklisting failed
INFO:     127.0.0.1:35188 - "POST /api/v1/auth/logout HTTP/1.1" 500 Internal Server Error
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/uvicorn/protocols/http/httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/uvicorn/middleware/proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/fastapi/applications.py", line 1106, in __call__
    await super().__call__(scope, receive, send)
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/starlette/applications.py", line 122, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 184, in __call__
    raise exc
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 83, in __call__
    await self.app(scope, receive, send)
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 79, in __call__
    raise exc
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 68, in __call__
    await self.app(scope, receive, sender)
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/fastapi/middleware/asyncexitstack.py", line 20, in __call__
    raise e
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/fastapi/middleware/asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/starlette/routing.py", line 718, in __call__
    await route.handle(scope, receive, send)
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/starlette/routing.py", line 276, in handle
    await self.app(scope, receive, send)
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/starlette/routing.py", line 66, in app
    response = await func(request)
               ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/fastapi/routing.py", line 292, in app
    content = await serialize_response(
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/fastapi/routing.py", line 155, in serialize_response
    raise ResponseValidationError(
fastapi.exceptions.ResponseValidationError: 1 validation errors:
  {'type': 'string_type', 'loc': ('response', 'token_invalidated'), 'msg': 'Input should be a valid string', 'input': False, 'url': 'https://errors.pydantic.dev/2.11/v/string_type'}

INFO:httpx:HTTP Request: GET http://localhost:7233/api/v1/users/5ab59205-a551-4c58-b641-a4ea727a8905 "HTTP/1.1 404 Not Found"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 404 - Not Found
INFO:app.services.business.user_management.services.cluster_auth_service:Fallback: trying select_records for user_id: 5ab59205-a551-4c58-b641-a4ea727a8905
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
ERROR:app.services.business.user_management.services.token_blacklist_service:Token does not contain jti - cannot blacklist
WARNING:app.api.auth_endpoints:⚠️ User <EMAIL> logged out - token blacklisting failed
INFO:     127.0.0.1:54070 - "POST /api/v1/auth/logout HTTP/1.1" 500 Internal Server Error
ERROR:    Exception in ASGI application
Traceback (most recent call last):
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/uvicorn/protocols/http/httptools_impl.py", line 426, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/uvicorn/middleware/proxy_headers.py", line 84, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/fastapi/applications.py", line 1106, in __call__
    await super().__call__(scope, receive, send)
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/starlette/applications.py", line 122, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 184, in __call__
    raise exc
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 162, in __call__
    await self.app(scope, receive, _send)
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 83, in __call__
    await self.app(scope, receive, send)
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 79, in __call__
    raise exc
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 68, in __call__
    await self.app(scope, receive, sender)
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/fastapi/middleware/asyncexitstack.py", line 20, in __call__
    raise e
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/fastapi/middleware/asyncexitstack.py", line 17, in __call__
    await self.app(scope, receive, send)
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/starlette/routing.py", line 718, in __call__
    await route.handle(scope, receive, send)
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/starlette/routing.py", line 276, in handle
    await self.app(scope, receive, send)
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/starlette/routing.py", line 66, in app
    response = await func(request)
               ^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/fastapi/routing.py", line 292, in app
    content = await serialize_response(
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/fastapi/routing.py", line 155, in serialize_response
    raise ResponseValidationError(
fastapi.exceptions.ResponseValidationError: 1 validation errors:
  {'type': 'string_type', 'loc': ('response', 'token_invalidated'), 'msg': 'Input should be a valid string', 'input': False, 'url': 'https://errors.pydantic.dev/2.11/v/string_type'}

WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [90923]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [93835]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 17:33:05.335 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 17:33:05.339 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:httpx:HTTP Request: GET http://localhost:7233/api/v1/users/5ab59205-a551-4c58-b641-a4ea727a8905 "HTTP/1.1 404 Not Found"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 404 - Not Found
INFO:app.services.business.user_management.services.cluster_auth_service:Fallback: trying select_records for user_id: 5ab59205-a551-4c58-b641-a4ea727a8905
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
ERROR:app.services.business.user_management.services.token_blacklist_service:Token does not contain jti - cannot blacklist
WARNING:app.api.auth_endpoints:⚠️ User <EMAIL> logged out - token blacklisting failed
INFO:     127.0.0.1:36174 - "POST /api/v1/auth/logout HTTP/1.1" 200 OK
INFO:httpx:HTTP Request: GET http://localhost:7233/api/v1/users/5ab59205-a551-4c58-b641-a4ea727a8905 "HTTP/1.1 404 Not Found"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 404 - Not Found
INFO:app.services.business.user_management.services.cluster_auth_service:Fallback: trying select_records for user_id: 5ab59205-a551-4c58-b641-a4ea727a8905
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:     127.0.0.1:55462 - "GET /api/v1/auth/me HTTP/1.1" 200 OK
INFO:httpx:HTTP Request: GET http://localhost:7233/api/v1/users/5ab59205-a551-4c58-b641-a4ea727a8905 "HTTP/1.1 404 Not Found"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 404 - Not Found
INFO:app.services.business.user_management.services.cluster_auth_service:Fallback: trying select_records for user_id: 5ab59205-a551-4c58-b641-a4ea727a8905
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
ERROR:app.services.business.user_management.services.token_blacklist_service:Token does not contain jti - cannot blacklist
WARNING:app.api.auth_endpoints:⚠️ User <EMAIL> logged out - token blacklisting failed
INFO:     127.0.0.1:48578 - "POST /api/v1/auth/logout HTTP/1.1" 200 OK
INFO:httpx:HTTP Request: GET http://localhost:7233/api/v1/users/5ab59205-a551-4c58-b641-a4ea727a8905 "HTTP/1.1 404 Not Found"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 404 - Not Found
INFO:app.services.business.user_management.services.cluster_auth_service:Fallback: trying select_records for user_id: 5ab59205-a551-4c58-b641-a4ea727a8905
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:     127.0.0.1:51104 - "GET /api/v1/auth/me HTTP/1.1" 200 OK
