=== 2025-06-24 15:01:52 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [9988] using WatchFiles
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [9991]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
WARNING:app.services.cluster_db_init:⚠️ No roles found in database or query failed
ERROR:app.services.cluster_db_init:❌ Super admin role not found
ERROR:app.services.cluster_db_init:🔍 Admin role query result: {'success': False, 'error': True, 'status_code': 503, 'error_detail': 'PostgreSQL pool not initialized', 'is_duplicate_key': False}
ERROR:app.services.cluster_db_init:❌ Failed to create admin user
WARNING:main:⚠️ Database initialization attempt 1 failed
INFO:main:⏳ Waiting 2.0s before retry...
INFO:main:🔄 Database initialization attempt 2/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
WARNING:app.services.cluster_db_init:⚠️ No roles found in database or query failed
ERROR:app.services.cluster_db_init:❌ Super admin role not found
ERROR:app.services.cluster_db_init:🔍 Admin role query result: {'success': False, 'error': True, 'status_code': 503, 'error_detail': 'PostgreSQL pool not initialized', 'is_duplicate_key': False}
ERROR:app.services.cluster_db_init:❌ Failed to create admin user
WARNING:main:⚠️ Database initialization attempt 2 failed
INFO:main:⏳ Waiting 4.0s before retry...
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 15:01:59.941 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 15:01:59.943 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:main:🔄 Database initialization attempt 3/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
WARNING:app.services.cluster_db_init:⚠️ No roles found in database or query failed
ERROR:app.services.cluster_db_init:❌ Super admin role not found
ERROR:app.services.cluster_db_init:🔍 Admin role query result: {'success': False, 'error': True, 'status_code': 503, 'error_detail': 'PostgreSQL pool not initialized', 'is_duplicate_key': False}
ERROR:app.services.cluster_db_init:❌ Failed to create admin user
WARNING:main:⚠️ Database initialization attempt 3 failed
INFO:main:⏳ Waiting 8.0s before retry...
INFO:main:🔄 Database initialization attempt 4/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
WARNING:app.services.cluster_db_init:⚠️ No roles found in database or query failed
ERROR:app.services.cluster_db_init:❌ Super admin role not found
ERROR:app.services.cluster_db_init:🔍 Admin role query result: {'success': False, 'error': True, 'status_code': 503, 'error_detail': 'PostgreSQL pool not initialized', 'is_duplicate_key': False}
ERROR:app.services.cluster_db_init:❌ Failed to create admin user
WARNING:main:⚠️ Database initialization attempt 4 failed
INFO:main:⏳ Waiting 16.0s before retry...
INFO:main:🔄 Database initialization attempt 5/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
WARNING:app.services.cluster_db_init:⚠️ No roles found in database or query failed
ERROR:app.services.cluster_db_init:❌ Super admin role not found
ERROR:app.services.cluster_db_init:🔍 Admin role query result: {'success': False, 'error': True, 'status_code': 503, 'error_detail': 'PostgreSQL pool not initialized', 'is_duplicate_key': False}
ERROR:app.services.cluster_db_init:❌ Failed to create admin user
WARNING:main:⚠️ Database initialization attempt 5 failed
INFO:main:⏳ Waiting 32.0s before retry...
INFO:main:🔄 Database initialization attempt 6/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
WARNING:app.services.cluster_db_init:⚠️ No roles found in database or query failed
ERROR:app.services.cluster_db_init:❌ Super admin role not found
ERROR:app.services.cluster_db_init:🔍 Admin role query result: {'success': False, 'error': True, 'status_code': 503, 'error_detail': 'PostgreSQL pool not initialized', 'is_duplicate_key': False}
ERROR:app.services.cluster_db_init:❌ Failed to create admin user
WARNING:main:⚠️ Database initialization attempt 6 failed
INFO:main:⏳ Waiting 64.0s before retry...
INFO:main:🔄 Database initialization attempt 7/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
WARNING:app.services.cluster_db_init:⚠️ No roles found in database or query failed
ERROR:app.services.cluster_db_init:❌ Super admin role not found
ERROR:app.services.cluster_db_init:🔍 Admin role query result: {'success': False, 'error': True, 'status_code': 503, 'error_detail': 'PostgreSQL pool not initialized', 'is_duplicate_key': False}
ERROR:app.services.cluster_db_init:❌ Failed to create admin user
WARNING:main:⚠️ Database initialization attempt 7 failed
INFO:main:⏳ Waiting 128.0s before retry...
INFO:main:🔄 Database initialization attempt 8/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
WARNING:app.services.cluster_db_init:⚠️ No roles found in database or query failed
ERROR:app.services.cluster_db_init:❌ Super admin role not found
ERROR:app.services.cluster_db_init:🔍 Admin role query result: {'success': False, 'error': True, 'status_code': 503, 'error_detail': 'PostgreSQL pool not initialized', 'is_duplicate_key': False}
ERROR:app.services.cluster_db_init:❌ Failed to create admin user
WARNING:main:⚠️ Database initialization attempt 8 failed
INFO:main:⏳ Waiting 256.0s before retry...
WARNING:  WatchFiles detected changes in 'app/services/inter_service_client.py', 'main.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [9991]
WARNING:  WatchFiles detected changes in 'app/core/config.py'. Reloading...
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [12399]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
WARNING:app.services.cluster_db_init:⚠️ No roles found in database or query failed
ERROR:app.services.cluster_db_init:❌ Super admin role not found
ERROR:app.services.cluster_db_init:🔍 Admin role query result: {'success': False, 'error': True, 'status_code': 503, 'error_detail': 'PostgreSQL pool not initialized', 'is_duplicate_key': False}
ERROR:app.services.cluster_db_init:❌ Failed to create admin user
WARNING:main:⚠️ Database initialization attempt 1 failed
INFO:main:⏳ Waiting 2.0s before retry...
INFO:main:🔄 Database initialization attempt 2/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
WARNING:app.services.cluster_db_init:⚠️ No roles found in database or query failed
ERROR:app.services.cluster_db_init:❌ Super admin role not found
ERROR:app.services.cluster_db_init:🔍 Admin role query result: {'success': False, 'error': True, 'status_code': 503, 'error_detail': 'PostgreSQL pool not initialized', 'is_duplicate_key': False}
ERROR:app.services.cluster_db_init:❌ Failed to create admin user
WARNING:main:⚠️ Database initialization attempt 2 failed
INFO:main:⏳ Waiting 4.0s before retry...
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 15:06:49.947 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 15:06:49.948 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:main:🔄 Database initialization attempt 3/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
WARNING:app.services.cluster_db_init:⚠️ No roles found in database or query failed
ERROR:app.services.cluster_db_init:❌ Super admin role not found
ERROR:app.services.cluster_db_init:🔍 Admin role query result: {'success': False, 'error': True, 'status_code': 503, 'error_detail': 'PostgreSQL pool not initialized', 'is_duplicate_key': False}
ERROR:app.services.cluster_db_init:❌ Failed to create admin user
WARNING:main:⚠️ Database initialization attempt 3 failed
INFO:main:⏳ Waiting 8.0s before retry...
INFO:main:🔄 Database initialization attempt 4/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
WARNING:app.services.cluster_db_init:⚠️ No roles found in database or query failed
ERROR:app.services.cluster_db_init:❌ Super admin role not found
ERROR:app.services.cluster_db_init:🔍 Admin role query result: {'success': False, 'error': True, 'status_code': 503, 'error_detail': 'PostgreSQL pool not initialized', 'is_duplicate_key': False}
ERROR:app.services.cluster_db_init:❌ Failed to create admin user
WARNING:main:⚠️ Database initialization attempt 4 failed
INFO:main:⏳ Waiting 16.0s before retry...
INFO:main:🔄 Database initialization attempt 5/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
WARNING:app.services.cluster_db_init:⚠️ No roles found in database or query failed
ERROR:app.services.cluster_db_init:❌ Super admin role not found
ERROR:app.services.cluster_db_init:🔍 Admin role query result: {'success': False, 'error': True, 'status_code': 503, 'error_detail': 'PostgreSQL pool not initialized', 'is_duplicate_key': False}
ERROR:app.services.cluster_db_init:❌ Failed to create admin user
WARNING:main:⚠️ Database initialization attempt 5 failed
INFO:main:⏳ Waiting 32.0s before retry...
INFO:main:🔄 Database initialization attempt 6/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
WARNING:app.services.cluster_db_init:⚠️ No roles found in database or query failed
ERROR:app.services.cluster_db_init:❌ Super admin role not found
ERROR:app.services.cluster_db_init:🔍 Admin role query result: {'success': False, 'error': True, 'status_code': 503, 'error_detail': 'PostgreSQL pool not initialized', 'is_duplicate_key': False}
ERROR:app.services.cluster_db_init:❌ Failed to create admin user
WARNING:main:⚠️ Database initialization attempt 6 failed
INFO:main:⏳ Waiting 64.0s before retry...
INFO:main:🔄 Database initialization attempt 7/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
WARNING:app.services.cluster_db_init:⚠️ No roles found in database or query failed
ERROR:app.services.cluster_db_init:❌ Super admin role not found
ERROR:app.services.cluster_db_init:🔍 Admin role query result: {'success': False, 'error': True, 'status_code': 503, 'error_detail': 'PostgreSQL pool not initialized', 'is_duplicate_key': False}
ERROR:app.services.cluster_db_init:❌ Failed to create admin user
WARNING:main:⚠️ Database initialization attempt 7 failed
INFO:main:⏳ Waiting 128.0s before retry...
INFO:     127.0.0.1:53344 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:53350 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53354 - "GET /docs HTTP/1.1" 200 OK
/home/<USER>/Documents/simba-micro-services/services/core/user-management/.venv/lib/python3.11/site-packages/fastapi/openapi/utils.py:207: UserWarning: Duplicate Operation ID test_authentication_system_api_v1_auth_test_get for function test_authentication_system at /home/<USER>/Documents/simba-micro-services/services/core/user-management/app/api/auth_endpoints.py
  warnings.warn(message, stacklevel=1)
INFO:     127.0.0.1:53354 - "GET /openapi.json HTTP/1.1" 200 OK
INFO:     127.0.0.1:44482 - "POST /api/v1/auth/login HTTP/1.1" 503 Service Unavailable
INFO:main:🔄 Database initialization attempt 8/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
WARNING:app.services.cluster_db_init:⚠️ No roles found in database or query failed
ERROR:app.services.cluster_db_init:❌ Super admin role not found
ERROR:app.services.cluster_db_init:🔍 Admin role query result: {'success': False, 'error': True, 'status_code': 503, 'error_detail': 'PostgreSQL pool not initialized', 'is_duplicate_key': False}
ERROR:app.services.cluster_db_init:❌ Failed to create admin user
WARNING:main:⚠️ Database initialization attempt 8 failed
INFO:main:⏳ Waiting 256.0s before retry...
INFO:     127.0.0.1:44156 - "POST /api/v1/auth/login HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:43534 - "POST /api/v1/auth/login HTTP/1.1" 503 Service Unavailable
INFO:main:🔄 Database initialization attempt 9/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 503 Service Unavailable"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 503 - PostgreSQL pool not initialized
WARNING:app.services.cluster_db_init:⚠️ No roles found in database or query failed
ERROR:app.services.cluster_db_init:❌ Super admin role not found
ERROR:app.services.cluster_db_init:🔍 Admin role query result: {'success': False, 'error': True, 'status_code': 503, 'error_detail': 'PostgreSQL pool not initialized', 'is_duplicate_key': False}
ERROR:app.services.cluster_db_init:❌ Failed to create admin user
WARNING:main:⚠️ Database initialization attempt 9 failed
INFO:main:⏳ Waiting 512.0s before retry...
INFO:     127.0.0.1:34784 - "POST /api/v1/auth/login HTTP/1.1" 503 Service Unavailable
INFO:main:🔄 Database initialization attempt 10/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:     127.0.0.1:38818 - "POST /api/v1/auth/login HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:60204 - "POST /api/v1/auth/register HTTP/1.1" 503 Service Unavailable
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [12399]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [22513]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 15:36:59.951 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 15:36:59.952 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [22513]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [22613]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 15:37:28.694 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 15:37:28.695 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [22613]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [22656]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 15:37:52.205 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 15:37:52.208 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
ERROR:app.api.auth_endpoints:Unexpected registration error: PostgreSQLClusterClient.select_records() got an unexpected keyword argument 'where_conditions'
INFO:     127.0.0.1:48670 - "POST /api/v1/auth/register HTTP/1.1" 500 Internal Server Error
ERROR:app.api.auth_endpoints:Unexpected registration error: PostgreSQLClusterClient.select_records() got an unexpected keyword argument 'where_conditions'
INFO:     127.0.0.1:39904 - "POST /api/v1/auth/register HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:56646 - "GET /health HTTP/1.1" 200 OK
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [22656]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [25303]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 15:42:07.692 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 15:42:07.693 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [25303]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [25342]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 15:42:20.491 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 15:42:20.492 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/create "HTTP/1.1 500 Internal Server Error"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 500 - column "password_hash" of relation "users" does not exist
INFO:     127.0.0.1:34834 - "POST /api/v1/auth/register HTTP/1.1" 500 Internal Server Error
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/create "HTTP/1.1 500 Internal Server Error"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 500 - column "password_hash" of relation "users" does not exist
INFO:     127.0.0.1:57100 - "POST /api/v1/auth/register HTTP/1.1" 500 Internal Server Error
=== 2025-06-24 15:47:40 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
ERROR:    [Errno 98] Address already in use
=== 2025-06-24 15:48:55 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
ERROR:    [Errno 98] Address already in use
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [25342]
INFO:     Stopping reloader process [9988]
=== 2025-06-24 15:49:50 - Starting user-management ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/core/user-management']
INFO:     Uvicorn running on http://0.0.0.0:7301 (Press CTRL+C to quit)
INFO:     Started reloader process [28939] using WatchFiles
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [28942]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 15:49:57.942 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 15:49:57.943 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/create "HTTP/1.1 500 Internal Server Error"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 500 - column "password_hash" of relation "users" does not exist
INFO:     127.0.0.1:37322 - "POST /api/v1/auth/register HTTP/1.1" 500 Internal Server Error
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [28942]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [29685]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 15:52:46.375 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 15:52:46.376 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
WARNING:  WatchFiles detected changes in 'app/api/auth_endpoints.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
INFO:     Application shutdown complete.
INFO:     Finished server process [29685]
INFO:app.services.business.user_management.db.base:🚫 Direct database connection disabled - using PostgreSQL Cluster Service
INFO:main:✅ Successfully included router with 25 routes
INFO:     Started server process [29790]
INFO:     Waiting for application startup.
INFO:app.startup.vault_startup:🔐 Initializing Vault for user-management
WARNING:app.startup.vault_startup:⚠️ Failed to load config from vault: 404
WARNING:app.startup.vault_startup:⚠️ Failed to load secrets from vault: 401
INFO:app.startup.vault_startup:✅ Vault integration successful for user-management
INFO:app.startup.vault_startup:🔐 Vault Status: {'vault_enabled': True, 'environment': 'development', 'database_configured': False, 'redis_configured': False, 'mongodb_configured': False, 'secrets_available': False}
INFO:main:✅ Vault integration initialized successfully
INFO:main:🔧 Starting background database initialization via PostgreSQL Cluster Service...
INFO:main:🔄 Database initialization attempt 1/10...
INFO:app.services.cluster_db_init:🚀 Starting user management initialization via PostgreSQL Cluster Service...
INFO:app.services.postgresql_client:🔍 Health check attempt 1/5 for PostgreSQL Cluster Service...
INFO:main:🔗 Starting background inter-service communication initialization...
INFO:main:🔄 Inter-service initialization attempt 1/5...
INFO:     Application startup complete.
INFO:httpx:HTTP Request: GET http://localhost:7233/health "HTTP/1.1 200 OK"
INFO:app.services.postgresql_client:✅ PostgreSQL Cluster Service is healthy
INFO:app.services.cluster_db_init:📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation
INFO:app.services.cluster_db_init:👤 Creating default admin user...
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:app.services.cluster_db_init:✅ Users already exist, skipping admin creation
INFO:app.services.cluster_db_init:✅ User management initialization completed successfully via PostgreSQL Cluster Service
INFO:main:✅ User management database initialized successfully via PostgreSQL Cluster Service
INFO:httpx:HTTP Request: POST http://localhost:7010/api/v1/registry/register "HTTP/1.1 200 OK"
2025-06-24 15:53:11.690 | INFO     | app.services.inter_service_client:_register_user_management_service:90 - ✅ User management service registered with service registry
2025-06-24 15:53:11.691 | INFO     | app.services.inter_service_client:initialize:39 - ✅ User management service client initialized
INFO:main:✅ Inter-service communication initialized successfully
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/create "HTTP/1.1 500 Internal Server Error"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 500 - column "metadata" of relation "users" does not exist
INFO:     127.0.0.1:41324 - "POST /api/v1/auth/register HTTP/1.1" 500 Internal Server Error
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/create "HTTP/1.1 500 Internal Server Error"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 500 - null value in column "id" of relation "users" violates not-null constraint
DETAIL:  Failing row contains (null, <EMAIL>, testuser34, $2b$12$rzazdrMZvlpk/RQ8eJOwcuG7edR8g4MSha5EVFvGvgn79D1sXIX5e, Test, User, t, t, ACTIVE, null, null, null, null, null, null, null, null, null, null, 2025-06-24 15:57:26.174787, 2025-06-24 15:57:26.174793, null).
INFO:     127.0.0.1:48198 - "POST /api/v1/auth/register HTTP/1.1" 500 Internal Server Error
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/create "HTTP/1.1 500 Internal Server Error"
ERROR:app.services.postgresql_client:PostgreSQL Cluster request failed: 500 - null value in column "id" of relation "users" violates not-null constraint
DETAIL:  Failing row contains (null, <EMAIL>, string, $2b$12$/roCh87aWKMYZtbkgru0..t9aexZ5W6H0I.B/I9gO1oJP.AYbB.0S, string, string, t, t, ACTIVE, null, null, null, null, null, null, null, null, null, null, 2025-06-24 15:57:50.631045, 2025-06-24 15:57:50.631055, null).
INFO:     127.0.0.1:38812 - "POST /api/v1/auth/register HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:36270 - "GET /health HTTP/1.1" 200 OK
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/records/select "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:7233/api/v1/users/create "HTTP/1.1 200 OK"
INFO:app.api.auth_endpoints:User <EMAIL> registered successfully without email verification
INFO:     127.0.0.1:44040 - "POST /api/v1/auth/register HTTP/1.1" 400 Bad Request
