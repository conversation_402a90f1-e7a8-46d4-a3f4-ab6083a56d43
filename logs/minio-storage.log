=== 2025-06-24 14:34:39 - Starting minio-storage ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/minio-storage']
INFO:     Uvicorn running on http://0.0.0.0:7201 (Press CTRL+C to quit)
INFO:     Started reloader process [74895] using WatchFiles
/home/<USER>/Documents/simba-micro-services/services/shared-services/minio-storage/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:128: UserWarning: Field "model_name" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
INFO:     Started server process [74900]
INFO:     Waiting for application startup.
2025-06-24 14:34:45.931 | INFO     | main:startup_event:434 - 🚀 Starting MinIO Storage Service...
2025-06-24 14:34:45.931 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for minio-storage
2025-06-24 14:34:45.932 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for minio-storage
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-24 14:34:46.149 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for minio-storage
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/minio-storage "HTTP/1.1 200 OK"
2025-06-24 14:34:46.224 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for minio-storage
2025-06-24 14:34:46.226 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for minio-storage
2025-06-24 14:34:46.227 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for minio-storage
2025-06-24 14:34:46.725 | INFO     | main:create_bucket:150 - ✅ Bucket models already exists
2025-06-24 14:34:46.737 | INFO     | main:create_bucket:150 - ✅ Bucket simbaai-models already exists
2025-06-24 14:34:46.750 | INFO     | main:create_bucket:150 - ✅ Bucket simbaai-datasets already exists
2025-06-24 14:34:46.763 | INFO     | main:create_bucket:150 - ✅ Bucket simbaai-uploads already exists
2025-06-24 14:34:46.775 | INFO     | main:create_bucket:150 - ✅ Bucket simbaai-cache already exists
2025-06-24 14:34:46.791 | INFO     | main:create_bucket:150 - ✅ Bucket simbaai-backups already exists
2025-06-24 14:34:46.793 | INFO     | main:create_default_buckets:488 - ✅ Default buckets created/verified
2025-06-24 14:34:46.794 | INFO     | main:startup_event:462 - ✅ MinIO client initialized successfully
2025-06-24 14:34:46.795 | INFO     | main:startup_event:463 - 🎯 MinIO Storage Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:50056 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44980 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40940 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45584 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46874 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50628 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42608 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57184 - "GET /metrics HTTP/1.1" 404 Not Found
=== 2025-06-24 15:01:05 - Starting minio-storage ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/minio-storage']
INFO:     Uvicorn running on http://0.0.0.0:7201 (Press CTRL+C to quit)
INFO:     Started reloader process [9687] using WatchFiles
/home/<USER>/Documents/simba-micro-services/services/shared-services/minio-storage/.venv/lib/python3.11/site-packages/pydantic/_internal/_fields.py:128: UserWarning: Field "model_name" has conflict with protected namespace "model_".

You may be able to resolve this warning by setting `model_config['protected_namespaces'] = ()`.
  warnings.warn(
INFO:     Started server process [9690]
INFO:     Waiting for application startup.
2025-06-24 15:01:07.952 | INFO     | main:startup_event:434 - 🚀 Starting MinIO Storage Service...
2025-06-24 15:01:07.952 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for minio-storage
2025-06-24 15:01:07.953 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for minio-storage
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-24 15:01:08.084 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for minio-storage
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/minio-storage "HTTP/1.1 200 OK"
2025-06-24 15:01:08.122 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for minio-storage
2025-06-24 15:01:08.123 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for minio-storage
2025-06-24 15:01:08.123 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for minio-storage
WARNING:urllib3.connectionpool:Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f236aed9f50>: Failed to establish a new connection: [Errno 111] Connection refused')': /
WARNING:urllib3.connectionpool:Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f236aef0410>: Failed to establish a new connection: [Errno 111] Connection refused')': /
WARNING:urllib3.connectionpool:Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f236b077e50>: Failed to establish a new connection: [Errno 111] Connection refused')': /
WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f236aec6ed0>: Failed to establish a new connection: [Errno 111] Connection refused')': /
WARNING:urllib3.connectionpool:Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f236aec6890>: Failed to establish a new connection: [Errno 111] Connection refused')': /
2025-06-24 15:01:14.147 | ERROR    | main:startup_event:466 - ❌ Failed to initialize MinIO Storage Service: HTTPConnectionPool(host='localhost', port=9000): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f236aef0e90>: Failed to establish a new connection: [Errno 111] Connection refused'))
INFO:     Application startup complete.
INFO:     127.0.0.1:34398 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47764 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60688 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35074 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48814 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46718 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33476 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42262 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55410 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49834 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55318 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38620 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55208 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45376 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56324 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42058 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51326 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35928 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52968 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38380 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43040 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57774 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58224 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46076 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40754 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37442 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38758 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60166 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54510 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59908 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60962 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35068 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60612 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50624 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50910 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40568 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42580 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37272 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36186 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36000 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59376 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56396 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58950 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36038 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53048 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52556 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43336 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49780 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51548 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60462 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57638 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45962 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38046 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36062 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54076 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36808 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53642 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60424 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59712 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51902 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56516 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59958 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37742 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42792 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56264 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60674 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53604 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45006 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45938 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55870 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57994 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42228 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48352 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49624 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57976 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48880 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60336 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33106 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57376 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38198 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35668 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59496 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53736 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56544 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34734 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38148 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56226 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43256 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47510 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46854 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45866 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33820 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56378 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40778 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46474 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51832 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57518 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58520 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48284 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47736 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47000 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37006 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43646 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47588 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41376 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42298 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49800 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55486 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49594 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50932 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49468 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45718 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55268 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45668 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42584 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:32968 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55446 - "GET /metrics HTTP/1.1" 404 Not Found
