=== 2025-06-24 17:23:17 - Starting postgresql-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/postgresql-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7233 (Press CTRL+C to quit)
INFO:     Started reloader process [84518] using WatchFiles
INFO:     Started server process [84521]
INFO:     Waiting for application startup.
2025-06-24 17:23:20.181 | INFO     | main:startup_event:530 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-24 17:23:20.182 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-24 17:23:20.182 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-24 17:23:20.326 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/postgresql-cluster "HTTP/1.1 200 OK"
2025-06-24 17:23:20.399 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for postgresql-cluster
2025-06-24 17:23:20.400 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for postgresql-cluster
2025-06-24 17:23:20.400 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for postgresql-cluster
2025-06-24 17:23:20.932 | INFO     | main:startup_event:555 - 🔧 Starting database initialization...
2025-06-24 17:23:22.105 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-24 17:23:22.105 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-24 17:23:22.131 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-24 17:23:22.132 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-24 17:23:22.263 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-24 17:23:22.263 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-24 17:23:22.265 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-24 17:23:22.622 | INFO     | app.services.database.init_database:_insert_default_permissions:226 - ➕ Added permission: read
2025-06-24 17:23:22.630 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (a50e3154-298c-4d38-a66d-602916e553fd, read, Read access, null, null, 2025-06-24 17:23:22.627178, 2025-06-24 17:23:22.627184).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('a50e3154-298c-4d38-a66d-602916e553fd'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 17, 23, 22, 627178), 'updated_at': datetime.datetime(2025, 6, 24, 17, 23, 22, 627184)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 17:23:22.631 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (a50e3154-298c-4d38-a66d-602916e553fd, read, Read access, null, null, 2025-06-24 17:23:22.627178, 2025-06-24 17:23:22.627184).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('a50e3154-298c-4d38-a66d-602916e553fd'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 17, 23, 22, 627178), 'updated_at': datetime.datetime(2025, 6, 24, 17, 23, 22, 627184)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 17:23:22.632 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (a50e3154-298c-4d38-a66d-602916e553fd, read, Read access, null, null, 2025-06-24 17:23:22.627178, 2025-06-24 17:23:22.627184).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('a50e3154-298c-4d38-a66d-602916e553fd'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 17, 23, 22, 627178), 'updated_at': datetime.datetime(2025, 6, 24, 17, 23, 22, 627184)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 17:23:22.632 | WARNING  | main:startup_event:565 - ⚠️ Database initialization failed: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (a50e3154-298c-4d38-a66d-602916e553fd, read, Read access, null, null, 2025-06-24 17:23:22.627178, 2025-06-24 17:23:22.627184).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('a50e3154-298c-4d38-a66d-602916e553fd'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 17, 23, 22, 627178), 'updated_at': datetime.datetime(2025, 6, 24, 17, 23, 22, 627184)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 17:23:22.633 | INFO     | main:startup_event:566 - 🔄 Service will continue in basic mode...
2025-06-24 17:23:22.633 | INFO     | main:startup_event:575 - ✅ PostgreSQL connection pool initialized successfully
2025-06-24 17:23:22.633 | INFO     | main:startup_event:576 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:38100 - "GET /api/v1/users/5ab59205-a551-4c58-b641-a4ea727a8905 HTTP/1.1" 404 Not Found
2025-06-24 17:23:33.791 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:38110 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-24 17:23:33.868 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:38118 - "POST /api/v1/records/select HTTP/1.1" 200 OK
INFO:     127.0.0.1:51778 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58330 - "GET /health HTTP/1.1" 200 OK
2025-06-24 17:23:56.429 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:58332 - "POST /api/v1/records/select HTTP/1.1" 200 OK
INFO:     127.0.0.1:46416 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46426 - "GET /health HTTP/1.1" 200 OK
2025-06-24 17:24:17.348 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:46442 - "POST /api/v1/records/select HTTP/1.1" 200 OK
INFO:     127.0.0.1:34650 - "GET /api/v1/users/5ab59205-a551-4c58-b641-a4ea727a8905 HTTP/1.1" 404 Not Found
2025-06-24 17:24:36.025 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:34656 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-24 17:24:36.081 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:34668 - "POST /api/v1/records/select HTTP/1.1" 200 OK
INFO:     127.0.0.1:60386 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35690 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36168 - "GET /health HTTP/1.1" 200 OK
2025-06-24 17:25:30.979 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:36178 - "POST /api/v1/records/select HTTP/1.1" 200 OK
INFO:     127.0.0.1:37154 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37164 - "GET /health HTTP/1.1" 200 OK
2025-06-24 17:25:47.835 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:37174 - "POST /api/v1/records/select HTTP/1.1" 200 OK
INFO:     127.0.0.1:52228 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36418 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41908 - "GET /api/v1/users/5ab59205-a551-4c58-b641-a4ea727a8905 HTTP/1.1" 404 Not Found
2025-06-24 17:27:08.195 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:41924 - "POST /api/v1/records/select HTTP/1.1" 200 OK
INFO:     127.0.0.1:38594 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40690 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58506 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58522 - "GET /api/v1/users/5ab59205-a551-4c58-b641-a4ea727a8905 HTTP/1.1" 404 Not Found
2025-06-24 17:28:15.126 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:58530 - "POST /api/v1/records/select HTTP/1.1" 200 OK
INFO:     127.0.0.1:35136 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35142 - "GET /health HTTP/1.1" 200 OK
2025-06-24 17:28:43.570 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:35150 - "POST /api/v1/records/select HTTP/1.1" 200 OK
INFO:     127.0.0.1:49554 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33596 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38686 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59344 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34988 - "GET /health HTTP/1.1" 200 OK
2025-06-24 17:31:06.070 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:34990 - "POST /api/v1/records/select HTTP/1.1" 200 OK
INFO:     127.0.0.1:38612 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38618 - "GET /api/v1/users/5ab59205-a551-4c58-b641-a4ea727a8905 HTTP/1.1" 404 Not Found
2025-06-24 17:31:14.663 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:38620 - "POST /api/v1/records/select HTTP/1.1" 200 OK
INFO:     127.0.0.1:59680 - "GET /health HTTP/1.1" 200 OK
2025-06-24 17:31:25.286 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:59688 - "POST /api/v1/records/select HTTP/1.1" 200 OK
INFO:     127.0.0.1:41834 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41850 - "GET /api/v1/users/5ab59205-a551-4c58-b641-a4ea727a8905 HTTP/1.1" 404 Not Found
2025-06-24 17:31:44.111 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:41866 - "POST /api/v1/records/select HTTP/1.1" 200 OK
INFO:     127.0.0.1:48704 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49828 - "GET /api/v1/users/5ab59205-a551-4c58-b641-a4ea727a8905 HTTP/1.1" 404 Not Found
2025-06-24 17:32:31.995 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:49842 - "POST /api/v1/records/select HTTP/1.1" 200 OK
INFO:     127.0.0.1:45986 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53946 - "GET /health HTTP/1.1" 200 OK
2025-06-24 17:33:02.067 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:53954 - "POST /api/v1/records/select HTTP/1.1" 200 OK
INFO:     127.0.0.1:33364 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38976 - "GET /api/v1/users/5ab59205-a551-4c58-b641-a4ea727a8905 HTTP/1.1" 404 Not Found
2025-06-24 17:33:20.828 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:38984 - "POST /api/v1/records/select HTTP/1.1" 200 OK
INFO:     127.0.0.1:46828 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46844 - "GET /api/v1/users/5ab59205-a551-4c58-b641-a4ea727a8905 HTTP/1.1" 404 Not Found
2025-06-24 17:33:49.821 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:46858 - "POST /api/v1/records/select HTTP/1.1" 200 OK
INFO:     127.0.0.1:58428 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58440 - "GET /api/v1/users/5ab59205-a551-4c58-b641-a4ea727a8905 HTTP/1.1" 404 Not Found
2025-06-24 17:34:14.321 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:58456 - "POST /api/v1/records/select HTTP/1.1" 200 OK
INFO:     127.0.0.1:42338 - "GET /api/v1/users/5ab59205-a551-4c58-b641-a4ea727a8905 HTTP/1.1" 404 Not Found
2025-06-24 17:34:23.940 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:42346 - "POST /api/v1/records/select HTTP/1.1" 200 OK
INFO:     127.0.0.1:44518 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57780 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:57686 - "GET /metrics HTTP/1.1" 404 Not Found
