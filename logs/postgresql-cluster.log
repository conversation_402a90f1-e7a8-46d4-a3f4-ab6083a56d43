=== 2025-06-24 14:34:10 - Starting postgresql-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/postgresql-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7233 (Press CTRL+C to quit)
INFO:     Started reloader process [74694] using WatchFiles
INFO:     Started server process [74705]
INFO:     Waiting for application startup.
2025-06-24 14:34:14.658 | INFO     | main:startup_event:513 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-24 14:34:14.659 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-24 14:34:14.660 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-24 14:34:14.991 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/postgresql-cluster "HTTP/1.1 200 OK"
2025-06-24 14:34:15.127 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for postgresql-cluster
2025-06-24 14:34:15.129 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for postgresql-cluster
2025-06-24 14:34:15.130 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for postgresql-cluster
2025-06-24 14:34:16.085 | INFO     | main:startup_event:538 - 🔧 Starting database initialization...
2025-06-24 14:34:18.148 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-24 14:34:18.149 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-24 14:34:18.198 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-24 14:34:18.199 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-24 14:34:18.393 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-24 14:34:18.393 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-24 14:34:18.395 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-24 14:34:18.752 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 14:34:18.754 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 14:34:18.754 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 14:34:18.755 | WARNING  | main:startup_event:548 - ⚠️ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 14:34:18.755 | INFO     | main:startup_event:549 - 🔄 Service will continue in basic mode...
2025-06-24 14:34:18.756 | INFO     | main:startup_event:562 - ✅ PostgreSQL connection pool initialized successfully
2025-06-24 14:34:18.756 | INFO     | main:startup_event:563 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:60724 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48068 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34894 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38864 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59360 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50208 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38952 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39806 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38938 - "GET /metrics HTTP/1.1" 404 Not Found
=== 2025-06-24 15:00:41 - Starting postgresql-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/postgresql-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7233 (Press CTRL+C to quit)
INFO:     Started reloader process [9507] using WatchFiles
INFO:     Started server process [9510]
INFO:     Waiting for application startup.
2025-06-24 15:00:44.199 | INFO     | main:startup_event:513 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-24 15:00:44.199 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-24 15:00:44.200 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-24 15:00:44.328 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/postgresql-cluster "HTTP/1.1 200 OK"
2025-06-24 15:00:44.369 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for postgresql-cluster
2025-06-24 15:00:44.370 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for postgresql-cluster
2025-06-24 15:00:44.370 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for postgresql-cluster
2025-06-24 15:00:44.375 | ERROR    | main:startup_event:566 - ❌ Failed to initialize PostgreSQL Cluster Service: [Errno 111] Connection refused
INFO:     Application startup complete.
INFO:     127.0.0.1:60254 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41730 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33634 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:33638 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:33652 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:33656 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:33664 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:33676 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:33690 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:33696 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:58474 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:58486 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:58498 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:58514 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:57946 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:57956 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:57958 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:57974 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:57988 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43482 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:43494 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:43496 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:43502 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:45632 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53322 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:53332 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:53338 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:53354 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:60166 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53610 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34930 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:34942 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:34948 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:34964 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:51008 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56070 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37884 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46170 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49056 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49066 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:49076 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:49090 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:49100 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:47022 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47034 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:47048 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:47062 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:47078 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:47092 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:47106 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:47120 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:47136 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:39916 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:39930 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:39942 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:39948 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:39956 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:39960 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:39976 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:35076 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:33684 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33696 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:33712 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:33718 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:33720 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:35724 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35726 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:35728 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:35744 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:35746 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:60348 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37178 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36026 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:36036 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:36040 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:36054 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:46724 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33056 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43582 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-24 15:10:34.776 | INFO     | main:shutdown_event:614 - 🛑 Shutting down PostgreSQL Cluster Service...
2025-06-24 15:10:34.783 | INFO     | main:shutdown_event:620 - ✅ PostgreSQL Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [9510]
INFO:     Stopping reloader process [9507]
=== 2025-06-24 15:10:44 - Starting postgresql-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/postgresql-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7233 (Press CTRL+C to quit)
INFO:     Started reloader process [13264] using WatchFiles
INFO:     Started server process [13267]
INFO:     Waiting for application startup.
2025-06-24 15:10:46.976 | INFO     | main:startup_event:513 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-24 15:10:46.977 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-24 15:10:46.977 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-24 15:10:47.141 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/postgresql-cluster "HTTP/1.1 200 OK"
2025-06-24 15:10:47.230 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for postgresql-cluster
2025-06-24 15:10:47.231 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for postgresql-cluster
2025-06-24 15:10:47.232 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for postgresql-cluster
2025-06-24 15:10:47.241 | ERROR    | main:startup_event:566 - ❌ Failed to initialize PostgreSQL Cluster Service: [Errno 111] Connection refused
INFO:     Application startup complete.
INFO:     127.0.0.1:53458 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:53470 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:53480 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:53492 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:56698 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47796 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43438 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52196 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33168 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47486 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39638 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59974 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-24 15:15:02.525 | INFO     | main:shutdown_event:614 - 🛑 Shutting down PostgreSQL Cluster Service...
2025-06-24 15:15:02.526 | INFO     | main:shutdown_event:620 - ✅ PostgreSQL Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [13267]
INFO:     Stopping reloader process [13264]
=== 2025-06-24 15:15:12 - Starting postgresql-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/postgresql-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7233 (Press CTRL+C to quit)
INFO:     Started reloader process [14881] using WatchFiles
INFO:     Started server process [14884]
INFO:     Waiting for application startup.
2025-06-24 15:15:16.792 | INFO     | main:startup_event:513 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-24 15:15:16.793 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-24 15:15:16.793 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-24 15:15:16.998 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/postgresql-cluster "HTTP/1.1 200 OK"
2025-06-24 15:15:17.161 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for postgresql-cluster
2025-06-24 15:15:17.163 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for postgresql-cluster
2025-06-24 15:15:17.164 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for postgresql-cluster
2025-06-24 15:15:17.182 | ERROR    | main:startup_event:566 - ❌ Failed to initialize PostgreSQL Cluster Service: [Errno 111] Connection refused
INFO:     Application startup complete.
INFO:     127.0.0.1:59480 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:59494 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:59508 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:59520 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:48864 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55022 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35040 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50960 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51390 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48302 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60622 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52158 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35148 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:32774 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-24 15:20:25.763 | INFO     | main:shutdown_event:614 - 🛑 Shutting down PostgreSQL Cluster Service...
2025-06-24 15:20:25.764 | INFO     | main:shutdown_event:620 - ✅ PostgreSQL Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [14884]
INFO:     Stopping reloader process [14881]
=== 2025-06-24 15:20:35 - Starting postgresql-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/postgresql-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7233 (Press CTRL+C to quit)
INFO:     Started reloader process [17660] using WatchFiles
INFO:     Started server process [17665]
INFO:     Waiting for application startup.
2025-06-24 15:20:37.236 | INFO     | main:startup_event:513 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-24 15:20:37.236 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-24 15:20:37.237 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-24 15:20:37.383 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/postgresql-cluster "HTTP/1.1 200 OK"
2025-06-24 15:20:37.448 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for postgresql-cluster
2025-06-24 15:20:37.449 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for postgresql-cluster
2025-06-24 15:20:37.450 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for postgresql-cluster
2025-06-24 15:20:38.055 | INFO     | main:startup_event:538 - 🔧 Starting database initialization...
2025-06-24 15:20:39.705 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-24 15:20:39.706 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-24 15:20:39.765 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-24 15:20:39.766 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-24 15:20:39.932 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-24 15:20:39.932 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-24 15:20:39.933 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-24 15:20:40.215 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 15:20:40.217 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 15:20:40.217 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 15:20:40.218 | WARNING  | main:startup_event:548 - ⚠️ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 15:20:40.218 | INFO     | main:startup_event:549 - 🔄 Service will continue in basic mode...
2025-06-24 15:20:40.218 | INFO     | main:startup_event:562 - ✅ PostgreSQL connection pool initialized successfully
2025-06-24 15:20:40.219 | INFO     | main:startup_event:563 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:51250 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54896 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49922 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46646 - "GET /metrics HTTP/1.1" 404 Not Found
WARNING:  WatchFiles detected changes in 'app/services/database/models/user.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-24 15:22:38.264 | INFO     | main:shutdown_event:614 - 🛑 Shutting down PostgreSQL Cluster Service...
2025-06-24 15:22:38.283 | INFO     | main:shutdown_event:618 - ✅ PostgreSQL connection pool closed
2025-06-24 15:22:38.284 | INFO     | main:shutdown_event:620 - ✅ PostgreSQL Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [17665]
INFO:     Started server process [18582]
INFO:     Waiting for application startup.
2025-06-24 15:22:42.734 | INFO     | main:startup_event:513 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-24 15:22:42.735 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-24 15:22:42.736 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-24 15:22:42.976 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/postgresql-cluster "HTTP/1.1 200 OK"
2025-06-24 15:22:43.069 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for postgresql-cluster
2025-06-24 15:22:43.071 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for postgresql-cluster
2025-06-24 15:22:43.072 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for postgresql-cluster
2025-06-24 15:22:43.885 | INFO     | main:startup_event:538 - 🔧 Starting database initialization...
2025-06-24 15:22:45.705 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-24 15:22:45.706 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-24 15:22:45.744 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-24 15:22:45.745 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-24 15:22:45.980 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-24 15:22:45.981 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-24 15:22:45.986 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-24 15:22:46.576 | INFO     | app.services.database.init_database:_insert_default_permissions:226 - ➕ Added permission: read
2025-06-24 15:22:46.584 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (324b5ec8-577d-4e2f-b010-f3d52f490ab7, read, Read access, null, null, 2025-06-24 15:22:46.581892, 2025-06-24 15:22:46.581899).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('324b5ec8-577d-4e2f-b010-f3d52f490ab7'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 15, 22, 46, 581892), 'updated_at': datetime.datetime(2025, 6, 24, 15, 22, 46, 581899)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 15:22:46.586 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (324b5ec8-577d-4e2f-b010-f3d52f490ab7, read, Read access, null, null, 2025-06-24 15:22:46.581892, 2025-06-24 15:22:46.581899).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('324b5ec8-577d-4e2f-b010-f3d52f490ab7'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 15, 22, 46, 581892), 'updated_at': datetime.datetime(2025, 6, 24, 15, 22, 46, 581899)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 15:22:46.586 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (324b5ec8-577d-4e2f-b010-f3d52f490ab7, read, Read access, null, null, 2025-06-24 15:22:46.581892, 2025-06-24 15:22:46.581899).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('324b5ec8-577d-4e2f-b010-f3d52f490ab7'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 15, 22, 46, 581892), 'updated_at': datetime.datetime(2025, 6, 24, 15, 22, 46, 581899)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 15:22:46.587 | WARNING  | main:startup_event:548 - ⚠️ Database initialization failed: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (324b5ec8-577d-4e2f-b010-f3d52f490ab7, read, Read access, null, null, 2025-06-24 15:22:46.581892, 2025-06-24 15:22:46.581899).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('324b5ec8-577d-4e2f-b010-f3d52f490ab7'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 15, 22, 46, 581892), 'updated_at': datetime.datetime(2025, 6, 24, 15, 22, 46, 581899)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 15:22:46.587 | INFO     | main:startup_event:549 - 🔄 Service will continue in basic mode...
2025-06-24 15:22:46.588 | INFO     | main:startup_event:562 - ✅ PostgreSQL connection pool initialized successfully
2025-06-24 15:22:46.589 | INFO     | main:startup_event:563 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:52746 - "GET /metrics HTTP/1.1" 404 Not Found
WARNING:  WatchFiles detected changes in 'app/services/database/models/api_keys.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-24 15:22:55.627 | INFO     | main:shutdown_event:614 - 🛑 Shutting down PostgreSQL Cluster Service...
2025-06-24 15:22:55.646 | INFO     | main:shutdown_event:618 - ✅ PostgreSQL connection pool closed
2025-06-24 15:22:55.647 | INFO     | main:shutdown_event:620 - ✅ PostgreSQL Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [18582]
INFO:     Started server process [18626]
INFO:     Waiting for application startup.
2025-06-24 15:22:59.466 | INFO     | main:startup_event:513 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-24 15:22:59.468 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-24 15:22:59.468 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-24 15:22:59.697 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/postgresql-cluster "HTTP/1.1 200 OK"
2025-06-24 15:22:59.824 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for postgresql-cluster
2025-06-24 15:22:59.826 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for postgresql-cluster
2025-06-24 15:22:59.827 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for postgresql-cluster
2025-06-24 15:23:00.677 | INFO     | main:startup_event:538 - 🔧 Starting database initialization...
2025-06-24 15:23:02.096 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-24 15:23:02.097 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-24 15:23:02.125 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-24 15:23:02.126 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-24 15:23:02.262 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-24 15:23:02.262 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-24 15:23:02.263 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-24 15:23:02.701 | INFO     | app.services.database.init_database:_insert_default_permissions:226 - ➕ Added permission: read
2025-06-24 15:23:02.711 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (d480416a-fd7c-4246-9a97-************, read, Read access, null, null, 2025-06-24 15:23:02.707732, 2025-06-24 15:23:02.707741).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('d480416a-fd7c-4246-9a97-************'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 15, 23, 2, 707732), 'updated_at': datetime.datetime(2025, 6, 24, 15, 23, 2, 707741)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 15:23:02.712 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (d480416a-fd7c-4246-9a97-************, read, Read access, null, null, 2025-06-24 15:23:02.707732, 2025-06-24 15:23:02.707741).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('d480416a-fd7c-4246-9a97-************'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 15, 23, 2, 707732), 'updated_at': datetime.datetime(2025, 6, 24, 15, 23, 2, 707741)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 15:23:02.713 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (d480416a-fd7c-4246-9a97-************, read, Read access, null, null, 2025-06-24 15:23:02.707732, 2025-06-24 15:23:02.707741).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('d480416a-fd7c-4246-9a97-************'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 15, 23, 2, 707732), 'updated_at': datetime.datetime(2025, 6, 24, 15, 23, 2, 707741)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 15:23:02.714 | WARNING  | main:startup_event:548 - ⚠️ Database initialization failed: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (d480416a-fd7c-4246-9a97-************, read, Read access, null, null, 2025-06-24 15:23:02.707732, 2025-06-24 15:23:02.707741).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('d480416a-fd7c-4246-9a97-************'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 15, 23, 2, 707732), 'updated_at': datetime.datetime(2025, 6, 24, 15, 23, 2, 707741)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 15:23:02.714 | INFO     | main:startup_event:549 - 🔄 Service will continue in basic mode...
2025-06-24 15:23:02.714 | INFO     | main:startup_event:562 - ✅ PostgreSQL connection pool initialized successfully
2025-06-24 15:23:02.715 | INFO     | main:startup_event:563 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:34548 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54506 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54508 - "GET /health HTTP/1.1" 200 OK
2025-06-24 15:23:49.829 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:54518 - "POST /api/v1/records/select HTTP/1.1" 200 OK
INFO:     127.0.0.1:50566 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33356 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41378 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47320 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:60998 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39952 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34776 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42900 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52482 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38608 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58264 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52890 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49614 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39610 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59882 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53488 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58694 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41098 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:54726 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:42802 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40108 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41490 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50594 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51056 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35310 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56916 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59096 - "GET /health HTTP/1.1" 200 OK
2025-06-24 15:36:56.537 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:59100 - "POST /api/v1/records/select HTTP/1.1" 200 OK
INFO:     127.0.0.1:49342 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52994 - "GET /health HTTP/1.1" 200 OK
2025-06-24 15:37:24.869 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:53000 - "POST /api/v1/records/select HTTP/1.1" 200 OK
INFO:     127.0.0.1:38238 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38254 - "GET /health HTTP/1.1" 200 OK
2025-06-24 15:37:48.497 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:38270 - "POST /api/v1/records/select HTTP/1.1" 200 OK
INFO:     127.0.0.1:60976 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:32938 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55454 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38724 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58964 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:58144 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52308 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43582 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44488 - "GET /health HTTP/1.1" 200 OK
2025-06-24 15:42:04.284 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:44496 - "POST /api/v1/records/select HTTP/1.1" 200 OK
INFO:     127.0.0.1:50028 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50030 - "GET /health HTTP/1.1" 200 OK
2025-06-24 15:42:17.269 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:50032 - "POST /api/v1/records/select HTTP/1.1" 200 OK
INFO:     127.0.0.1:58640 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43782 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47324 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40858 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38452 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49870 - "GET /metrics HTTP/1.1" 404 Not Found
2025-06-24 15:45:16.712 | INFO     | main:select_records:347 - 📥 Selected 0 records from users
INFO:     127.0.0.1:49882 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-24 15:45:17.766 | ERROR    | main:create_user:397 - ❌ Failed to create user testuser33: column "password_hash" of relation "users" does not exist
INFO:     127.0.0.1:49890 - "POST /api/v1/users/create HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:40228 - "GET /metrics HTTP/1.1" 404 Not Found
2025-06-24 15:45:44.884 | INFO     | main:select_records:347 - 📥 Selected 0 records from users
INFO:     127.0.0.1:40238 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-24 15:45:45.943 | ERROR    | main:create_user:397 - ❌ Failed to create user string: column "password_hash" of relation "users" does not exist
INFO:     127.0.0.1:40250 - "POST /api/v1/users/create HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:45750 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:51940 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:40148 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38402 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39176 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56778 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45414 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47770 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43082 - "GET /health HTTP/1.1" 200 OK
2025-06-24 15:49:54.447 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:43092 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-24 15:50:08.840 | INFO     | main:select_records:347 - 📥 Selected 0 records from users
INFO:     127.0.0.1:55280 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-24 15:50:09.952 | ERROR    | main:create_user:397 - ❌ Failed to create user string: column "password_hash" of relation "users" does not exist
INFO:     127.0.0.1:55296 - "POST /api/v1/users/create HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:39774 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39834 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34788 - "GET /metrics HTTP/1.1" 404 Not Found
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-24 15:51:32.929 | INFO     | main:shutdown_event:614 - 🛑 Shutting down PostgreSQL Cluster Service...
2025-06-24 15:51:32.953 | INFO     | main:shutdown_event:618 - ✅ PostgreSQL connection pool closed
2025-06-24 15:51:32.955 | INFO     | main:shutdown_event:620 - ✅ PostgreSQL Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [18626]
INFO:     Started server process [29399]
INFO:     Waiting for application startup.
2025-06-24 15:51:38.168 | INFO     | main:startup_event:513 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-24 15:51:38.169 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-24 15:51:38.169 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-24 15:51:38.357 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/postgresql-cluster "HTTP/1.1 200 OK"
2025-06-24 15:51:38.550 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for postgresql-cluster
2025-06-24 15:51:38.552 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for postgresql-cluster
2025-06-24 15:51:38.554 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for postgresql-cluster
2025-06-24 15:51:39.247 | INFO     | main:startup_event:538 - 🔧 Starting database initialization...
2025-06-24 15:51:41.375 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-24 15:51:41.376 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-24 15:51:41.426 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-24 15:51:41.427 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-24 15:51:41.812 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-24 15:51:41.813 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-24 15:51:41.815 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-24 15:51:42.439 | INFO     | app.services.database.init_database:_insert_default_permissions:226 - ➕ Added permission: read
2025-06-24 15:51:42.448 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (3cfe4cb6-8183-4d7d-a799-c1c6783cb04e, read, Read access, null, null, 2025-06-24 15:51:42.444829, 2025-06-24 15:51:42.44484).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('3cfe4cb6-8183-4d7d-a799-c1c6783cb04e'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 15, 51, 42, 444829), 'updated_at': datetime.datetime(2025, 6, 24, 15, 51, 42, 444840)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 15:51:42.450 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (3cfe4cb6-8183-4d7d-a799-c1c6783cb04e, read, Read access, null, null, 2025-06-24 15:51:42.444829, 2025-06-24 15:51:42.44484).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('3cfe4cb6-8183-4d7d-a799-c1c6783cb04e'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 15, 51, 42, 444829), 'updated_at': datetime.datetime(2025, 6, 24, 15, 51, 42, 444840)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 15:51:42.451 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (3cfe4cb6-8183-4d7d-a799-c1c6783cb04e, read, Read access, null, null, 2025-06-24 15:51:42.444829, 2025-06-24 15:51:42.44484).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('3cfe4cb6-8183-4d7d-a799-c1c6783cb04e'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 15, 51, 42, 444829), 'updated_at': datetime.datetime(2025, 6, 24, 15, 51, 42, 444840)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 15:51:42.452 | WARNING  | main:startup_event:548 - ⚠️ Database initialization failed: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (3cfe4cb6-8183-4d7d-a799-c1c6783cb04e, read, Read access, null, null, 2025-06-24 15:51:42.444829, 2025-06-24 15:51:42.44484).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('3cfe4cb6-8183-4d7d-a799-c1c6783cb04e'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 15, 51, 42, 444829), 'updated_at': datetime.datetime(2025, 6, 24, 15, 51, 42, 444840)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 15:51:42.453 | INFO     | main:startup_event:549 - 🔄 Service will continue in basic mode...
2025-06-24 15:51:42.454 | INFO     | main:startup_event:562 - ✅ PostgreSQL connection pool initialized successfully
2025-06-24 15:51:42.455 | INFO     | main:startup_event:563 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:42476 - "GET /metrics HTTP/1.1" 404 Not Found
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-24 15:51:44.968 | INFO     | main:shutdown_event:614 - 🛑 Shutting down PostgreSQL Cluster Service...
2025-06-24 15:51:44.982 | INFO     | main:shutdown_event:618 - ✅ PostgreSQL connection pool closed
2025-06-24 15:51:44.983 | INFO     | main:shutdown_event:620 - ✅ PostgreSQL Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [29399]
INFO:     Started server process [29432]
INFO:     Waiting for application startup.
2025-06-24 15:51:48.097 | INFO     | main:startup_event:513 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-24 15:51:48.098 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-24 15:51:48.098 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-24 15:51:48.281 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/postgresql-cluster "HTTP/1.1 200 OK"
2025-06-24 15:51:48.352 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for postgresql-cluster
2025-06-24 15:51:48.354 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for postgresql-cluster
2025-06-24 15:51:48.355 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for postgresql-cluster
2025-06-24 15:51:48.945 | INFO     | main:startup_event:538 - 🔧 Starting database initialization...
2025-06-24 15:51:50.862 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-24 15:51:50.863 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-24 15:51:50.919 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-24 15:51:50.920 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-24 15:51:51.139 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-24 15:51:51.140 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-24 15:51:51.142 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-24 15:51:51.780 | INFO     | app.services.database.init_database:_insert_default_permissions:226 - ➕ Added permission: read
2025-06-24 15:51:51.794 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (dba3cbf0-67a9-4a51-a974-d7a94b2df6a8, read, Read access, null, null, 2025-06-24 15:51:51.789426, 2025-06-24 15:51:51.789436).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('dba3cbf0-67a9-4a51-a974-d7a94b2df6a8'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 15, 51, 51, 789426), 'updated_at': datetime.datetime(2025, 6, 24, 15, 51, 51, 789436)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 15:51:51.797 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (dba3cbf0-67a9-4a51-a974-d7a94b2df6a8, read, Read access, null, null, 2025-06-24 15:51:51.789426, 2025-06-24 15:51:51.789436).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('dba3cbf0-67a9-4a51-a974-d7a94b2df6a8'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 15, 51, 51, 789426), 'updated_at': datetime.datetime(2025, 6, 24, 15, 51, 51, 789436)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 15:51:51.798 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (dba3cbf0-67a9-4a51-a974-d7a94b2df6a8, read, Read access, null, null, 2025-06-24 15:51:51.789426, 2025-06-24 15:51:51.789436).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('dba3cbf0-67a9-4a51-a974-d7a94b2df6a8'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 15, 51, 51, 789426), 'updated_at': datetime.datetime(2025, 6, 24, 15, 51, 51, 789436)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 15:51:51.798 | WARNING  | main:startup_event:548 - ⚠️ Database initialization failed: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (dba3cbf0-67a9-4a51-a974-d7a94b2df6a8, read, Read access, null, null, 2025-06-24 15:51:51.789426, 2025-06-24 15:51:51.789436).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('dba3cbf0-67a9-4a51-a974-d7a94b2df6a8'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 15, 51, 51, 789426), 'updated_at': datetime.datetime(2025, 6, 24, 15, 51, 51, 789436)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 15:51:51.799 | INFO     | main:startup_event:549 - 🔄 Service will continue in basic mode...
2025-06-24 15:51:51.800 | INFO     | main:startup_event:562 - ✅ PostgreSQL connection pool initialized successfully
2025-06-24 15:51:51.800 | INFO     | main:startup_event:563 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-24 15:51:56.824 | INFO     | main:shutdown_event:614 - 🛑 Shutting down PostgreSQL Cluster Service...
2025-06-24 15:51:56.837 | INFO     | main:shutdown_event:618 - ✅ PostgreSQL connection pool closed
2025-06-24 15:51:56.838 | INFO     | main:shutdown_event:620 - ✅ PostgreSQL Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [29432]
INFO:     Started server process [29503]
INFO:     Waiting for application startup.
2025-06-24 15:52:00.123 | INFO     | main:startup_event:513 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-24 15:52:00.124 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-24 15:52:00.125 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-24 15:52:00.269 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/postgresql-cluster "HTTP/1.1 200 OK"
2025-06-24 15:52:00.342 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for postgresql-cluster
2025-06-24 15:52:00.343 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for postgresql-cluster
2025-06-24 15:52:00.343 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for postgresql-cluster
2025-06-24 15:52:01.124 | INFO     | main:startup_event:538 - 🔧 Starting database initialization...
2025-06-24 15:52:02.654 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-24 15:52:02.655 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-24 15:52:02.682 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-24 15:52:02.683 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-24 15:52:02.855 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-24 15:52:02.856 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-24 15:52:02.858 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-24 15:52:03.313 | INFO     | app.services.database.init_database:_insert_default_permissions:226 - ➕ Added permission: read
2025-06-24 15:52:03.322 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (9840affc-764e-47ff-a967-9f94ed21cd87, read, Read access, null, null, 2025-06-24 15:52:03.318963, 2025-06-24 15:52:03.31897).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('9840affc-764e-47ff-a967-9f94ed21cd87'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 15, 52, 3, 318963), 'updated_at': datetime.datetime(2025, 6, 24, 15, 52, 3, 318970)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 15:52:03.323 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (9840affc-764e-47ff-a967-9f94ed21cd87, read, Read access, null, null, 2025-06-24 15:52:03.318963, 2025-06-24 15:52:03.31897).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('9840affc-764e-47ff-a967-9f94ed21cd87'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 15, 52, 3, 318963), 'updated_at': datetime.datetime(2025, 6, 24, 15, 52, 3, 318970)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 15:52:03.324 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (9840affc-764e-47ff-a967-9f94ed21cd87, read, Read access, null, null, 2025-06-24 15:52:03.318963, 2025-06-24 15:52:03.31897).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('9840affc-764e-47ff-a967-9f94ed21cd87'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 15, 52, 3, 318963), 'updated_at': datetime.datetime(2025, 6, 24, 15, 52, 3, 318970)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 15:52:03.324 | WARNING  | main:startup_event:548 - ⚠️ Database initialization failed: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (9840affc-764e-47ff-a967-9f94ed21cd87, read, Read access, null, null, 2025-06-24 15:52:03.318963, 2025-06-24 15:52:03.31897).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('9840affc-764e-47ff-a967-9f94ed21cd87'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 15, 52, 3, 318963), 'updated_at': datetime.datetime(2025, 6, 24, 15, 52, 3, 318970)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 15:52:03.325 | INFO     | main:startup_event:549 - 🔄 Service will continue in basic mode...
2025-06-24 15:52:03.325 | INFO     | main:startup_event:562 - ✅ PostgreSQL connection pool initialized successfully
2025-06-24 15:52:03.325 | INFO     | main:startup_event:563 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-24 15:52:11.256 | INFO     | main:shutdown_event:614 - 🛑 Shutting down PostgreSQL Cluster Service...
2025-06-24 15:52:11.273 | INFO     | main:shutdown_event:618 - ✅ PostgreSQL connection pool closed
2025-06-24 15:52:11.274 | INFO     | main:shutdown_event:620 - ✅ PostgreSQL Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [29503]
INFO:     Started server process [29577]
INFO:     Waiting for application startup.
2025-06-24 15:52:14.655 | INFO     | main:startup_event:513 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-24 15:52:14.655 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-24 15:52:14.656 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-24 15:52:14.833 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/postgresql-cluster "HTTP/1.1 200 OK"
2025-06-24 15:52:14.902 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for postgresql-cluster
2025-06-24 15:52:14.903 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for postgresql-cluster
2025-06-24 15:52:14.904 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for postgresql-cluster
2025-06-24 15:52:15.497 | INFO     | main:startup_event:538 - 🔧 Starting database initialization...
2025-06-24 15:52:17.320 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-24 15:52:17.321 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-24 15:52:17.356 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-24 15:52:17.357 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-24 15:52:17.497 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-24 15:52:17.498 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-24 15:52:17.499 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-24 15:52:17.908 | INFO     | app.services.database.init_database:_insert_default_permissions:226 - ➕ Added permission: read
2025-06-24 15:52:17.918 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (83cc99ce-e31d-4a73-ad93-b2809372381c, read, Read access, null, null, 2025-06-24 15:52:17.914634, 2025-06-24 15:52:17.91464).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('83cc99ce-e31d-4a73-ad93-b2809372381c'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 15, 52, 17, 914634), 'updated_at': datetime.datetime(2025, 6, 24, 15, 52, 17, 914640)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 15:52:17.921 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (83cc99ce-e31d-4a73-ad93-b2809372381c, read, Read access, null, null, 2025-06-24 15:52:17.914634, 2025-06-24 15:52:17.91464).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('83cc99ce-e31d-4a73-ad93-b2809372381c'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 15, 52, 17, 914634), 'updated_at': datetime.datetime(2025, 6, 24, 15, 52, 17, 914640)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 15:52:17.922 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (83cc99ce-e31d-4a73-ad93-b2809372381c, read, Read access, null, null, 2025-06-24 15:52:17.914634, 2025-06-24 15:52:17.91464).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('83cc99ce-e31d-4a73-ad93-b2809372381c'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 15, 52, 17, 914634), 'updated_at': datetime.datetime(2025, 6, 24, 15, 52, 17, 914640)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 15:52:17.922 | WARNING  | main:startup_event:548 - ⚠️ Database initialization failed: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (83cc99ce-e31d-4a73-ad93-b2809372381c, read, Read access, null, null, 2025-06-24 15:52:17.914634, 2025-06-24 15:52:17.91464).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('83cc99ce-e31d-4a73-ad93-b2809372381c'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 15, 52, 17, 914634), 'updated_at': datetime.datetime(2025, 6, 24, 15, 52, 17, 914640)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 15:52:17.923 | INFO     | main:startup_event:549 - 🔄 Service will continue in basic mode...
2025-06-24 15:52:17.923 | INFO     | main:startup_event:562 - ✅ PostgreSQL connection pool initialized successfully
2025-06-24 15:52:17.923 | INFO     | main:startup_event:563 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:35824 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34388 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34398 - "GET /health HTTP/1.1" 200 OK
2025-06-24 15:52:42.665 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:34406 - "POST /api/v1/records/select HTTP/1.1" 200 OK
INFO:     127.0.0.1:55252 - "GET /health HTTP/1.1" 200 OK
2025-06-24 15:53:08.099 | INFO     | main:select_records:347 - 📥 Selected 1 records from users
INFO:     127.0.0.1:55266 - "POST /api/v1/records/select HTTP/1.1" 200 OK
INFO:     127.0.0.1:59358 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56402 - "GET /metrics HTTP/1.1" 404 Not Found
2025-06-24 15:54:08.903 | INFO     | main:select_records:347 - 📥 Selected 0 records from users
INFO:     127.0.0.1:40050 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-24 15:54:10.031 | ERROR    | main:create_user:397 - ❌ Failed to create user testuser33: column "metadata" of relation "users" does not exist
INFO:     127.0.0.1:40062 - "POST /api/v1/users/create HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:55358 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:44658 - "GET /metrics HTTP/1.1" 404 Not Found
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-24 15:54:56.926 | INFO     | main:shutdown_event:614 - 🛑 Shutting down PostgreSQL Cluster Service...
2025-06-24 15:54:56.940 | INFO     | main:shutdown_event:618 - ✅ PostgreSQL connection pool closed
2025-06-24 15:54:56.941 | INFO     | main:shutdown_event:620 - ✅ PostgreSQL Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [29577]
INFO:     Started server process [30617]
INFO:     Waiting for application startup.
2025-06-24 15:55:01.099 | INFO     | main:startup_event:525 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-24 15:55:01.100 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-24 15:55:01.101 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-24 15:55:01.409 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/postgresql-cluster "HTTP/1.1 200 OK"
2025-06-24 15:55:01.537 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for postgresql-cluster
2025-06-24 15:55:01.541 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for postgresql-cluster
2025-06-24 15:55:01.542 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for postgresql-cluster
2025-06-24 15:55:02.282 | INFO     | main:startup_event:550 - 🔧 Starting database initialization...
2025-06-24 15:55:04.123 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-24 15:55:04.123 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-24 15:55:04.166 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-24 15:55:04.167 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-24 15:55:04.388 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-24 15:55:04.389 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-24 15:55:04.391 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-24 15:55:04.913 | INFO     | app.services.database.init_database:_insert_default_permissions:226 - ➕ Added permission: read
2025-06-24 15:55:04.925 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (2bf0cdf0-c5ad-4caf-abd9-c2b21d51007b, read, Read access, null, null, 2025-06-24 15:55:04.92111, 2025-06-24 15:55:04.921119).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('2bf0cdf0-c5ad-4caf-abd9-c2b21d51007b'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 15, 55, 4, 921110), 'updated_at': datetime.datetime(2025, 6, 24, 15, 55, 4, 921119)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 15:55:04.929 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (2bf0cdf0-c5ad-4caf-abd9-c2b21d51007b, read, Read access, null, null, 2025-06-24 15:55:04.92111, 2025-06-24 15:55:04.921119).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('2bf0cdf0-c5ad-4caf-abd9-c2b21d51007b'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 15, 55, 4, 921110), 'updated_at': datetime.datetime(2025, 6, 24, 15, 55, 4, 921119)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 15:55:04.931 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (2bf0cdf0-c5ad-4caf-abd9-c2b21d51007b, read, Read access, null, null, 2025-06-24 15:55:04.92111, 2025-06-24 15:55:04.921119).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('2bf0cdf0-c5ad-4caf-abd9-c2b21d51007b'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 15, 55, 4, 921110), 'updated_at': datetime.datetime(2025, 6, 24, 15, 55, 4, 921119)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 15:55:04.933 | WARNING  | main:startup_event:560 - ⚠️ Database initialization failed: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (2bf0cdf0-c5ad-4caf-abd9-c2b21d51007b, read, Read access, null, null, 2025-06-24 15:55:04.92111, 2025-06-24 15:55:04.921119).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('2bf0cdf0-c5ad-4caf-abd9-c2b21d51007b'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 15, 55, 4, 921110), 'updated_at': datetime.datetime(2025, 6, 24, 15, 55, 4, 921119)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 15:55:04.934 | INFO     | main:startup_event:561 - 🔄 Service will continue in basic mode...
2025-06-24 15:55:04.934 | INFO     | main:startup_event:574 - ✅ PostgreSQL connection pool initialized successfully
2025-06-24 15:55:04.935 | INFO     | main:startup_event:575 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:57866 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46114 - "GET /metrics HTTP/1.1" 404 Not Found
WARNING:  WatchFiles detected changes in 'main.py'. Reloading...
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-24 15:56:10.379 | INFO     | main:shutdown_event:626 - 🛑 Shutting down PostgreSQL Cluster Service...
2025-06-24 15:56:10.397 | INFO     | main:shutdown_event:630 - ✅ PostgreSQL connection pool closed
2025-06-24 15:56:10.398 | INFO     | main:shutdown_event:632 - ✅ PostgreSQL Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [30617]
INFO:     Started server process [30883]
INFO:     Waiting for application startup.
2025-06-24 15:56:14.558 | INFO     | main:startup_event:525 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-24 15:56:14.559 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-24 15:56:14.559 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-24 15:56:14.835 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/postgresql-cluster "HTTP/1.1 200 OK"
2025-06-24 15:56:14.962 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for postgresql-cluster
2025-06-24 15:56:14.965 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for postgresql-cluster
2025-06-24 15:56:14.967 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for postgresql-cluster
2025-06-24 15:56:15.743 | INFO     | main:startup_event:550 - 🔧 Starting database initialization...
2025-06-24 15:56:17.570 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-24 15:56:17.571 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-24 15:56:17.608 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-24 15:56:17.609 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-24 15:56:17.809 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-24 15:56:17.810 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-24 15:56:17.811 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-24 15:56:18.291 | INFO     | app.services.database.init_database:_insert_default_permissions:226 - ➕ Added permission: read
2025-06-24 15:56:18.299 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (9ff8a92b-8703-49e5-ba51-5eccb908342d, read, Read access, null, null, 2025-06-24 15:56:18.29646, 2025-06-24 15:56:18.296465).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('9ff8a92b-8703-49e5-ba51-5eccb908342d'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 15, 56, 18, 296460), 'updated_at': datetime.datetime(2025, 6, 24, 15, 56, 18, 296465)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 15:56:18.300 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (9ff8a92b-8703-49e5-ba51-5eccb908342d, read, Read access, null, null, 2025-06-24 15:56:18.29646, 2025-06-24 15:56:18.296465).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('9ff8a92b-8703-49e5-ba51-5eccb908342d'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 15, 56, 18, 296460), 'updated_at': datetime.datetime(2025, 6, 24, 15, 56, 18, 296465)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 15:56:18.301 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (9ff8a92b-8703-49e5-ba51-5eccb908342d, read, Read access, null, null, 2025-06-24 15:56:18.29646, 2025-06-24 15:56:18.296465).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('9ff8a92b-8703-49e5-ba51-5eccb908342d'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 15, 56, 18, 296460), 'updated_at': datetime.datetime(2025, 6, 24, 15, 56, 18, 296465)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 15:56:18.302 | WARNING  | main:startup_event:560 - ⚠️ Database initialization failed: (raised as a result of Query-invoked autoflush; consider using a session.no_autoflush block if this flush is occurring prematurely)
(psycopg2.errors.NotNullViolation) null value in column "resource" of relation "permissions" violates not-null constraint
DETAIL:  Failing row contains (9ff8a92b-8703-49e5-ba51-5eccb908342d, read, Read access, null, null, 2025-06-24 15:56:18.29646, 2025-06-24 15:56:18.296465).

[SQL: INSERT INTO permissions (id, name, description, resource, action, created_at, updated_at) VALUES (%(id)s::UUID, %(name)s, %(description)s, %(resource)s, %(action)s, %(created_at)s, %(updated_at)s)]
[parameters: {'id': UUID('9ff8a92b-8703-49e5-ba51-5eccb908342d'), 'name': 'read', 'description': 'Read access', 'resource': None, 'action': None, 'created_at': datetime.datetime(2025, 6, 24, 15, 56, 18, 296460), 'updated_at': datetime.datetime(2025, 6, 24, 15, 56, 18, 296465)}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-06-24 15:56:18.303 | INFO     | main:startup_event:561 - 🔄 Service will continue in basic mode...
2025-06-24 15:56:18.303 | INFO     | main:startup_event:574 - ✅ PostgreSQL connection pool initialized successfully
2025-06-24 15:56:18.304 | INFO     | main:startup_event:575 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:59874 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33784 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56832 - "GET /metrics HTTP/1.1" 404 Not Found
2025-06-24 15:57:25.150 | INFO     | main:select_records:347 - 📥 Selected 0 records from users
INFO:     127.0.0.1:37716 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-24 15:57:26.259 | ERROR    | main:create_user:409 - ❌ Failed to create user testuser34: null value in column "id" of relation "users" violates not-null constraint
DETAIL:  Failing row contains (null, <EMAIL>, testuser34, $2b$12$rzazdrMZvlpk/RQ8eJOwcuG7edR8g4MSha5EVFvGvgn79D1sXIX5e, Test, User, t, t, ACTIVE, null, null, null, null, null, null, null, null, null, null, 2025-06-24 15:57:26.174787, 2025-06-24 15:57:26.174793, null).
INFO:     127.0.0.1:37732 - "POST /api/v1/users/create HTTP/1.1" 500 Internal Server Error
INFO:     127.0.0.1:59494 - "GET /metrics HTTP/1.1" 404 Not Found
2025-06-24 15:57:49.520 | INFO     | main:select_records:347 - 📥 Selected 0 records from users
INFO:     127.0.0.1:59504 - "POST /api/v1/records/select HTTP/1.1" 200 OK
2025-06-24 15:57:50.634 | ERROR    | main:create_user:409 - ❌ Failed to create user string: null value in column "id" of relation "users" violates not-null constraint
DETAIL:  Failing row contains (null, <EMAIL>, string, $2b$12$/roCh87aWKMYZtbkgru0..t9aexZ5W6H0I.B/I9gO1oJP.AYbB.0S, string, string, t, t, ACTIVE, null, null, null, null, null, null, null, null, null, null, 2025-06-24 15:57:50.631045, 2025-06-24 15:57:50.631055, null).
INFO:     127.0.0.1:58558 - "POST /api/v1/users/create HTTP/1.1" 500 Internal Server Error
