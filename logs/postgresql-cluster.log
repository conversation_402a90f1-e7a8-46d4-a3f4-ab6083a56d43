=== 2025-06-24 14:34:10 - Starting postgresql-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/postgresql-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7233 (Press CTRL+C to quit)
INFO:     Started reloader process [74694] using WatchFiles
INFO:     Started server process [74705]
INFO:     Waiting for application startup.
2025-06-24 14:34:14.658 | INFO     | main:startup_event:513 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-24 14:34:14.659 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-24 14:34:14.660 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-24 14:34:14.991 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/postgresql-cluster "HTTP/1.1 200 OK"
2025-06-24 14:34:15.127 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for postgresql-cluster
2025-06-24 14:34:15.129 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for postgresql-cluster
2025-06-24 14:34:15.130 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for postgresql-cluster
2025-06-24 14:34:16.085 | INFO     | main:startup_event:538 - 🔧 Starting database initialization...
2025-06-24 14:34:18.148 | INFO     | app.services.database.init_database:initialize_complete_database:54 - 🚀 Starting comprehensive database initialization...
2025-06-24 14:34:18.149 | INFO     | app.services.database.init_database:_create_database_if_not_exists:94 - 🔍 Checking if database 'simba_models_database' exists...
2025-06-24 14:34:18.198 | INFO     | app.services.database.init_database:_create_database_if_not_exists:124 - ✅ Database 'simba_models_database' already exists
2025-06-24 14:34:18.199 | INFO     | app.services.database.init_database:_create_all_tables:138 - 🏗️ Creating database tables...
2025-06-24 14:34:18.393 | INFO     | app.services.database.init_database:_create_all_tables:163 - ✅ Created 28 database tables successfully!
2025-06-24 14:34:18.393 | INFO     | app.services.database.init_database:_create_all_tables:164 - 📋 Tables: admin_dashboard_widgets, admin_reports, api_key_permissions, api_key_usage, api_keys, audit_logs, billing_usage, email_verification_tokens, feature_usage, login_attempts, password_reset_tokens, payment_methods, payment_providers, payment_webhooks, payments, permissions, rate_limits, refunds, role_permissions, roles, subscription_plans, subscriptions, token_blacklist, usage_logs, user_profiles, user_roles, user_sessions, users
2025-06-24 14:34:18.395 | INFO     | app.services.database.init_database:_insert_default_data:176 - 📝 Inserting default data...
2025-06-24 14:34:18.752 | ERROR    | app.services.database.init_database:_insert_default_data:201 - ❌ Failed to insert default data: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 14:34:18.754 | ERROR    | app.services.database.init_database:_insert_default_data:208 - ❌ Default data insertion failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 14:34:18.754 | ERROR    | app.services.database.init_database:initialize_complete_database:83 - ❌ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 14:34:18.755 | WARNING  | main:startup_event:548 - ⚠️ Database initialization failed: Mapper 'Mapper[User(users)]' has no property 'api_keys'.  If this property was indicated from other mappers or configure events, ensure registry.configure() has been called.
2025-06-24 14:34:18.755 | INFO     | main:startup_event:549 - 🔄 Service will continue in basic mode...
2025-06-24 14:34:18.756 | INFO     | main:startup_event:562 - ✅ PostgreSQL connection pool initialized successfully
2025-06-24 14:34:18.756 | INFO     | main:startup_event:563 - 🎯 PostgreSQL Cluster Service ready to serve microservices
INFO:     Application startup complete.
INFO:     127.0.0.1:60724 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:48068 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34894 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38864 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59360 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:50208 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38952 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39806 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:38938 - "GET /metrics HTTP/1.1" 404 Not Found
=== 2025-06-24 15:00:41 - Starting postgresql-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/postgresql-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7233 (Press CTRL+C to quit)
INFO:     Started reloader process [9507] using WatchFiles
INFO:     Started server process [9510]
INFO:     Waiting for application startup.
2025-06-24 15:00:44.199 | INFO     | main:startup_event:513 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-24 15:00:44.199 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-24 15:00:44.200 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-24 15:00:44.328 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/postgresql-cluster "HTTP/1.1 200 OK"
2025-06-24 15:00:44.369 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for postgresql-cluster
2025-06-24 15:00:44.370 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for postgresql-cluster
2025-06-24 15:00:44.370 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for postgresql-cluster
2025-06-24 15:00:44.375 | ERROR    | main:startup_event:566 - ❌ Failed to initialize PostgreSQL Cluster Service: [Errno 111] Connection refused
INFO:     Application startup complete.
INFO:     127.0.0.1:60254 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:41730 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33634 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:33638 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:33652 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:33656 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:33664 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:33676 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:33690 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:33696 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:58474 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:58486 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:58498 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:58514 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:57946 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:57956 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:57958 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:57974 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:57988 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43482 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:43494 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:43496 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:43502 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:45632 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53322 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:53332 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:53338 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:53354 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:60166 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:53610 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:34930 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:34942 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:34948 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:34964 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:51008 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:56070 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37884 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:46170 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49056 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:49066 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:49076 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:49090 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:49100 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:47022 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47034 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:47048 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:47062 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:47078 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:47092 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:47106 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:47120 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:47136 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:39916 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:39930 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:39942 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:39948 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:39956 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:39960 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:39976 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:35076 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:33684 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33696 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:33712 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:33718 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:33720 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:35724 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35726 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:35728 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:35744 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:35746 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:60348 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:37178 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:36026 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:36036 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:36040 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:36054 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:46724 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33056 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43582 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-24 15:10:34.776 | INFO     | main:shutdown_event:614 - 🛑 Shutting down PostgreSQL Cluster Service...
2025-06-24 15:10:34.783 | INFO     | main:shutdown_event:620 - ✅ PostgreSQL Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [9510]
INFO:     Stopping reloader process [9507]
=== 2025-06-24 15:10:44 - Starting postgresql-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/postgresql-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7233 (Press CTRL+C to quit)
INFO:     Started reloader process [13264] using WatchFiles
INFO:     Started server process [13267]
INFO:     Waiting for application startup.
2025-06-24 15:10:46.976 | INFO     | main:startup_event:513 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-24 15:10:46.977 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-24 15:10:46.977 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-24 15:10:47.141 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/postgresql-cluster "HTTP/1.1 200 OK"
2025-06-24 15:10:47.230 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for postgresql-cluster
2025-06-24 15:10:47.231 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for postgresql-cluster
2025-06-24 15:10:47.232 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for postgresql-cluster
2025-06-24 15:10:47.241 | ERROR    | main:startup_event:566 - ❌ Failed to initialize PostgreSQL Cluster Service: [Errno 111] Connection refused
INFO:     Application startup complete.
INFO:     127.0.0.1:53458 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:53470 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:53480 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:53492 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:56698 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47796 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:43438 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52196 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:33168 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:47486 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:39638 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:59974 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-06-24 15:15:02.525 | INFO     | main:shutdown_event:614 - 🛑 Shutting down PostgreSQL Cluster Service...
2025-06-24 15:15:02.526 | INFO     | main:shutdown_event:620 - ✅ PostgreSQL Cluster Service shutdown complete
INFO:     Application shutdown complete.
INFO:     Finished server process [13267]
INFO:     Stopping reloader process [13264]
=== 2025-06-24 15:15:12 - Starting postgresql-cluster ===
INFO:     Will watch for changes in these directories: ['/home/<USER>/Documents/simba-micro-services/services/shared-services/postgresql-cluster']
INFO:     Uvicorn running on http://0.0.0.0:7233 (Press CTRL+C to quit)
INFO:     Started reloader process [14881] using WatchFiles
INFO:     Started server process [14884]
INFO:     Waiting for application startup.
2025-06-24 15:15:16.792 | INFO     | main:startup_event:513 - 🚀 Starting PostgreSQL Cluster Service...
2025-06-24 15:15:16.793 | INFO     | app.vault_integration:initialize:30 - 🔐 Initializing Vault integration for postgresql-cluster
2025-06-24 15:15:16.793 | INFO     | app.client.vault_http_client:initialize:41 - 🔐 Initializing Vault HTTP client for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/health "HTTP/1.1 200 OK"
2025-06-24 15:15:16.998 | INFO     | app.client.vault_http_client:initialize:47 - ✅ Vault HTTP client initialized for postgresql-cluster
INFO:httpx:HTTP Request: GET http://localhost:7200/api/v1/secrets/postgresql-cluster "HTTP/1.1 200 OK"
2025-06-24 15:15:17.161 | INFO     | app.client.vault_http_client:get_all_config:98 - ✅ Retrieved 20 config items for postgresql-cluster
2025-06-24 15:15:17.163 | INFO     | app.client.vault_http_client:inject_config_to_env:251 - ✅ Injected 20 config items into environment for postgresql-cluster
2025-06-24 15:15:17.164 | INFO     | app.vault_integration:initialize:39 - ✅ Vault integration initialized for postgresql-cluster
2025-06-24 15:15:17.182 | ERROR    | main:startup_event:566 - ❌ Failed to initialize PostgreSQL Cluster Service: [Errno 111] Connection refused
INFO:     Application startup complete.
INFO:     127.0.0.1:59480 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:59494 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:59508 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:59520 - "POST /api/v1/records/select HTTP/1.1" 503 Service Unavailable
INFO:     127.0.0.1:48864 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:55022 - "GET /metrics HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:35040 - "GET /metrics HTTP/1.1" 404 Not Found
