#!/usr/bin/env python3
"""
Test script to verify JWT token blacklisting functionality
"""

import requests
import json
import sys

BASE_URL = "http://localhost:7301"

def test_token_blacklist():
    """Test the complete token blacklist flow"""
    
    # Step 1: Register a new user
    print("Step 1: Registering new user...")
    register_data = {
        "username": "blacklisttest",
        "email": "<EMAIL>", 
        "password": "TestPassword123!",
        "first_name": "Blacklist",
        "last_name": "Test"
    }
    
    response = requests.post(f"{BASE_URL}/api/v1/auth/register", json=register_data)
    if response.status_code != 201:
        print(f"Registration failed: {response.status_code} - {response.text}")
        return False
    
    print("✅ User registered successfully")
    
    # Step 2: Login to get token
    print("\nStep 2: Logging in...")
    login_data = {
        "username": "<EMAIL>",
        "password": "TestPassword123!"
    }
    
    response = requests.post(
        f"{BASE_URL}/api/v1/auth/login",
        data=login_data,
        headers={"Content-Type": "application/x-www-form-urlencoded"}
    )
    
    if response.status_code != 200:
        print(f"Login failed: {response.status_code} - {response.text}")
        return False
    
    token_data = response.json()
    access_token = token_data["access_token"]
    print(f"✅ Login successful, got token: {access_token[:50]}...")
    
    # Step 3: Test /me endpoint with token
    print("\nStep 3: Testing /me endpoint...")
    headers = {"Authorization": f"Bearer {access_token}"}
    
    response = requests.get(f"{BASE_URL}/api/v1/auth/me", headers=headers)
    if response.status_code != 200:
        print(f"/me failed: {response.status_code} - {response.text}")
        return False
    
    user_data = response.json()
    print(f"✅ /me successful: {user_data['email']}")
    
    # Step 4: Logout (should blacklist token)
    print("\nStep 4: Logging out...")
    response = requests.post(f"{BASE_URL}/api/v1/auth/logout", headers=headers)
    if response.status_code != 200:
        print(f"Logout failed: {response.status_code} - {response.text}")
        return False
    
    logout_data = response.json()
    print(f"✅ Logout successful: {logout_data}")
    
    # Step 5: Try to use token after logout (should fail)
    print("\nStep 5: Testing token after logout...")
    response = requests.get(f"{BASE_URL}/api/v1/auth/me", headers=headers)
    if response.status_code == 200:
        print("❌ Token still works after logout - blacklisting failed!")
        return False
    elif response.status_code == 401:
        print("✅ Token rejected after logout - blacklisting works!")
        return True
    else:
        print(f"Unexpected response: {response.status_code} - {response.text}")
        return False

if __name__ == "__main__":
    success = test_token_blacklist()
    sys.exit(0 if success else 1)
