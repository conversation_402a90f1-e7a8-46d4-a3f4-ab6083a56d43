"""
User-Management-Service Service
Authentication, authorization, user profiles
Technology: FastAPI + Keycloak + PostgreSQL
Port: 7301
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import logging
import os
from pathlib import Path

# Import Vault integration functions
from app.startup.vault_startup import initialize_vault_for_service as initialize_vault, get_vault_config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get the main.py path for service categorization
main_py_path = Path(__file__)

# Create FastAPI application with comprehensive OpenAPI documentation
app = FastAPI(
    title="SimbaAI User Management Service",
    description="""
## 🔐 Professional User Management & Authentication API

**Production-ready authentication service** with comprehensive security features, real database integration, and Vault secrets management.

### **Key Features**
- **JWT Authentication** with secure token management
- **Email Verification** with real email service integration
- **Password Reset** with secure token-based flow
- **Role-Based Access Control** (RBAC) with admin management
- **Multi-Factor Authentication** support (coming soon)
- **Real PostgreSQL Integration** with Vault secrets
- **Comprehensive Audit Logging** for all operations

### **Architecture**
- **Database**: PostgreSQL with SQLAlchemy ORM
- **Secrets**: HashiCorp Vault integration
- **Email**: Real email service for verification/reset
- **Security**: Argon2 password hashing, JWT tokens
- **Monitoring**: Comprehensive logging and health checks

### **API Categories**
1. **Authentication** - Login, logout, registration, profile management
2. **Email Verification** - Account activation and email confirmation
3. **Password Management** - Secure password reset flow
4. **Admin Management** - User CRUD operations for administrators
5. **Health & Monitoring** - Service status and connectivity checks

### **Security Features**
- Secure password hashing with Argon2
- JWT tokens with configurable expiration
- Token blacklisting for logout security
- Email enumeration protection
- Failed login attempt tracking
- Role-based authorization
- Input validation and sanitization

### **External Integrations**
- **PostgreSQL**: User data persistence
- **Vault**: Secrets and configuration management
- **Email Service**: Verification and password reset emails
- **Redis**: Session and token management
- **Monitoring**: Prometheus metrics and health checks
    """,
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    contact={
        "name": "SimbaAI Development Team",
        "email": "<EMAIL>",
        "url": "https://simbaai.com"
    },
    license_info={
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT"
    },
    servers=[
        {
            "url": "http://localhost:7301",
            "description": "Development server"
        },
        {
            "url": "https://api.simbaai.com",
            "description": "Production server"
        }
    ],
    tags_metadata=[
        {
            "name": "Authentication",
            "description": "**Core authentication operations** including user registration, login, logout, and profile management. All endpoints use JWT tokens for secure authentication.",
            "externalDocs": {
                "description": "Authentication Guide",
                "url": "https://docs.simbaai.com/auth"
            }
        },
        {
            "name": "Email Verification",
            "description": "**Email verification system** for account activation. Users must verify their email address before accessing the system.",
            "externalDocs": {
                "description": "Email Verification Guide",
                "url": "https://docs.simbaai.com/email-verification"
            }
        },
        {
            "name": "Password Management",
            "description": "**Secure password reset flow** with email-based token verification. Includes password change functionality for authenticated users.",
            "externalDocs": {
                "description": "Password Management Guide",
                "url": "https://docs.simbaai.com/password-management"
            }
        },
        {
            "name": "Admin - User Management",
            "description": "**Administrative user management** operations. Requires admin role for access. Includes user CRUD operations, activation/deactivation.",
            "externalDocs": {
                "description": "Admin Guide",
                "url": "https://docs.simbaai.com/admin"
            }
        },
        {
            "name": "Health & Monitoring",
            "description": "**Service health checks** and monitoring endpoints. Used for service discovery, load balancing, and system monitoring.",
            "externalDocs": {
                "description": "Monitoring Guide",
                "url": "https://docs.simbaai.com/monitoring"
            }
        }
    ]
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/health", tags=["System"])
async def health_check():
    """Health check endpoint for service discovery"""
    # Get current port from Vault or default
    current_port = get_vault_config("PORT", "7301")
    vault_status = "connected" if get_vault_config("VAULT_URL") else "disconnected"

    return {
        "status": "healthy",
        "service": "user-management",
        "port": int(current_port),
        "category": "core",
        "technology": "FastAPI + Keycloak + PostgreSQL",
        "vault_status": vault_status,
        "gpu_required": False,
        "configuration_source": "vault" if vault_status == "connected" else "environment"
    }

@app.get("/database/status", tags=["System"])
async def database_status():
    """Check database initialization status via PostgreSQL Cluster Service"""
    try:
        from app.services.cluster_db_init import check_database_status
        status = await check_database_status()
        return status
    except Exception as e:
        logger.error(f"❌ Error checking database status: {e}")
        return {
            "status": "error",
            "message": str(e),
            "cluster_service": False,
            "tables_exist": False,
            "roles_exist": False,
            "admin_exists": False
        }

@app.get("/", tags=["System"])
async def root():
    """Root endpoint"""
    from datetime import datetime, timezone

    # Get current port from Vault or environment
    current_port = get_vault_config("PORT", os.getenv("PORT", "7301"))

    return {
        "message": "Authentication system is working",
        "status": "healthy",
        "service": "user-management",
        "version": "2.0.0",
        "port": int(current_port),
        "features": {
            "authentication": True,
            "email_verification": True,
            "password_reset": True,
            "mfa_support": True,
            "oauth2_support": True,
            "admin_management": True
        },
        "endpoints": {
            "authentication": [
                "GET /auth/test",
                "POST /auth/register",
                "POST /auth/login",
                "POST /auth/logout",
                "GET /auth/me",
                "PUT /auth/me",
                "POST /auth/change-password"
            ],
            "email_verification": [
                "POST /auth/verify-email/request",
                "POST /auth/verify-email/confirm"
            ],
            "password_reset": [
                "POST /auth/password-reset/request",
                "POST /auth/password-reset/confirm"
            ],
            "admin": [
                "GET /auth/admin/users/",
                "POST /auth/admin/users/",
                "GET /auth/admin/users/{user_id}",
                "PUT /auth/admin/users/{user_id}",
                "DELETE /auth/admin/users/{user_id}",
                "POST /auth/admin/users/{user_id}/activate",
                "POST /auth/admin/users/{user_id}/deactivate"
            ]
        },
        "timestamp": datetime.now(timezone.utc).isoformat()
    }

# Import service-specific routes
try:
    from app.api import router
    app.include_router(router, prefix="/api/v1")
    logger.info(f"✅ Successfully included router with {len(router.routes)} routes")
except ImportError as e:
    logger.error(f"❌ Failed to import router: {e}")
except Exception as e:
    logger.error(f"❌ Failed to include router: {e}")
    import traceback
    traceback.print_exc()

# Startup and shutdown events
@app.on_event("startup")
async def startup_event():
    """Initialize Vault integration, database, and inter-service communication on startup"""
    try:
        await initialize_vault()
        logger.info("✅ Vault integration initialized successfully")
    except Exception as e:
        logger.warning(f"Vault initialization failed: {e}")

    # Initialize database via PostgreSQL Cluster Service (non-blocking)
    import asyncio

    async def initialize_database_background():
        """Initialize database in background with retries."""
        try:
            logger.info("🔧 Starting background database initialization via PostgreSQL Cluster Service...")
            from app.services.cluster_db_init import initialize_database_via_cluster

            # Retry database initialization with exponential backoff
            max_retries = 10
            base_delay = 2.0

            for attempt in range(max_retries):
                try:
                    logger.info(f"🔄 Database initialization attempt {attempt + 1}/{max_retries}...")
                    success = await initialize_database_via_cluster()
                    if success:
                        logger.info("✅ User management database initialized successfully via PostgreSQL Cluster Service")
                        return
                    else:
                        logger.warning(f"⚠️ Database initialization attempt {attempt + 1} failed")

                except Exception as e:
                    logger.warning(f"⚠️ Database initialization attempt {attempt + 1} failed: {e}")

                if attempt < max_retries - 1:
                    delay = base_delay * (2 ** attempt)  # Exponential backoff
                    logger.info(f"⏳ Waiting {delay}s before retry...")
                    await asyncio.sleep(delay)

            logger.error("❌ Database initialization failed after all retries")
            logger.warning("⚠️ Service will continue without database initialization")

        except Exception as e:
            logger.error(f"❌ Background database initialization failed: {e}")

    # Start database initialization in background
    asyncio.create_task(initialize_database_background())

    # Initialize inter-service communication (non-blocking)
    async def initialize_inter_service_background():
        """Initialize inter-service communication in background with retries."""
        try:
            logger.info("🔗 Starting background inter-service communication initialization...")
            from app.services.inter_service_client import initialize_user_management_client

            # Retry inter-service initialization with exponential backoff
            max_retries = 5
            base_delay = 3.0

            for attempt in range(max_retries):
                try:
                    logger.info(f"🔄 Inter-service initialization attempt {attempt + 1}/{max_retries}...")
                    await initialize_user_management_client()
                    logger.info("✅ Inter-service communication initialized successfully")
                    return

                except Exception as e:
                    logger.warning(f"⚠️ Inter-service initialization attempt {attempt + 1} failed: {e}")

                if attempt < max_retries - 1:
                    delay = base_delay * (2 ** attempt)  # Exponential backoff
                    logger.info(f"⏳ Waiting {delay}s before retry...")
                    await asyncio.sleep(delay)

            logger.error("❌ Inter-service communication initialization failed after all retries")
            logger.warning("⚠️ Service will continue without inter-service communication")

        except Exception as e:
            logger.error(f"❌ Background inter-service initialization failed: {e}")

    # Start inter-service initialization in background
    asyncio.create_task(initialize_inter_service_background())

if __name__ == "__main__":
    import uvicorn
    import os

    # Get port from environment or default (Vault will override during startup)
    port = int(os.getenv("PORT", "7301"))
    host = os.getenv("HOST", "0.0.0.0")

    uvicorn.run(app, host=host, port=port)
