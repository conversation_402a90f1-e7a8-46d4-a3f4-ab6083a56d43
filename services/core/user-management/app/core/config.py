"""
Core configuration for User Management Service with Vault Integration
"""
import os
from typing import Optional

def get_vault_secret(key: str, default: str = None) -> str:
    """Get secret from Vault with fallback to environment variables."""
    try:
        from app.startup.vault_startup import get_vault_secret as vault_get_secret
        return vault_get_secret(key, default)
    except ImportError:
        # Fallback to environment variables if Vault is not available
        return os.getenv(key, default)

def get_vault_config(key: str, default: str = None) -> str:
    """Get configuration from Vault with fallback to environment variables."""
    try:
        from app.startup.vault_startup import get_vault_config as vault_get_config
        return vault_get_config(key, default)
    except ImportError:
        # Fallback to environment variables if Vault is not available
        return os.getenv(key, default)

class Settings:
    """User Management service settings with Vault integration"""

    # API Configuration
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "SimbaAI User Management Service"

    # Service Configuration
    SERVICE_NAME: str = "user-management"
    SERVICE_PORT: int = int(get_vault_config("PORT", "7301"))

    # PostgreSQL Cluster Service Configuration - SECRETS FROM VAULT
    POSTGRESQL_CLUSTER_URL: str = get_vault_secret(
        "POSTGRESQL_CLUSTER_URL",
        "http://localhost:7233"
    )

    # Database Configuration (for PostgreSQL Cluster Service)
    DATABASE_NAME: str = get_vault_config("DATABASE_NAME", "simba_models_database")

    # Disable direct database connection - use PostgreSQL Cluster Service instead
    DATABASE_URL: str = "********************************************/disabled"

    # Redis for caching and sessions - SECRETS FROM VAULT
    REDIS_URL: str = get_vault_secret("REDIS_URL", "redis://localhost:6379/0")

    # MongoDB for additional data - SECRETS FROM VAULT
    MONGODB_URL: str = get_vault_secret(
        "MONGODB_URL",
        "*******************************************************"
    )
    MONGODB_DATABASE: str = get_vault_config("MONGODB_DATABASE", "simba_model_db")

    # Security Configuration - CRITICAL SECRETS FROM VAULT
    SECRET_KEY: str = get_vault_secret("SECRET_KEY", "your-secret-key-change-in-production")
    ALGORITHM: str = get_vault_config("ALGORITHM", "HS256")

    # Token Configuration
    ACCESS_TOKEN_EXPIRE_MINUTES: int = int(get_vault_config("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
    REFRESH_TOKEN_EXPIRE_DAYS: int = int(get_vault_config("REFRESH_TOKEN_EXPIRE_DAYS", "7"))

    # Email Configuration
    EMAIL_ENABLED: bool = get_vault_config("EMAIL_ENABLED", "false").lower() == "true"
    EMAIL_BACKEND: str = get_vault_config("EMAIL_BACKEND", "smtp")
    EMAIL_FROM: str = get_vault_config("EMAIL_FROM", "<EMAIL>")
    EMAIL_FROM_NAME: str = get_vault_config("EMAIL_FROM_NAME", "SimbaAI")

    # SMTP Configuration - SECRETS FROM VAULT
    SMTP_SERVER: str = get_vault_config("SMTP_SERVER", "localhost")
    SMTP_PORT: int = int(get_vault_config("SMTP_PORT", "587"))
    SMTP_USERNAME: Optional[str] = get_vault_secret("SMTP_USERNAME")
    SMTP_PASSWORD: Optional[str] = get_vault_secret("SMTP_PASSWORD")
    SMTP_USE_TLS: bool = get_vault_config("SMTP_USE_TLS", "true").lower() == "true"

    # Frontend Configuration
    FRONTEND_URL: str = os.getenv("FRONTEND_URL", "http://localhost:3000")

    # Vault Configuration
    VAULT_URL: str = os.getenv("VAULT_URL", "http://localhost:8200")
    VAULT_SERVICE_URL: str = os.getenv("VAULT_SERVICE_URL", "http://localhost:7200")

    # Environment
    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "development")
    DEBUG: bool = os.getenv("DEBUG", "false").lower() == "true"

    # Logging
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")

    # CORS Configuration
    ALLOWED_ORIGINS: list = [
        "http://localhost:3000",
        "http://localhost:3001",
        "http://localhost:8080",
        "https://simbaai.com",
        "https://app.simbaai.com"
    ]

    # Rate Limiting
    RATE_LIMIT_ENABLED: bool = os.getenv("RATE_LIMIT_ENABLED", "true").lower() == "true"
    RATE_LIMIT_REQUESTS: int = int(os.getenv("RATE_LIMIT_REQUESTS", "100"))
    RATE_LIMIT_WINDOW: int = int(os.getenv("RATE_LIMIT_WINDOW", "3600"))  # 1 hour

    # Password Policy
    MIN_PASSWORD_LENGTH: int = int(os.getenv("MIN_PASSWORD_LENGTH", "8"))
    REQUIRE_UPPERCASE: bool = os.getenv("REQUIRE_UPPERCASE", "true").lower() == "true"
    REQUIRE_LOWERCASE: bool = os.getenv("REQUIRE_LOWERCASE", "true").lower() == "true"
    REQUIRE_NUMBERS: bool = os.getenv("REQUIRE_NUMBERS", "true").lower() == "true"
    REQUIRE_SPECIAL_CHARS: bool = os.getenv("REQUIRE_SPECIAL_CHARS", "true").lower() == "true"

    # Account Security
    MAX_LOGIN_ATTEMPTS: int = int(os.getenv("MAX_LOGIN_ATTEMPTS", "5"))
    ACCOUNT_LOCKOUT_DURATION: int = int(os.getenv("ACCOUNT_LOCKOUT_DURATION", "900"))  # 15 minutes

    # Email Verification
    EMAIL_VERIFICATION_EXPIRE_HOURS: int = int(os.getenv("EMAIL_VERIFICATION_EXPIRE_HOURS", "24"))
    PASSWORD_RESET_EXPIRE_HOURS: int = int(os.getenv("PASSWORD_RESET_EXPIRE_HOURS", "1"))

    # Admin Configuration
    ADMIN_EMAIL: str = os.getenv("ADMIN_EMAIL", "<EMAIL>")
    SUPPORT_EMAIL: str = os.getenv("SUPPORT_EMAIL", "<EMAIL>")

    # Feature Flags
    ENABLE_REGISTRATION: bool = os.getenv("ENABLE_REGISTRATION", "true").lower() == "true"
    ENABLE_EMAIL_VERIFICATION: bool = os.getenv("ENABLE_EMAIL_VERIFICATION", "false").lower() == "true"
    ENABLE_PASSWORD_RESET: bool = os.getenv("ENABLE_PASSWORD_RESET", "true").lower() == "true"
    ENABLE_MFA: bool = os.getenv("ENABLE_MFA", "false").lower() == "true"

    # OAuth2 Configuration - SECRETS FROM VAULT
    GOOGLE_CLIENT_ID: Optional[str] = get_vault_config("GOOGLE_CLIENT_ID")
    GOOGLE_CLIENT_SECRET: Optional[str] = get_vault_secret("GOOGLE_CLIENT_SECRET")
    GITHUB_CLIENT_ID: Optional[str] = get_vault_config("GITHUB_CLIENT_ID")
    GITHUB_CLIENT_SECRET: Optional[str] = get_vault_secret("GITHUB_CLIENT_SECRET")

    # Monitoring
    ENABLE_METRICS: bool = os.getenv("ENABLE_METRICS", "true").lower() == "true"
    METRICS_PORT: int = int(os.getenv("METRICS_PORT", "9090"))

    # Health Check
    HEALTH_CHECK_TIMEOUT: int = int(os.getenv("HEALTH_CHECK_TIMEOUT", "30"))

    class Config:
        case_sensitive = True

# Create settings instance
settings = Settings()
