"""
Vault HTTP Client for Microservices

HTTP-based client for microservices to get secrets and configuration from vault-consul service.
Replaces shared imports with proper API-based communication.
"""

import httpx
import os
import asyncio
from typing import Dict, Any, Optional
from loguru import logger
from datetime import datetime, timedelta
import json


class VaultHTTPClient:
    """
    HTTP client for microservices to communicate with vault-consul service.
    
    This replaces the shared import pattern with proper API calls.
    """
    
    def __init__(self, service_name: str, vault_consul_url: str = None):
        self.service_name = service_name
        self.vault_consul_url = vault_consul_url or os.getenv(
            "VAULT_CONSUL_URL", 
            "http://vault-consul-service:8200"
        )
        self.service_token = os.getenv("INTERNAL_SERVICE_TOKEN", "simbaai-internal-token")
        self.timeout = 30.0
        
        # Cache for configuration
        self._config_cache = {}
        self._cache_ttl = 300  # 5 minutes
        self._last_cache_time = None
        
    async def initialize(self) -> bool:
        """Initialize the client and test connection."""
        try:
            logger.info(f"🔐 Initializing Vault HTTP client for {self.service_name}")
            
            # Test connection to vault-consul service
            health_check = await self.health_check()
            
            if health_check:
                logger.info(f"✅ Vault HTTP client initialized for {self.service_name}")
                return True
            else:
                logger.warning(f"⚠️ Vault-consul service not available for {self.service_name}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Failed to initialize Vault HTTP client: {e}")
            return False
    
    async def health_check(self) -> bool:
        """Check if vault-consul service is healthy."""
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(f"{self.vault_consul_url}/api/v1/secrets/health")
                return response.status_code == 200
        except Exception:
            return False
    
    async def get_all_config(self, force_refresh: bool = False) -> Dict[str, Any]:
        """
        Get all configuration and secrets for the service.
        
        Args:
            force_refresh: Force refresh from Vault (skip cache)
            
        Returns:
            Complete service configuration
        """
        # Check cache first
        if not force_refresh and self._is_cache_valid():
            logger.debug(f"Returning cached config for {self.service_name}")
            return self._config_cache
        
        try:
            headers = {"X-Service-Token": self.service_token}
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(
                    f"{self.vault_consul_url}/api/v1/secrets/{self.service_name}",
                    headers=headers
                )
                
                if response.status_code == 200:
                    result = response.json()
                    config = result.get("config", {})
                    
                    # Cache the result
                    self._config_cache = config
                    self._last_cache_time = datetime.now()
                    
                    logger.info(f"✅ Retrieved {len(config)} config items for {self.service_name}")
                    return config
                else:
                    logger.error(f"❌ Failed to get config: {response.status_code} - {response.text}")
                    return {}
                    
        except Exception as e:
            logger.error(f"❌ Error getting config for {self.service_name}: {e}")
            return {}
    
    async def get_config(self, key: str, default: Any = None) -> Any:
        """
        Get a specific configuration value.
        
        Args:
            key: Configuration key
            default: Default value if not found
            
        Returns:
            Configuration value
        """
        try:
            config = await self.get_all_config()
            return config.get(key, default)
        except Exception as e:
            logger.error(f"❌ Error getting config key '{key}': {e}")
            return default
    
    async def get_secret(self, key: str, default: str = None) -> Optional[str]:
        """
        Get a specific secret value.
        
        Args:
            key: Secret key
            default: Default value if not found
            
        Returns:
            Secret value
        """
        try:
            headers = {"X-Service-Token": self.service_token}
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(
                    f"{self.vault_consul_url}/api/v1/secrets/{self.service_name}/{key}",
                    headers=headers
                )
                
                if response.status_code == 200:
                    result = response.json()
                    return result.get("value", default)
                elif response.status_code == 404:
                    logger.debug(f"Secret '{key}' not found for {self.service_name}")
                    return default
                else:
                    logger.error(f"❌ Failed to get secret '{key}': {response.status_code}")
                    return default
                    
        except Exception as e:
            logger.error(f"❌ Error getting secret '{key}': {e}")
            return default
    
    async def update_secrets(self, secrets: Dict[str, Any]) -> bool:
        """
        Update secrets for the service.
        
        Args:
            secrets: Dictionary of secrets to update
            
        Returns:
            Success status
        """
        try:
            headers = {
                "X-Service-Token": self.service_token,
                "Content-Type": "application/json"
            }
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    f"{self.vault_consul_url}/api/v1/secrets/{self.service_name}",
                    headers=headers,
                    json=secrets
                )
                
                if response.status_code == 200:
                    logger.info(f"✅ Updated {len(secrets)} secrets for {self.service_name}")
                    # Clear cache to force refresh
                    self._config_cache.clear()
                    self._last_cache_time = None
                    return True
                else:
                    logger.error(f"❌ Failed to update secrets: {response.status_code}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Error updating secrets: {e}")
            return False
    
    async def get_non_secret_config(self) -> Dict[str, Any]:
        """
        Get non-secret configuration only.
        
        Returns:
            Non-sensitive configuration
        """
        try:
            headers = {"X-Service-Token": self.service_token}
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(
                    f"{self.vault_consul_url}/api/v1/config/{self.service_name}",
                    headers=headers
                )
                
                if response.status_code == 200:
                    result = response.json()
                    return result.get("config", {})
                else:
                    logger.error(f"❌ Failed to get config: {response.status_code}")
                    return {}
                    
        except Exception as e:
            logger.error(f"❌ Error getting non-secret config: {e}")
            return {}
    
    def _is_cache_valid(self) -> bool:
        """Check if cache is still valid."""
        if not self._config_cache or not self._last_cache_time:
            return False
        
        cache_age = datetime.now() - self._last_cache_time
        return cache_age < timedelta(seconds=self._cache_ttl)
    
    def clear_cache(self):
        """Clear the configuration cache."""
        self._config_cache.clear()
        self._last_cache_time = None
        logger.debug(f"Cleared config cache for {self.service_name}")
    
    async def inject_config_to_env(self):
        """
        Inject configuration into environment variables for legacy compatibility.
        
        This allows existing code that uses os.getenv() to work without changes.
        """
        try:
            config = await self.get_all_config()
            
            for key, value in config.items():
                if value is not None:
                    os.environ[key] = str(value)
            
            logger.info(f"✅ Injected {len(config)} config items into environment for {self.service_name}")
            
        except Exception as e:
            logger.error(f"❌ Error injecting config to environment: {e}")


# Convenience functions for easy migration
async def get_vault_config(service_name: str, key: str, default: Any = None) -> Any:
    """
    Convenience function to get configuration value.
    
    Args:
        service_name: Name of the service
        key: Configuration key
        default: Default value
        
    Returns:
        Configuration value
    """
    client = VaultHTTPClient(service_name)
    await client.initialize()
    return await client.get_config(key, default)


async def get_vault_secret(service_name: str, key: str, default: str = None) -> Optional[str]:
    """
    Convenience function to get secret value.
    
    Args:
        service_name: Name of the service
        key: Secret key
        default: Default value
        
    Returns:
        Secret value
    """
    client = VaultHTTPClient(service_name)
    await client.initialize()
    return await client.get_secret(key, default)


async def initialize_vault_for_service(service_name: str) -> bool:
    """
    Initialize Vault integration for a service and inject config to environment.
    
    Args:
        service_name: Name of the service
        
    Returns:
        Success status
    """
    try:
        client = VaultHTTPClient(service_name)
        success = await client.initialize()
        
        if success:
            # Inject configuration into environment for legacy compatibility
            await client.inject_config_to_env()
            logger.info(f"✅ Vault integration initialized for {service_name}")
        else:
            logger.warning(f"⚠️ Vault not available for {service_name}, using environment variables")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize Vault for {service_name}: {e}")
        return False
