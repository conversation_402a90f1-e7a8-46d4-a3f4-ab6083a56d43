"""
Vault Integration for user-management

This module handles Vault-Consul integration for secure configuration management.
"""

import os
import asyncio
from typing import Dict, Any, Optional
from loguru import logger

from app.client.vault_http_client import VaultHTTPClient, initialize_vault_for_service


class UsermanagementVaultIntegration:
    """
    Vault integration for user-management.
    
    Handles secure configuration and secrets management.
    """
    
    def __init__(self):
        self.service_name = "user-management"
        self.vault_client = VaultHTTPClient(self.service_name)
        self._initialized = False
    
    async def initialize(self) -> bool:
        """Initialize vault integration."""
        try:
            logger.info(f"🔐 Initializing Vault integration for {self.service_name}")
            
            # Initialize vault client
            success = await self.vault_client.initialize()
            
            if success:
                # Inject configuration into environment for legacy compatibility
                await self.vault_client.inject_config_to_env()
                self._initialized = True
                logger.info(f"✅ Vault integration initialized for {self.service_name}")
            else:
                logger.warning(f"⚠️ Vault not available for {self.service_name}, using environment variables")
                self._initialized = False
            
            return success
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Vault integration: {e}")
            self._initialized = False
            return False
    
    async def get_config(self, key: str, default: Any = None) -> Any:
        """Get configuration value from Vault or environment."""
        if self._initialized:
            return await self.vault_client.get_config(key, default)
        else:
            # Fallback to environment variables
            return os.getenv(key, default)
    
    async def get_secret(self, key: str, default: str = None) -> Optional[str]:
        """Get secret value from Vault or environment."""
        if self._initialized:
            return await self.vault_client.get_secret(key, default)
        else:
            # Fallback to environment variables
            return os.getenv(key, default)
    
    async def get_all_config(self) -> Dict[str, Any]:
        """Get all configuration from Vault."""
        if self._initialized:
            return await self.vault_client.get_all_config()
        else:
            # Return environment variables as fallback
            return dict(os.environ)
    
    async def update_secrets(self, secrets: Dict[str, Any]) -> bool:
        """Update secrets in Vault."""
        if self._initialized:
            return await self.vault_client.update_secrets(secrets)
        else:
            logger.warning("Vault not initialized, cannot update secrets")
            return False
    
    def is_initialized(self) -> bool:
        """Check if vault integration is initialized."""
        return self._initialized


# Global instance for easy access
vault_integration = UsermanagementVaultIntegration()


# Convenience functions for easy migration
async def get_config(key: str, default: Any = None) -> Any:
    """Get configuration value."""
    return await vault_integration.get_config(key, default)


async def get_secret(key: str, default: str = None) -> Optional[str]:
    """Get secret value."""
    return await vault_integration.get_secret(key, default)


async def initialize_vault() -> bool:
    """Initialize vault integration for this service."""
    return await vault_integration.initialize()
