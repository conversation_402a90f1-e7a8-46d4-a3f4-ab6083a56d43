"""
PostgreSQL Cluster Service Client

Handles communication with the PostgreSQL Cluster microservice.
"""

import httpx
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import json

from app.core.config import settings

logger = logging.getLogger(__name__)


class PostgreSQLClusterClient:
    """Client for communicating with PostgreSQL Cluster Service."""

    def __init__(self):
        self.base_url = settings.POSTGRESQL_CLUSTER_URL
        self.service_token = "simbaai-internal-token"  # Should come from Vault
        self.timeout = 30.0

    async def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None
    ) -> Optional[Dict[str, Any]]:
        """Make HTTP request to PostgreSQL Cluster Service."""
        try:
            headers = {
                "X-Service-Token": self.service_token,
                "Content-Type": "application/json"
            }

            url = f"{self.base_url}{endpoint}"

            async with httpx.AsyncClient(timeout=self.timeout) as client:
                if method.upper() == "GET":
                    response = await client.get(url, headers=headers)
                elif method.upper() == "POST":
                    response = await client.post(url, headers=headers, json=data)
                elif method.upper() == "PUT":
                    response = await client.put(url, headers=headers, json=data)
                elif method.upper() == "DELETE":
                    response = await client.delete(url, headers=headers, json=data)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")

                if response.status_code == 200:
                    return response.json()
                else:
                    # Enhanced error handling - return error details for better handling
                    error_detail = None
                    try:
                        error_response = response.json()
                        error_detail = error_response.get("detail", response.text)
                    except:
                        error_detail = response.text

                    logger.error(f"PostgreSQL Cluster request failed: {response.status_code} - {error_detail}")

                    # Return error information instead of None for better error handling
                    return {
                        "success": False,
                        "error": True,
                        "status_code": response.status_code,
                        "error_detail": error_detail,
                        "is_duplicate_key": self._is_duplicate_key_error(error_detail)
                    }

        except Exception as e:
            logger.error(f"Error communicating with PostgreSQL Cluster: {e}")
            return {
                "success": False,
                "error": True,
                "status_code": None,
                "error_detail": str(e),
                "is_duplicate_key": False
            }

    def _is_duplicate_key_error(self, error_detail: str) -> bool:
        """Check if the error is a duplicate key constraint violation."""
        if not error_detail:
            return False

        error_lower = error_detail.lower()
        duplicate_indicators = [
            "duplicate key value violates unique constraint",
            "unique constraint",
            "already exists",
            "duplicate entry"
        ]

        return any(indicator in error_lower for indicator in duplicate_indicators)

    # Database Operations
    async def execute_query(self, query: str, params: List[Any] = None) -> Optional[Dict[str, Any]]:
        """Execute a raw SQL query."""
        data = {
            "query": query,
            "params": params or []
        }
        return await self._make_request("POST", "/api/v1/query/execute", data)

    # Table Operations
    async def create_table(
        self,
        table_name: str,
        columns: List[Dict[str, str]],
        if_not_exists: bool = True
    ) -> Optional[Dict[str, Any]]:
        """Create a table."""
        data = {
            "table_name": table_name,
            "columns": columns,
            "if_not_exists": if_not_exists
        }
        return await self._make_request("POST", "/api/v1/tables/create", data)

    async def list_tables(self) -> Optional[Dict[str, Any]]:
        """List all tables."""
        return await self._make_request("GET", "/api/v1/tables")

    # Record Operations
    async def insert_record(
        self,
        table_name: str,
        data: Dict[str, Any],
        returning: List[str] = None
    ) -> Optional[Dict[str, Any]]:
        """Insert a record."""
        request_data = {
            "table_name": table_name,
            "data": data,
            "returning": returning or ["id"]
        }
        return await self._make_request("POST", "/api/v1/records/insert", request_data)

    async def select_records(
        self,
        table_name: str,
        columns: List[str] = None,
        where_clause: str = None,
        params: List[Any] = None,
        order_by: str = None,
        limit: int = None,
        offset: int = None
    ) -> Optional[Dict[str, Any]]:
        """Select records."""
        data = {
            "table_name": table_name,
            "columns": columns or ["*"],
            "where_clause": where_clause,
            "params": params or [],
            "order_by": order_by,
            "limit": limit,
            "offset": offset
        }
        return await self._make_request("POST", "/api/v1/records/select", data)

    async def update_record(
        self,
        table_name: str,
        data: Dict[str, Any],
        where_clause: str,
        params: List[Any] = None,
        returning: List[str] = None
    ) -> Optional[Dict[str, Any]]:
        """Update records."""
        request_data = {
            "table_name": table_name,
            "data": data,
            "where_clause": where_clause,
            "params": params or [],
            "returning": returning or ["id"]
        }
        return await self._make_request("PUT", "/api/v1/records/update", request_data)

    async def delete_record(
        self,
        table_name: str,
        where_clause: str,
        params: List[Any] = None,
        returning: List[str] = None
    ) -> Optional[Dict[str, Any]]:
        """Delete records."""
        data = {
            "table_name": table_name,
            "where_clause": where_clause,
            "params": params or [],
            "returning": returning or ["id"]
        }
        return await self._make_request("DELETE", "/api/v1/records/delete", data)

    # User-specific Operations
    async def create_user(
        self,
        username: str,
        email: str,
        password_hash: str,
        metadata: Dict[str, Any] = None
    ) -> Optional[Dict[str, Any]]:
        """Create a user via PostgreSQL Cluster Service."""
        data = {
            "username": username,
            "email": email,
            "password_hash": password_hash,
            "metadata": metadata or {}
        }
        return await self._make_request("POST", "/api/v1/users/create", data)

    async def get_user(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Get user by ID."""
        return await self._make_request("GET", f"/api/v1/users/{user_id}")

    async def update_user(self, user_id: int, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update user."""
        request_data = {
            "user_id": user_id,
            "data": data
        }
        return await self._make_request("PUT", "/api/v1/users/update", request_data)

    async def list_users(self, limit: int = 100, offset: int = 0) -> Optional[Dict[str, Any]]:
        """List users."""
        return await self._make_request("GET", f"/api/v1/users?limit={limit}&offset={offset}")

    async def authenticate_user(
        self,
        username: str = None,
        email: str = None,
        password: str = None
    ) -> Optional[Dict[str, Any]]:
        """Authenticate user via PostgreSQL Cluster Service."""
        data = {
            "username": username,
            "email": email,
            "password": password
        }
        return await self._make_request("POST", "/api/v1/users/authenticate", data)

    async def update_user_login(
        self,
        user_id: str,
        success: bool
    ) -> Optional[Dict[str, Any]]:
        """Update user login attempt."""
        endpoint = f"/api/v1/users/update-login?user_id={user_id}&success={success}"
        return await self._make_request("POST", endpoint)

    # Database Initialization
    async def initialize_user_tables(self) -> bool:
        """Initialize all user management tables."""
        try:
            logger.info("🏗️ Initializing user management tables...")

            # Create users table
            users_result = await self.create_table(
                table_name="users",
                columns=[
                    {"name": "id", "type": "SERIAL PRIMARY KEY"},
                    {"name": "username", "type": "VARCHAR(50) UNIQUE NOT NULL"},
                    {"name": "email", "type": "VARCHAR(255) UNIQUE NOT NULL"},
                    {"name": "password_hash", "type": "TEXT NOT NULL"},
                    {"name": "first_name", "type": "VARCHAR(100)"},
                    {"name": "last_name", "type": "VARCHAR(100)"},
                    {"name": "is_active", "type": "BOOLEAN DEFAULT TRUE"},
                    {"name": "is_verified", "type": "BOOLEAN DEFAULT FALSE"},
                    {"name": "status", "type": "VARCHAR(20) DEFAULT 'PENDING'"},
                    {"name": "metadata", "type": "JSONB DEFAULT '{}'"},
                    {"name": "created_at", "type": "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"},
                    {"name": "updated_at", "type": "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"}
                ]
            )

            # Create user_profiles table
            profiles_result = await self.create_table(
                table_name="user_profiles",
                columns=[
                    {"name": "id", "type": "SERIAL PRIMARY KEY"},
                    {"name": "user_id", "type": "INTEGER REFERENCES users(id) ON DELETE CASCADE"},
                    {"name": "avatar_url", "type": "TEXT"},
                    {"name": "bio", "type": "TEXT"},
                    {"name": "phone", "type": "VARCHAR(20)"},
                    {"name": "timezone", "type": "VARCHAR(50)"},
                    {"name": "language", "type": "VARCHAR(10) DEFAULT 'en'"},
                    {"name": "preferences", "type": "JSONB DEFAULT '{}'"},
                    {"name": "created_at", "type": "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"},
                    {"name": "updated_at", "type": "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"}
                ]
            )

            # Create roles table
            roles_result = await self.create_table(
                table_name="roles",
                columns=[
                    {"name": "id", "type": "SERIAL PRIMARY KEY"},
                    {"name": "name", "type": "VARCHAR(50) UNIQUE NOT NULL"},
                    {"name": "description", "type": "TEXT"},
                    {"name": "permissions", "type": "JSONB DEFAULT '[]'"},
                    {"name": "created_at", "type": "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"}
                ]
            )

            # Create user_roles table
            user_roles_result = await self.create_table(
                table_name="user_roles",
                columns=[
                    {"name": "id", "type": "SERIAL PRIMARY KEY"},
                    {"name": "user_id", "type": "INTEGER REFERENCES users(id) ON DELETE CASCADE"},
                    {"name": "role_id", "type": "INTEGER REFERENCES roles(id) ON DELETE CASCADE"},
                    {"name": "assigned_at", "type": "TIMESTAMP DEFAULT CURRENT_TIMESTAMP"},
                    {"name": "assigned_by", "type": "INTEGER REFERENCES users(id)"}
                ]
            )

            if all([users_result, profiles_result, roles_result, user_roles_result]):
                logger.info("✅ User management tables initialized successfully")
                return True
            else:
                logger.error("❌ Failed to initialize some tables")
                return False

        except Exception as e:
            logger.error(f"❌ Error initializing user tables: {e}")
            return False

    # Health Check
    async def health_check(self, max_retries: int = 5, retry_delay: float = 2.0) -> bool:
        """Check if PostgreSQL Cluster Service is healthy with retry logic."""
        import asyncio

        for attempt in range(max_retries):
            try:
                logger.info(f"🔍 Health check attempt {attempt + 1}/{max_retries} for PostgreSQL Cluster Service...")
                response = await self._make_request("GET", "/health")

                if response is not None and response.get("status") == "healthy":
                    logger.info("✅ PostgreSQL Cluster Service is healthy")
                    return True
                else:
                    logger.warning(f"⚠️ PostgreSQL Cluster Service not healthy: {response}")

            except Exception as e:
                logger.warning(f"⚠️ Health check attempt {attempt + 1} failed: {e}")

            if attempt < max_retries - 1:
                logger.info(f"⏳ Waiting {retry_delay}s before retry...")
                await asyncio.sleep(retry_delay)

        logger.error(f"❌ PostgreSQL Cluster health check failed after {max_retries} attempts")
        return False


# Global client instance
postgresql_client = PostgreSQLClusterClient()
