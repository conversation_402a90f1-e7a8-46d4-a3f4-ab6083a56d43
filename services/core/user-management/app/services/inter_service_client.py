"""
Inter-Service Client for User Management Service

Handles communication with other microservices for user management operations.
Includes billing integration, email services, and audit logging.
"""

import httpx
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime
from loguru import logger
import os

# Import the shared inter-service client
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent.parent.parent / "shared-services" / "service-registry" / "app"))
from inter_service_client import InterServiceClient


class UserManagementServiceClient:
    """
    Inter-service client specifically for user management service operations.

    Provides high-level methods for user-related service communication.
    """

    def __init__(self):
        self.client = InterServiceClient("user-management")
        self._initialized = False

    async def initialize(self) -> bool:
        """Initialize the user management service client."""
        try:
            # Register user management service with registry
            await self._register_user_management_service()
            self._initialized = True
            logger.info("✅ User management service client initialized")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to initialize user management service client: {e}")
            return False

    async def _register_user_management_service(self):
        """Register user management service with the service registry."""
        try:
            registration_data = {
                "name": "user-management",
                "url": "http://user-management:7301",
                "port": 7301,
                "health_endpoint": "/health",
                "capabilities": [
                    "user_authentication",
                    "user_registration",
                    "profile_management",
                    "role_management",
                    "permission_management",
                    "password_management",
                    "email_verification",
                    "oauth_integration"
                ],
                "metadata": {
                    "version": "1.0.0",
                    "supported_auth": ["jwt", "oauth2", "api_key"],
                    "oauth_providers": ["google", "github", "apple"],
                    "features": [
                        "multi_factor_auth",
                        "role_based_access",
                        "email_verification",
                        "password_reset",
                        "profile_management",
                        "admin_dashboard"
                    ]
                }
            }

            # Register with service registry
            registry_url = os.getenv("SERVICE_REGISTRY_URL", "http://localhost:7010")
            headers = {"X-Service-Token": "simbaai-internal-token"}

            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.post(
                    f"{registry_url}/api/v1/registry/register",
                    json=registration_data,
                    headers=headers
                )

                if response.status_code == 200:
                    logger.info("✅ User management service registered with service registry")
                else:
                    logger.warning(f"⚠️ Failed to register with service registry: {response.status_code}")

        except Exception as e:
            logger.error(f"❌ Service registration failed: {e}")

    # Billing Service Integration
    async def create_billing_profile(self, user_id: str, user_data: Dict[str, Any]) -> bool:
        """Create billing profile when user registers."""
        try:
            billing_data = {
                "user_id": user_id,
                "email": user_data.get("email"),
                "first_name": user_data.get("first_name"),
                "last_name": user_data.get("last_name"),
                "plan": "free",  # Default plan
                "currency": "USD",  # Default currency
                "metadata": {
                    "created_from": "user_registration",
                    "registration_date": datetime.now().isoformat()
                }
            }

            response = await self.client.call_service(
                service_name="billing",
                endpoint="/api/v1/billing/profiles",
                method="POST",
                data=billing_data,
                timeout=15.0
            )

            if response and response.get("success"):
                logger.info(f"✅ Created billing profile for user {user_id}")
                return True
            else:
                logger.warning(f"⚠️ Failed to create billing profile for user {user_id}")
                return False

        except Exception as e:
            logger.error(f"❌ Failed to create billing profile for user {user_id}: {e}")
            return False

    async def update_billing_profile(self, user_id: str, updates: Dict[str, Any]) -> bool:
        """Update billing profile when user details change."""
        try:
            response = await self.client.call_service(
                service_name="billing",
                endpoint=f"/api/v1/billing/profiles/{user_id}",
                method="PUT",
                data=updates,
                timeout=10.0
            )

            return response is not None and response.get("success", False)

        except Exception as e:
            logger.error(f"❌ Failed to update billing profile for user {user_id}: {e}")
            return False

    async def delete_billing_profile(self, user_id: str) -> bool:
        """Delete billing profile when user is deleted."""
        try:
            response = await self.client.call_service(
                service_name="billing",
                endpoint=f"/api/v1/billing/profiles/{user_id}",
                method="DELETE",
                timeout=10.0
            )

            return response is not None and response.get("success", False)

        except Exception as e:
            logger.error(f"❌ Failed to delete billing profile for user {user_id}: {e}")
            return False

    # Email Service Integration
    async def send_welcome_email(self, user_email: str, user_data: Dict[str, Any]) -> bool:
        """Send welcome email to new user."""
        try:
            email_data = {
                "to": user_email,
                "template": "welcome",
                "data": {
                    "first_name": user_data.get("first_name", ""),
                    "username": user_data.get("username", ""),
                    "login_url": "https://simba-ai.com/login",
                    "support_email": "<EMAIL>"
                }
            }

            response = await self.client.call_service(
                service_name="email-service",
                endpoint="/api/v1/email/send",
                method="POST",
                data=email_data,
                timeout=15.0
            )

            return response is not None and response.get("success", False)

        except Exception as e:
            logger.error(f"❌ Failed to send welcome email to {user_email}: {e}")
            return False

    async def send_verification_email(
        self,
        user_email: str,
        verification_token: str,
        user_name: str = ""
    ) -> bool:
        """Send email verification email."""
        try:
            verification_url = f"https://simba-ai.com/verify-email?token={verification_token}"

            email_data = {
                "to": user_email,
                "template": "email_verification",
                "data": {
                    "first_name": user_name,
                    "verification_url": verification_url,
                    "support_email": "<EMAIL>"
                }
            }

            response = await self.client.call_service(
                service_name="email-service",
                endpoint="/api/v1/email/send",
                method="POST",
                data=email_data,
                timeout=15.0
            )

            return response is not None and response.get("success", False)

        except Exception as e:
            logger.error(f"❌ Failed to send verification email to {user_email}: {e}")
            return False

    async def send_password_reset_email(
        self,
        user_email: str,
        reset_token: str,
        user_name: str = ""
    ) -> bool:
        """Send password reset email."""
        try:
            reset_url = f"https://simba-ai.com/reset-password?token={reset_token}"

            email_data = {
                "to": user_email,
                "template": "password_reset",
                "data": {
                    "first_name": user_name,
                    "reset_url": reset_url,
                    "support_email": "<EMAIL>"
                }
            }

            response = await self.client.call_service(
                service_name="email-service",
                endpoint="/api/v1/email/send",
                method="POST",
                data=email_data,
                timeout=15.0
            )

            return response is not None and response.get("success", False)

        except Exception as e:
            logger.error(f"❌ Failed to send password reset email to {user_email}: {e}")
            return False

    # Audit Service Integration
    async def log_user_activity(
        self,
        user_id: str,
        action: str,
        details: Dict[str, Any],
        ip_address: str = None
    ) -> bool:
        """Log user activity for audit purposes."""
        try:
            audit_data = {
                "user_id": user_id,
                "service": "user-management",
                "action": action,
                "details": details,
                "ip_address": ip_address,
                "timestamp": datetime.now().isoformat()
            }

            response = await self.client.call_service(
                service_name="audit-service",
                endpoint="/api/v1/audit/log",
                method="POST",
                data=audit_data,
                timeout=10.0
            )

            return response is not None and response.get("success", False)

        except Exception as e:
            logger.error(f"❌ Failed to log user activity: {e}")
            return False

    # Chat Service Integration
    async def notify_chat_service_user_created(self, user_id: str, user_data: Dict[str, Any]) -> bool:
        """Notify chat service when a new user is created."""
        try:
            chat_user_data = {
                "user_id": user_id,
                "email": user_data.get("email"),
                "username": user_data.get("username"),
                "preferences": {
                    "default_model": "gpt-3.5-turbo",
                    "max_tokens": 1000,
                    "temperature": 0.7
                }
            }

            response = await self.client.call_service(
                service_name="chat-orchestrator",
                endpoint="/api/v1/users",
                method="POST",
                data=chat_user_data,
                timeout=10.0
            )

            return response is not None and response.get("success", False)

        except Exception as e:
            logger.error(f"❌ Failed to notify chat service of user creation: {e}")
            return False

    # OAuth Integration
    async def validate_oauth_token(self, provider: str, token: str) -> Optional[Dict[str, Any]]:
        """Validate OAuth token with external provider."""
        try:
            validation_data = {
                "provider": provider,
                "token": token
            }

            response = await self.client.call_service(
                service_name="oauth-service",
                endpoint="/api/v1/oauth/validate",
                method="POST",
                data=validation_data,
                timeout=10.0
            )

            return response

        except Exception as e:
            logger.error(f"❌ Failed to validate OAuth token: {e}")
            return None

    # Service Health Monitoring
    async def check_dependent_services(self) -> Dict[str, bool]:
        """Check health of services that user management depends on."""
        services_to_check = [
            "billing",
            "email-service",
            "audit-service",
            "oauth-service",
            "vault-consul"
        ]

        health_status = {}

        for service in services_to_check:
            try:
                is_healthy = await self.client.health_check_service(service)
                health_status[service] = is_healthy
            except Exception as e:
                logger.error(f"❌ Health check failed for {service}: {e}")
                health_status[service] = False

        return health_status

    async def get_user_service_permissions(self, user_id: str) -> Dict[str, List[str]]:
        """Get user permissions across all services."""
        try:
            # This would aggregate permissions from various services
            permissions = {
                "billing": ["view_invoices", "manage_payment_methods"],
                "chat": ["create_conversations", "view_history"],
                "admin": []  # Would be populated based on user roles
            }

            return permissions

        except Exception as e:
            logger.error(f"❌ Failed to get user service permissions: {e}")
            return {}


# Global user management service client instance
user_management_service_client = UserManagementServiceClient()


# Convenience functions
async def initialize_user_management_client() -> bool:
    """Initialize the user management service client."""
    return await user_management_service_client.initialize()


async def create_user_billing_profile(user_id: str, user_data: Dict[str, Any]) -> bool:
    """Create billing profile for new user."""
    return await user_management_service_client.create_billing_profile(user_id, user_data)


async def send_user_welcome_email(user_email: str, user_data: Dict[str, Any]) -> bool:
    """Send welcome email to new user."""
    return await user_management_service_client.send_welcome_email(user_email, user_data)


async def log_user_action(user_id: str, action: str, details: Dict[str, Any]) -> bool:
    """Log user activity."""
    return await user_management_service_client.log_user_activity(user_id, action, details)
