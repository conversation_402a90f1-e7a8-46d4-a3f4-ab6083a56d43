"""
Production Authentication Dependencies for User Management Service

Provides authentication and authorization for all endpoints with support for:
- JWT tokens
- Role-based access control
- Permission checking
- User status validation
"""

from typing import Optional, List, Union
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2<PERSON>asswordBearer
from sqlalchemy.orm import Session
from jose import JWTError, jwt
from datetime import datetime
import logging

from app.services.business.user_management.db import get_db
from app.services.business.user_management.db.models import User
from app.services.business.user_management.services.auth_service import AuthService
from app.services.business.user_management.services.token_blacklist_service import token_blacklist_service

logger = logging.getLogger(__name__)

# OAuth2 scheme for token authentication
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/v1/auth/login")


async def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
) -> User:
    """
    Get current authenticated user from JWT token with blacklist checking.

    This is the main authentication dependency that should be used
    across all endpoints that require authentication.

    Returns:
        User: Authenticated user

    Raises:
        HTTPException: If token is invalid, expired, or blacklisted
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    blacklisted_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Token has been invalidated",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        # First check if token is blacklisted
        is_blacklisted = await token_blacklist_service.is_token_blacklisted(token)
        if is_blacklisted:
            logger.warning("Attempted use of blacklisted token")
            raise blacklisted_exception

        # Decode JWT token
        payload = AuthService.decode_access_token(token)
        user_id: str = payload.get("sub")
        token_type: str = payload.get("type")

        if user_id is None or token_type != "access":
            raise credentials_exception

    except JWTError as e:
        logger.error(f"JWT validation error: {e}")
        raise credentials_exception

    # Get user from database
    user = db.query(User).filter(User.id == user_id).first()
    if user is None:
        logger.error(f"User not found: {user_id}")
        raise credentials_exception

    # Check if user is active
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User account is inactive"
        )

    return user


async def get_current_active_user(
    user: User = Depends(get_current_user)
) -> User:
    """
    Get current active user.

    Args:
        user: Current user from authentication

    Returns:
        User: Active user

    Raises:
        HTTPException: If user is not active
    """
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User account is inactive"
        )
    return user


async def get_current_verified_user(
    user: User = Depends(get_current_active_user)
) -> User:
    """
    Get current verified user.

    Args:
        user: Current active user

    Returns:
        User: Verified user

    Raises:
        HTTPException: If user email is not verified
    """
    if hasattr(user, 'is_verified') and not user.is_verified:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Email verification required"
        )
    return user


def require_role(required_roles: Union[str, List[str]]):
    """
    Dependency factory to require specific user roles.

    Args:
        required_roles: Single role or list of roles

    Returns:
        Dependency function
    """
    if isinstance(required_roles, str):
        required_roles = [required_roles]

    async def check_role(
        user: User = Depends(get_current_verified_user)
    ) -> User:
        user_roles = []
        if hasattr(user, 'roles') and user.roles:
            user_roles = [role.name for role in user.roles]

        if not any(role in user_roles for role in required_roles):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Required role: {' or '.join(required_roles)}"
            )

        return user

    return check_role


async def get_current_admin_user(
    user: User = Depends(require_role(["admin", "super_admin"]))
) -> User:
    """
    Get current admin user.

    Args:
        user: Current user with admin role

    Returns:
        User: Admin user
    """
    return user


async def get_current_super_admin_user(
    user: User = Depends(require_role("super_admin"))
) -> User:
    """
    Get current super admin user.

    Args:
        user: Current user with super admin role

    Returns:
        User: Super admin user
    """
    return user


# Simplified permission checking for production use
async def get_user_with_permissions(
    user: User = Depends(get_current_verified_user)
) -> tuple[User, List[str]]:
    """
    Get user with their effective permissions.

    Args:
        user: Current verified user

    Returns:
        tuple: (User, list of permissions)
    """
    # JWT authentication - derive permissions from roles
    permissions = _get_role_permissions(user)
    return user, permissions


def _get_role_permissions(user: User) -> List[str]:
    """
    Get permissions based on user roles.

    Args:
        user: User object

    Returns:
        List of permissions
    """
    permissions = []

    if hasattr(user, 'roles') and user.roles:
        user_roles = [role.name for role in user.roles]

        # Basic permissions for all users
        permissions.extend([
            "chat.completions",
            "models.list",
            "models.retrieve"
        ])

        # Pro/Enterprise permissions
        if any(role in ["pro", "enterprise", "admin", "super_admin"] for role in user_roles):
            permissions.extend([
                "embeddings.create",
                "files.upload",
                "files.list",
                "files.retrieve",
                "files.delete",
                "fine_tuning.jobs.create",
                "fine_tuning.jobs.list",
                "fine_tuning.jobs.retrieve",
                "moderations.create",
                "audio.transcriptions",
                "audio.translations",
                "images.generations"
            ])

        # Admin permissions
        if any(role in ["admin", "super_admin"] for role in user_roles):
            permissions.extend([
                "admin.users.read",
                "admin.users.write",
                "admin.billing.read"
            ])

    return list(set(permissions))  # Remove duplicates


# Export working dependencies
__all__ = [
    "get_current_user",
    "get_current_active_user",
    "get_current_verified_user",
    "get_current_admin_user",
    "get_current_super_admin_user",
    "require_role",
    "get_user_with_permissions",
    "oauth2_scheme"
]
