"""
Database initialization script.
"""
import logging
from typing import List, Dict, Any
from uuid import uuid4

from sqlalchemy.orm import Session

from .base import SessionLocal, init_db
from ..models.role import DEFAULT_ROLES
from ..models.user import UserStatus
from ..enums import PermissionType
from ..db.models import User, Role, Permission
from app.services.business.user_management.services.auth_service import AuthService

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def init() -> None:
    """Initialize the database with default roles and permissions."""
    db = SessionLocal()
    try:
        # Create default roles and permissions
        create_default_roles(db)

        # Create default admin user if it doesn't exist
        create_default_admin(db)

        db.commit()
        logger.info("Database initialized successfully")
    except Exception as e:
        db.rollback()
        logger.error(f"Error initializing database: {e}")
        raise
    finally:
        db.close()

def create_default_roles(db: Session) -> None:
    """Create default roles and permissions if they don't exist."""
    from ..db.models import DEFAULT_ROLES as ROLE_DEFINITIONS

    try:
        # Ensure tables exist before trying to query them
        logger.info("Ensuring database tables exist...")
        from sqlalchemy import text

        try:
            # First, ensure the enum type exists with correct values
            # PostgreSQL doesn't support IF NOT EXISTS for CREATE TYPE, so we need to check first
            try:
                db.execute(text("CREATE TYPE permissiontype AS ENUM ('READ', 'WRITE', 'DELETE', 'MANAGE')"))
                db.commit()  # Commit the enum creation
                logger.info("Created enum type permissiontype")
            except Exception as enum_error:
                db.rollback()  # Rollback failed transaction
                if "already exists" in str(enum_error):
                    logger.info("Enum type permissiontype already exists")
                else:
                    logger.warning(f"Enum creation failed: {enum_error}")

            # Create tables if they don't exist
            db.execute(text("""
                CREATE TABLE IF NOT EXISTS permissions (
                    id UUID PRIMARY KEY,
                    name VARCHAR(100) UNIQUE NOT NULL,
                    description TEXT,
                    resource VARCHAR(50) NOT NULL,
                    action permissiontype NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """))

            db.execute(text("""
                CREATE TABLE IF NOT EXISTS roles (
                    id UUID PRIMARY KEY,
                    name VARCHAR(100) UNIQUE NOT NULL,
                    display_name VARCHAR(200),
                    description TEXT,
                    is_default BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """))

            db.execute(text("""
                CREATE TABLE IF NOT EXISTS role_permissions (
                    role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
                    permission_id UUID REFERENCES permissions(id) ON DELETE CASCADE,
                    PRIMARY KEY (role_id, permission_id)
                )
            """))

            db.execute(text("""
                CREATE TABLE IF NOT EXISTS user_roles (
                    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
                    role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
                    PRIMARY KEY (user_id, role_id)
                )
            """))

            try:
                db.commit()
                logger.info("Database tables ensured to exist")
            except Exception as commit_error:
                logger.error(f"Failed to commit table creation: {commit_error}")
                db.rollback()
                raise

        except Exception as table_error:
            logger.error(f"Failed to create tables: {table_error}")
            db.rollback()

            # If table creation fails, try to handle enum mismatch
            if "'read' is not among the defined enum values" in str(table_error) or "does not exist" in str(table_error):
                logger.warning("Detected enum mismatch or missing tables. Performing database reset...")

                try:
                    # Drop everything and recreate
                    db.execute(text("DROP TABLE IF EXISTS role_permissions CASCADE"))
                    db.execute(text("DROP TABLE IF EXISTS user_roles CASCADE"))
                    db.execute(text("DROP TABLE IF EXISTS permissions CASCADE"))
                    db.execute(text("DROP TABLE IF EXISTS roles CASCADE"))
                    db.execute(text("DROP TYPE IF EXISTS permissiontype CASCADE"))

                    # Recreate enum and tables
                    db.execute(text("CREATE TYPE permissiontype AS ENUM ('READ', 'WRITE', 'DELETE', 'MANAGE')"))

                    db.execute(text("""
                        CREATE TABLE permissions (
                            id UUID PRIMARY KEY,
                            name VARCHAR(100) UNIQUE NOT NULL,
                            description TEXT,
                            resource VARCHAR(50) NOT NULL,
                            action permissiontype NOT NULL,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )
                    """))

                    db.execute(text("""
                        CREATE TABLE roles (
                            id UUID PRIMARY KEY,
                            name VARCHAR(100) UNIQUE NOT NULL,
                            display_name VARCHAR(200),
                            description TEXT,
                            is_default BOOLEAN DEFAULT FALSE,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )
                    """))

                    db.execute(text("""
                        CREATE TABLE role_permissions (
                            role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
                            permission_id UUID REFERENCES permissions(id) ON DELETE CASCADE,
                            PRIMARY KEY (role_id, permission_id)
                        )
                    """))

                    db.execute(text("""
                        CREATE TABLE user_roles (
                            user_id UUID REFERENCES users(id) ON DELETE CASCADE,
                            role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
                            PRIMARY KEY (user_id, role_id)
                        )
                    """))

                    db.commit()
                    logger.info("Successfully reset and recreated database tables")

                except Exception as reset_error:
                    logger.error(f"Failed to reset database: {reset_error}")
                    db.rollback()
                    raise
            else:
                raise

        # First create all permissions
        all_permissions = set()
        for role_name, role_data in ROLE_DEFINITIONS.items():
            for perm in role_data.get("permissions", []):
                all_permissions.add(perm)

        # Create permission objects
        permission_map = {}
        for perm_id in all_permissions:
            # Check if permission already exists by name
            permission = db.query(Permission).filter(Permission.name == perm_id).first()
            if not permission:
                # Generate a new UUID for the permission
                resource = perm_id.split(":")[0] if ":" in perm_id else perm_id
                action_str = perm_id.split(":")[1] if ":" in perm_id else "read"

                # Map action string to PermissionType enum
                action_mapping = {
                    "read": PermissionType.READ,
                    "write": PermissionType.WRITE,
                    "delete": PermissionType.DELETE,
                    "manage": PermissionType.MANAGE,
                    "*": PermissionType.MANAGE  # Wildcard maps to MANAGE
                }

                action_enum = action_mapping.get(action_str.lower(), PermissionType.READ)

                # Create a new permission with a proper UUID
                permission = Permission(
                    id=str(uuid4()),
                    name=perm_id,
                    description=f"Permission to {perm_id.replace(':', ' ')}",
                    resource=resource,
                    action=action_enum
                )
                db.add(permission)
                logger.info(f"Created permission: {perm_id}")
            permission_map[perm_id] = permission

        # Commit permissions first to ensure they exist before role creation
        db.commit()

        # Define role name to ID mapping
        role_name_to_id = {
            "super_admin": str(uuid4()),
            "admin": str(uuid4()),
            "manager": str(uuid4()),
            "user": str(uuid4()),
            "guest": str(uuid4())
        }

        # Now create roles and associate permissions
        for role_name, role_data in ROLE_DEFINITIONS.items():
            role_uuid = role_name_to_id[role_name]

            # Check if role exists by name
            role = db.query(Role).filter(Role.name == role_name).first()

            if not role:
                # Create role with display name and description
                display_name = role_name.replace("_", " ").title()
                description = role_data.get("description", f"{display_name} role")

                role = Role(
                    id=role_uuid,
                    name=role_name,
                    display_name=display_name,
                    description=description,
                    is_default=role_name in ["user", "guest"]
                )
                db.add(role)
                logger.info(f"Created role: {role_name} ({display_name})")

            # Add permissions to role if not already assigned
            current_permission_names = {p.name for p in role.permissions}
            for perm_id in role_data.get("permissions", []):
                if perm_id in permission_map and perm_id not in current_permission_names:
                    role.permissions.append(permission_map[perm_id])
                    logger.info(f"  - Added permission {perm_id} to role {role_name}")
                    current_permission_names.add(perm_id)

        db.commit()
        logger.info("Successfully created all default roles and permissions")

    except Exception as e:
        db.rollback()
        logger.error(f"Error creating default roles and permissions: {str(e)}", exc_info=True)
        raise



def create_default_admin(db: Session) -> None:
    """Create a default admin user if no users exist."""
    try:
        if db.query(User).count() > 0:
            logger.info("Users already exist, skipping default admin creation")
            return

        # Get the super admin role by name
        admin_role = db.query(Role).filter(Role.name == "super_admin").first()
        if not admin_role:
            logger.error("Super admin role not found, cannot create default admin user")
            # Try to create roles if they don't exist
            create_default_roles(db)
            db.commit()
            admin_role = db.query(Role).filter(Role.name == "super_admin").first()
            if not admin_role:
                logger.error("Failed to create super admin role, cannot create default admin user")
                return

        # Create default admin user with credentials: admin/simba
        default_email = "<EMAIL>"
        default_username = "admin"
        default_password = "simba"  # Default admin password as per requirements

        # Check if admin user already exists (by email or username)
        existing_user = db.query(User).filter(
            (User.email == default_email) | (User.username == default_username)
        ).first()
        if existing_user:
            logger.info(f"Admin user already exists: {existing_user.email or existing_user.username}")
            return

        admin_user = User(
            id=str(uuid4()),
            email=default_email,
            username=default_username,
            hashed_password=AuthService.get_password_hash(default_password),  # type: ignore
            first_name="Admin",
            last_name="User",
            is_active=True,
            is_verified=True,
            status=UserStatus.ACTIVE
        )

        # Add the super admin role to the user
        admin_user.roles.append(admin_role)

        db.add(admin_user)
        db.commit()

        logger.info(f"✅ Created default admin user: {default_username} ({default_email})")
        logger.info(f"🔑 Default admin credentials - Username: {default_username}, Password: {default_password}")
        logger.warning("⚠️  Please change the default password after first login for security!")

    except Exception as e:
        db.rollback()
        logger.error(f"❌ Error creating default admin user: {str(e)}", exc_info=True)
        raise


def main() -> None:
    """Initialize the database."""
    logger.info("Initializing database...")
    init_db()
    init()
    logger.info("Database initialization complete")


if __name__ == "__main__":
    main()
