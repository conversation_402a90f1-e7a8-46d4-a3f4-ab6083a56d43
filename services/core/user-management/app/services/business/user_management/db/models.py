"""
Database models for the user management system.
"""
from datetime import datetime
from typing import List, Optional, Dict, Any
from uuid import UUID, uuid4
from enum import Enum

from sqlalchemy import (
    Boolean, Column, DateTime, Enum as SQLEnum,
    Foreign<PERSON>ey, Integer, String, Table, Text, Numeric
)
from sqlalchemy.dialects.postgresql import UUID as PG_UUID, JSONB
from sqlalchemy.orm import relationship

# Import enums from the root user_management package to avoid circular imports
from ..enums import UserStatus, UserRole, PermissionType
from .base import Base


# Subscription status enum moved to app/billing/models/enums.py to avoid duplication


# Association table for many-to-many relationship between User and Role
user_role = Table(
    "user_roles",
    Base.metadata,
    Column("user_id", PG_UUID(as_uuid=True), ForeignKey("users.id"), primary_key=True),
    Column("role_id", PG_UUID(as_uuid=True), ForeignKey("roles.id"), primary_key=True),
)


class User(Base):
    """User account model."""
    __tablename__ = "users"

    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    email = Column(String(255), unique=True, nullable=False, index=True)
    username = Column(String(50), unique=True, nullable=True, index=True)
    hashed_password = Column(String(255), nullable=True)
    first_name = Column(String(50))
    last_name = Column(String(50))
    is_active = Column(Boolean(), default=True)
    is_verified = Column(Boolean(), default=False)
    status = Column(SQLEnum(UserStatus), default=UserStatus.PENDING)
    last_login = Column(DateTime, nullable=True)
    failed_login_attempts = Column(Integer, default=0)
    reset_password_token = Column(String(100), nullable=True, index=True)
    reset_password_sent_at = Column(DateTime, nullable=True)
    email_verification_token = Column(String(100), nullable=True, index=True)
    email_verification_sent_at = Column(DateTime, nullable=True)
    oauth_provider = Column(String(50), nullable=True)
    oauth_account_id = Column(String(255), nullable=True, index=True)
    oauth_account_data = Column(JSONB, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Use string-based relationship to avoid circular imports
    roles = relationship("Role", secondary=user_role, back_populates="users", lazy="selectin")
    profile = relationship("UserProfile", back_populates="user", uselist=False, lazy="selectin", cascade="all, delete-orphan")

    # Billing relationships removed - handled by separate billing microservice
    # In microservices architecture, cross-service relationships are handled via API calls
    # Use billing service API to get user's subscriptions, payments, etc.

    def __repr__(self):
        return f"<User {self.email}>"

    @property
    def full_name(self) -> str:
        """Return the full name of the user."""
        return f"{self.first_name} {self.last_name}" if self.first_name and self.last_name else self.email

    def has_role(self, role_name: str) -> bool:
        """Check if user has the specified role."""
        return any(role.name == role_name for role in self.roles)

    def is_admin(self) -> bool:
        """Check if user is an admin or super admin."""
        return self.has_role(UserRole.ADMIN) or self.has_role(UserRole.SUPER_ADMIN)


# Association table for many-to-many relationship between Role and Permission
role_permission = Table(
    "role_permissions",
    Base.metadata,
    Column("role_id", PG_UUID(as_uuid=True), ForeignKey("roles.id"), primary_key=True),
    Column("permission_id", PG_UUID(as_uuid=True), ForeignKey("permissions.id"), primary_key=True),
)


class Role(Base):
    """Role model for role-based access control.
    
    Attributes:
        id: Unique identifier (UUID)
        name: Internal role name (e.g., 'super_admin')
        display_name: Human-readable name (e.g., 'Super Administrator')
        description: Role description
        is_default: Whether this is a default role that should be assigned to new users
    """
    __tablename__ = "roles"

    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid4)
    name = Column(String(50), unique=True, nullable=False, index=True)
    display_name = Column(String(100), nullable=True)
    description = Column(Text, nullable=True)
    is_default = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Use string-based relationships to avoid circular imports
    users = relationship("User", secondary=user_role, back_populates="roles", lazy="selectin")
    permissions = relationship("Permission", secondary=role_permission, back_populates="roles", lazy="selectin")

    def __repr__(self):
        return f"<Role {self.name}>"


class Permission(Base):
    """Permission model for fine-grained access control."""
    __tablename__ = "permissions"

    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid4)
    name = Column(String(100), unique=True, nullable=False, index=True)
    description = Column(Text, nullable=True)
    resource = Column(String(50), nullable=False, index=True)
    action = Column(SQLEnum(PermissionType), nullable=False, index=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Use string-based relationship to avoid circular imports
    roles = relationship("Role", secondary=role_permission, back_populates="permissions", lazy="selectin")

    def __repr__(self):
        return f"<Permission {self.resource}.{self.action}>"


class UserProfile(Base):
    """Extended user profile information."""
    __tablename__ = "user_profiles"
    
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid4)
    user_id = Column(PG_UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), unique=True)
    
    # Contact Information
    phone_number = Column(String(20), nullable=True)
    company = Column(String(100), nullable=True)
    job_title = Column(String(100), nullable=True)
    
    # Location
    address = Column(Text, nullable=True)
    city = Column(String(100), nullable=True)
    state = Column(String(100), nullable=True)
    country = Column(String(100), nullable=True)
    postal_code = Column(String(20), nullable=True)
    timezone = Column(String(50), nullable=True)
    
    # Preferences
    language = Column(String(10), default="en")
    theme = Column(String(20), default="light")
    notifications_enabled = Column(Boolean, default=True)
    email_notifications = Column(Boolean, default=True)
    
    # Social Media
    website = Column(String(255), nullable=True)
    linkedin_url = Column(String(255), nullable=True)
    twitter_handle = Column(String(50), nullable=True)
    github_username = Column(String(50), nullable=True)
    
    # Additional Info
    bio = Column(Text, nullable=True)
    avatar_url = Column(String(255), nullable=True)
    
    # Metadata
    metadata_ = Column("metadata", JSONB, default=dict)
    
    # Relationships
    user = relationship("User", back_populates="profile")
    
    def __repr__(self):
        return f"<UserProfile {self.user_id}>"


# TokenBlacklist and AuditLog models are now defined in the PostgreSQL cluster service
# They are available through the shared database tables

# Subscription model moved to app/billing/models/subscription.py to avoid duplication


# Default roles and permissions
DEFAULT_ROLES = {
    "super_admin": {
        "description": "Super Administrator with full access to all features and settings.",
        "permissions": ["*:*"]  # Wildcard for all permissions
    },
    "admin": {
        "description": "Administrator with extensive access to system features.",
        "permissions": [
            "users:*",
            "roles:*",
            "permissions:read"
        ]
    },
    "manager": {
        "description": "Manager with limited administrative privileges.",
        "permissions": [
            "users:read",
            "users:write",
            "roles:read"
        ]
    },
    "user": {
        "description": "Regular authenticated user.",
        "permissions": [
            "profile:read",
            "profile:write"
        ]
    },
    "guest": {
        "description": "Guest user with minimal permissions.",
        "permissions": [
            "profile:read"
        ]
    }
}
