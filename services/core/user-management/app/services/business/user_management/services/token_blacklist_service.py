"""
Token Blacklist Service for User Management

Handles JWT token blacklisting for immediate invalidation after logout.
Integrates with PostgreSQL Cluster Service for persistent storage.
"""

import jwt
import hashlib
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from uuid import uuid4
import logging

from app.core.config import settings
from app.services.postgresql_client import postgresql_client

logger = logging.getLogger(__name__)


class TokenBlacklistService:
    """
    Service for managing JWT token blacklisting.

    Provides functionality to:
    - Add tokens to blacklist on logout
    - Check if tokens are blacklisted during authentication
    - Clean up expired blacklisted tokens
    - Generate unique JWT IDs (jti) for tokens
    """

    def __init__(self):
        """Initialize the token blacklist service."""
        self.postgresql_client = postgresql_client

    def generate_jti(self) -> str:
        """
        Generate a unique JWT ID (jti) for token identification.

        Returns:
            str: Unique JWT ID
        """
        return str(uuid4())

    def extract_jti_from_token(self, token: str) -> Optional[str]:
        """
        Extract JWT ID (jti) from a token without verification.

        Args:
            token: JWT token string

        Returns:
            Optional[str]: JWT ID if found, None otherwise
        """
        try:
            # Decode without verification to extract jti
            unverified_payload = jwt.decode(
                token,
                options={"verify_signature": False}
            )
            return unverified_payload.get("jti")
        except Exception as e:
            logger.error(f"Failed to extract jti from token: {e}")
            return None

    def extract_token_info(self, token: str) -> Dict[str, Any]:
        """
        Extract token information without verification.

        Args:
            token: JWT token string

        Returns:
            Dict with token information
        """
        try:
            unverified_payload = jwt.decode(
                token,
                options={"verify_signature": False}
            )

            return {
                "jti": unverified_payload.get("jti"),
                "sub": unverified_payload.get("sub"),  # user_id
                "exp": unverified_payload.get("exp"),  # expiration timestamp
                "type": unverified_payload.get("type", "access")
            }
        except Exception as e:
            logger.error(f"Failed to extract token info: {e}")
            return {}

    async def add_token_to_blacklist(
        self,
        token: str,
        user_id: str,
        reason: str = "logout"
    ) -> bool:
        """
        Add a token to the blacklist.

        Args:
            token: JWT token to blacklist
            user_id: ID of the user who owns the token
            reason: Reason for blacklisting (default: "logout")

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Extract token information
            token_info = self.extract_token_info(token)
            jti = token_info.get("jti")
            exp_timestamp = token_info.get("exp")

            if not jti:
                logger.error("Token does not contain jti - cannot blacklist")
                return False

            if not exp_timestamp:
                logger.error("Token does not contain expiration - cannot blacklist")
                return False

            # Convert expiration timestamp to datetime
            expires_at = datetime.utcfromtimestamp(exp_timestamp)

            # Create token hash for storage (don't store full token for security)
            token_hash = hashlib.sha256(token.encode()).hexdigest()

            # Insert into blacklist via PostgreSQL Cluster Service
            insert_data = {
                "jti": jti,
                "token": token_hash,  # Store hash, not full token
                "expires_at": expires_at.isoformat(),
                "user_id": user_id,
                "reason": reason,
                "created_at": datetime.utcnow().isoformat()
            }

            result = await self.postgresql_client.insert_record(
                table_name="token_blacklist",
                data=insert_data,
                returning=["id", "jti"]
            )

            if result and result.get("success"):
                logger.info(f"✅ Token blacklisted: jti={jti}, user={user_id}, reason={reason}")
                return True
            else:
                logger.error(f"❌ Failed to blacklist token: {result}")
                return False

        except Exception as e:
            logger.error(f"❌ Error blacklisting token: {e}")
            return False

    async def is_token_blacklisted(self, token: str) -> bool:
        """
        Check if a token is blacklisted.

        Args:
            token: JWT token to check

        Returns:
            bool: True if blacklisted, False otherwise
        """
        try:
            # Extract jti from token
            jti = self.extract_jti_from_token(token)
            if not jti:
                logger.warning("Token does not contain jti - treating as valid")
                return False

            # Check if jti exists in blacklist
            result = await self.postgresql_client.select_records(
                table_name="token_blacklist",
                columns=["jti", "expires_at"],
                where_clause="jti = $1 AND expires_at > $2",
                params=[jti, datetime.utcnow().isoformat()]
            )

            if result and result.get("success"):
                records = result.get("records", [])
                is_blacklisted = len(records) > 0

                if is_blacklisted:
                    logger.info(f"🚫 Token is blacklisted: jti={jti}")

                return is_blacklisted
            else:
                logger.error(f"❌ Error checking blacklist: {result}")
                return False

        except Exception as e:
            logger.error(f"❌ Error checking token blacklist: {e}")
            return False

    async def blacklist_all_user_tokens(self, user_id: str, reason: str = "security") -> int:
        """
        Blacklist all active tokens for a user.

        This is useful for security incidents or when forcing logout from all devices.

        Args:
            user_id: ID of the user
            reason: Reason for blacklisting

        Returns:
            int: Number of tokens blacklisted
        """
        try:
            # Note: This is a simplified implementation
            # In a full implementation, you'd need to track active tokens
            # For now, we'll create a special blacklist entry for the user

            special_jti = f"user_logout_all_{user_id}_{datetime.utcnow().timestamp()}"

            insert_data = {
                "jti": special_jti,
                "token": f"ALL_TOKENS_FOR_USER_{user_id}",
                "expires_at": (datetime.utcnow() + timedelta(days=30)).isoformat(),  # Long expiration
                "user_id": user_id,
                "reason": f"{reason}_all_tokens",
                "created_at": datetime.utcnow().isoformat()
            }

            result = await self.postgresql_client.insert_record(
                table_name="token_blacklist",
                data=insert_data
            )

            if result and result.get("success"):
                logger.info(f"✅ All tokens blacklisted for user: {user_id}")
                return 1
            else:
                logger.error(f"❌ Failed to blacklist all tokens for user: {user_id}")
                return 0

        except Exception as e:
            logger.error(f"❌ Error blacklisting all user tokens: {e}")
            return 0

    async def cleanup_expired_tokens(self) -> int:
        """
        Clean up expired tokens from the blacklist.

        Returns:
            int: Number of expired tokens removed
        """
        try:
            # Delete expired tokens
            result = await self.postgresql_client.execute_query(
                query="DELETE FROM token_blacklist WHERE expires_at <= $1",
                params=[datetime.utcnow().isoformat()]
            )

            if result and result.get("success"):
                # Extract affected rows count from result
                affected_rows = 0  # PostgreSQL cluster service should return this
                logger.info(f"🧹 Cleaned up {affected_rows} expired blacklisted tokens")
                return affected_rows
            else:
                logger.error(f"❌ Failed to cleanup expired tokens: {result}")
                return 0

        except Exception as e:
            logger.error(f"❌ Error cleaning up expired tokens: {e}")
            return 0

    async def get_blacklist_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the token blacklist.

        Returns:
            Dict with blacklist statistics
        """
        try:
            # Count total blacklisted tokens
            total_result = await self.postgresql_client.select_records(
                table_name="token_blacklist",
                columns=["COUNT(*) as total"]
            )

            # Count active (non-expired) blacklisted tokens
            active_result = await self.postgresql_client.select_records(
                table_name="token_blacklist",
                columns=["COUNT(*) as active"],
                where_clause="expires_at > $1",
                params=[datetime.utcnow().isoformat()]
            )

            total_count = 0
            active_count = 0

            if total_result and total_result.get("success"):
                records = total_result.get("records", [])
                if records:
                    total_count = records[0].get("total", 0)

            if active_result and active_result.get("success"):
                records = active_result.get("records", [])
                if records:
                    active_count = records[0].get("active", 0)

            return {
                "total_blacklisted": total_count,
                "active_blacklisted": active_count,
                "expired_blacklisted": total_count - active_count,
                "last_checked": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"❌ Error getting blacklist stats: {e}")
            return {
                "total_blacklisted": 0,
                "active_blacklisted": 0,
                "expired_blacklisted": 0,
                "error": str(e)
            }


# Global instance
token_blacklist_service = TokenBlacklistService()
