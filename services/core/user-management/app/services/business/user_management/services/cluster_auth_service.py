"""
Cluster-based Authentication Service

This service uses the PostgreSQL Cluster Service for authentication
instead of direct database access.
"""

import logging
from datetime import datetime, timedelta, timezone
from typing import Dict, Any, Optional
from uuid import uuid4
from fastapi import HTTPException, status, Depends
from fastapi.security import OAuth2<PERSON>asswordBearer
from jose import J<PERSON><PERSON><PERSON><PERSON>, jwt
import bcrypt

from app.core.config import settings
from app.services.postgresql_client import PostgreSQLClusterClient
from app.services.business.user_management.schemas.user import Token

logger = logging.getLogger(__name__)

# OAuth2 scheme for token authentication
oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/auth/login")

def get_oauth2_scheme():
    """Get OAuth2 scheme for dependency injection."""
    return oauth2_scheme


class ClusterAuthService:
    """Authentication service using PostgreSQL Cluster Service."""
    
    ACCESS_TOKEN_EXPIRE_MINUTES = settings.ACCESS_TOKEN_EXPIRE_MINUTES
    REFRESH_TOKEN_EXPIRE_DAYS = settings.REFRESH_TOKEN_EXPIRE_DAYS
    
    def __init__(self):
        self.cluster_client = PostgreSQLClusterClient()
    
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """
        Verify a plain password against a hashed password.
        
        Args:
            plain_password: The plain text password
            hashed_password: The hashed password
            
        Returns:
            bool: True if the password is correct
        """
        try:
            return bcrypt.checkpw(
                plain_password.encode('utf-8'),
                hashed_password.encode('utf-8')
            )
        except Exception as e:
            logger.error(f"Password verification error: {e}")
            return False
    
    @staticmethod
    def get_password_hash(password: str) -> str:
        """
        Hash a password using bcrypt.
        
        Args:
            password: The plain text password
            
        Returns:
            str: The hashed password
        """
        salt = bcrypt.gensalt()
        return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
    
    async def authenticate_user(
        self,
        username: str = None,
        email: str = None,
        password: str = None
    ) -> Optional[Dict[str, Any]]:
        """
        Authenticate a user using the PostgreSQL Cluster Service.
        
        Args:
            username: The username to authenticate with
            email: The email to authenticate with
            password: The plain text password
            
        Returns:
            Dict: The authenticated user data or None
        """
        if not (username or email) or not password:
            return None
        
        try:
            # Call cluster service to get user data
            response = await self.cluster_client.authenticate_user(
                username=username,
                email=email,
                password=password
            )
            
            if not response or not response.get("success"):
                logger.warning(f"Authentication failed for {username or email}")
                return None
            
            user_data = response.get("user")
            if not user_data:
                return None
            
            # Verify password
            if not self.verify_password(password, user_data.get("hashed_password", "")):
                # Update failed login attempt
                await self.cluster_client.update_user_login(
                    user_id=str(user_data["id"]),
                    success=False
                )
                logger.warning(f"Password verification failed for {username or email}")
                return None

            # Check if user is active and verified
            if not user_data.get("is_active") or user_data.get("status") != "ACTIVE":
                logger.warning(f"User not active: {username or email}")
                return None
            
            # Update successful login
            await self.cluster_client.update_user_login(
                user_id=str(user_data["id"]),
                success=True
            )
            
            logger.info(f"User authenticated successfully: {user_data.get('email')}")
            return user_data
            
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return None
    
    @staticmethod
    def create_access_token(
        data: Dict[str, Any],
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """
        Create a JWT access token with unique JTI for blacklisting.

        Args:
            data: The data to encode in the token
            expires_delta: Optional expiration time delta

        Returns:
            str: The encoded JWT token
        """
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.now(timezone.utc) + expires_delta
        else:
            expire = datetime.now(timezone.utc) + timedelta(
                minutes=ClusterAuthService.ACCESS_TOKEN_EXPIRE_MINUTES
            )

        # Add unique JWT ID (jti) for token blacklisting
        to_encode.update({
            "exp": expire,
            "type": "access",
            "jti": str(uuid4()),  # Unique token identifier
            "iat": datetime.now(timezone.utc).timestamp()  # Issued at time
        })
        encoded_jwt = jwt.encode(
            to_encode,
            settings.SECRET_KEY,
            algorithm=settings.ALGORITHM
        )
        return encoded_jwt
    
    @staticmethod
    def create_refresh_token(
        data: Dict[str, Any],
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """
        Create a JWT refresh token.
        
        Args:
            data: The data to encode in the token
            expires_delta: Optional expiration time delta
            
        Returns:
            str: The encoded JWT refresh token
        """
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.now(timezone.utc) + expires_delta
        else:
            expire = datetime.now(timezone.utc) + timedelta(
                days=ClusterAuthService.REFRESH_TOKEN_EXPIRE_DAYS
            )
        
        to_encode.update({"exp": expire, "type": "refresh"})
        encoded_jwt = jwt.encode(
            to_encode,
            settings.SECRET_KEY,
            algorithm=settings.ALGORITHM
        )
        return encoded_jwt
    
    @classmethod
    def create_tokens(
        cls,
        user: Dict[str, Any],
        expires_delta: Optional[timedelta] = None
    ) -> Dict[str, str]:
        """
        Create access and refresh tokens for a user.
        
        Args:
            user: The user data dictionary
            expires_delta: Optional expiration time delta for access token
            
        Returns:
            Dict containing access_token and refresh_token
        """
        access_token_expires = expires_delta or timedelta(
            minutes=cls.ACCESS_TOKEN_EXPIRE_MINUTES
        )
        
        # Create access token
        access_token = cls.create_access_token(
            data={"sub": str(user["id"]), "username": user.get("username"), "email": user.get("email")},
            expires_delta=access_token_expires
        )
        
        # Create refresh token
        refresh_token = cls.create_refresh_token(
            data={"sub": str(user["id"])}
        )
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer"
        }


# Dependency function for getting current user from token
async def get_current_user_from_token(
    token: str = Depends(oauth2_scheme)
) -> Dict[str, Any]:
    """
    Get the current user from the JWT token using cluster service.

    Args:
        token: The JWT token

    Returns:
        Dict: The user data

    Raises:
        HTTPException: If the token is invalid or the user doesn't exist
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    if not token:
        raise credentials_exception

    try:
        payload = jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM]
        )
        user_id: str = payload.get("sub")
        token_type: str = payload.get("type")

        if user_id is None or token_type != "access":
            raise credentials_exception

    except JWTError as e:
        logger.error(f"JWT Error: {e}")
        raise credentials_exception

    # Get fresh user data from the cluster service
    try:
        cluster_client = PostgreSQLClusterClient()

        # Try to get user by ID using the get_user method
        try:
            response = await cluster_client.get_user(user_id)
            if response and response.get("success") and response.get("data"):
                user_data = response["data"]
            else:
                # Fallback to select_records with UUID cast
                logger.info(f"Fallback: trying select_records for user_id: {user_id}")
                response = await cluster_client.select_records(
                    table_name="users",
                    columns=["id", "username", "email", "first_name", "last_name", "is_active", "is_verified", "status", "created_at", "updated_at"],
                    where_clause="id::text = $1",  # Cast UUID to text for comparison
                    params=[str(user_id)]
                )

                if not response or not response.get("success") or not response.get("records"):
                    logger.warning(f"User not found in cluster service: {user_id}")
                    raise credentials_exception

                user_data = response["records"][0]

        except Exception as get_error:
            logger.error(f"Error in get_user, trying select_records: {get_error}")
            # Fallback to select_records
            response = await cluster_client.select_records(
                table_name="users",
                columns=["id", "username", "email", "first_name", "last_name", "is_active", "is_verified", "status", "created_at", "updated_at"],
                where_clause="id::text = $1",  # Cast UUID to text for comparison
                params=[str(user_id)]
            )

            logger.info(f"Select records response: {response}")

            if not response or not response.get("success") or not response.get("records"):
                logger.warning(f"User not found in cluster service: {user_id}")
                raise credentials_exception

            user_data = response["records"][0]

        # Check if user is active
        if not user_data.get("is_active"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="User account is inactive"
            )

        return user_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching user from cluster service: {e}")
        raise credentials_exception


# Create a global instance
cluster_auth_service = ClusterAuthService()
