"""
Authentication service for user management.
"""
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional, Dict, Any, Union, List
import logging
from uuid import uuid4

from jose import JW<PERSON>rror, jwt
from passlib.context import CryptContext
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import <PERSON>A<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from sqlalchemy import or_

from app.core.config import settings
from app.services.business.user_management.db.models import User
from app.services.business.user_management.models.enums import UserStatus
from app.services.business.user_management.db.deps import get_db, SessionLocal
from app.services.business.user_management.schemas.user import Token, PasswordResetRequest, PasswordResetConfirm, UserCreate
from app.services.business.user_management.services.email_service import UserEmailService

def get_oauth2_scheme():
    """Get the OAuth2 password bearer scheme with the correct token URL."""
    return OAuth2Password<PERSON>earer(tokenUrl=f"{settings.API_V1_STR}/auth/token")

# Create the OAuth2 scheme
oauth2_scheme = get_oauth2_scheme()

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Configure logging
logger = logging.getLogger(__name__)

class AuthService:
    """
    Service for handling authentication and authorization.
    """

    # Token expiration times (in minutes)
    ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24 * 1  # 1 day
    REFRESH_TOKEN_EXPIRE_DAYS = 7  # 7 days
    PASSWORD_RESET_TOKEN_EXPIRE_HOURS = 1  # 1 hour
    EMAIL_VERIFICATION_TOKEN_EXPIRE_DAYS = 7  # 7 days

    @classmethod
    def create_access_token(
        cls,
        data: Dict[str, Any],
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """
        Create a JWT access token with unique JTI for blacklisting.

        Args:
            data: The data to encode in the token
            expires_delta: Optional expiration time delta

        Returns:
            str: The encoded JWT token
        """
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=cls.ACCESS_TOKEN_EXPIRE_MINUTES)

        # Add unique JWT ID (jti) for token blacklisting
        to_encode.update({
            "exp": expire,
            "type": "access",
            "jti": str(uuid4()),  # Unique token identifier
            "iat": datetime.utcnow().timestamp()  # Issued at time
        })

        encoded_jwt = jwt.encode(
            to_encode,
            settings.SECRET_KEY,
            algorithm=settings.ALGORITHM
        )
        return encoded_jwt

    @classmethod
    def create_refresh_token(
        cls,
        user_id: str,
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """
        Create a JWT refresh token with unique JTI for blacklisting.

        Args:
            user_id: The user ID
            expires_delta: Optional expiration time delta

        Returns:
            str: The encoded JWT refresh token
        """
        if expires_delta is None:
            expires_delta = timedelta(days=cls.REFRESH_TOKEN_EXPIRE_DAYS)

        to_encode = {
            "sub": str(user_id),
            "type": "refresh",
            "exp": datetime.utcnow() + expires_delta,
            "jti": str(uuid4()),  # Unique token identifier
            "iat": datetime.utcnow().timestamp()  # Issued at time
        }

        return jwt.encode(
            to_encode,
            settings.SECRET_KEY,
            algorithm=settings.ALGORITHM
        )

    @classmethod
    def decode_access_token(cls, token: str) -> Dict[str, Any]:
        """
        Decode and validate a JWT access token.

        Args:
            token: The JWT token to decode

        Returns:
            Dict: The decoded token payload

        Raises:
            JWTError: If the token is invalid or expired
        """
        return jwt.decode(
            token,
            settings.SECRET_KEY,
            algorithms=[settings.ALGORITHM]
        )

    @classmethod
    def create_tokens(
        cls,
        user_id: str,
        additional_claims: Optional[Dict[str, Any]] = None
    ) -> Dict[str, str]:
        """
        Create both access and refresh tokens for a user.

        Args:
            user_id: The user ID
            additional_claims: Additional claims to include in the access token

        Returns:
            Dict with access_token and refresh_token
        """
        if additional_claims is None:
            additional_claims = {}

        access_token = cls.create_access_token({
            "sub": str(user_id),
            **additional_claims
        })

        refresh_token = cls.create_refresh_token(user_id)

        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer"
        }

    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """
        Verify a password against a hash.

        Args:
            plain_password: The plain text password
            hashed_password: The hashed password

        Returns:
            bool: True if the password matches, False otherwise
        """
        return pwd_context.verify(plain_password, hashed_password)

    @staticmethod
    def get_password_hash(password: str) -> str:
        """
        Hash a password.

        Args:
            password: The plain text password

        Returns:
            str: The hashed password
        """
        return pwd_context.hash(password)

    @classmethod
    def authenticate_user(
        cls,
        db: Session,
        username: Optional[str] = None,
        email: Optional[str] = None,
        password: str = None
    ) -> Optional[User]:
        """
        Authenticate a user with username/email and password.

        Args:
            db: Database session
            username: The username to authenticate with
            email: The email to authenticate with
            password: The plain text password

        Returns:
            User: The authenticated user or None
        """
        if not (username or email) or not password:
            return None

        # Try to find user by username or email
        user = None
        if username:
            # First try to find by username
            user = db.query(User).filter(User.username == username).first()
            # If not found and username looks like an email, try email lookup
            if not user and "@" in username:
                user = db.query(User).filter(User.email == username).first()
        elif email:
            user = db.query(User).filter(User.email == email).first()

        if not user:
            # User not found
            return None

        # Check if user is active
        if not user.is_active or user.status != UserStatus.ACTIVE:
            return None

        # Verify password
        if not cls.verify_password(password, user.hashed_password):
            # Increment failed login attempts
            user.failed_login_attempts = (user.failed_login_attempts or 0) + 1
            db.add(user)
            db.commit()
            return None

        # Reset failed login attempts on successful login
        user.failed_login_attempts = 0
        user.last_login = datetime.utcnow()
        db.add(user)
        db.commit()

        return user

    @staticmethod
    def create_access_token(
        data: Dict[str, Any],
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """
        Create a JWT access token.

        Args:
            data: The data to encode in the token
            expires_delta: Optional expiration time delta

        Returns:
            str: The encoded JWT token
        """
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(
                minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
            )

        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(
            to_encode,
            settings.SECRET_KEY,
            algorithm=settings.ALGORITHM
        )
        return encoded_jwt

    @staticmethod
    def create_refresh_token(
        data: Dict[str, Any],
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """
        Create a JWT refresh token.

        Args:
            data: The data to encode in the token
            expires_delta: Optional expiration time delta

        Returns:
            str: The encoded JWT refresh token
        """
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(
                days=settings.REFRESH_TOKEN_EXPIRE_DAYS
            )

        to_encode.update({"exp": expire, "type": "refresh"})
        encoded_jwt = jwt.encode(
            to_encode,
            settings.SECRET_KEY,
            algorithm=settings.ALGORITHM
        )
        return encoded_jwt

    @classmethod
    def create_tokens(
        cls,
        user: User,
        expires_delta: Optional[timedelta] = None
    ) -> Dict[str, str]:
        """
        Create access and refresh tokens for a user.

        Args:
            user: The user to create tokens for
            expires_delta: Optional expiration time delta for access token

        Returns:
            Dict containing access_token and refresh_token
        """
        access_token_expires = expires_delta or timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )

        # Create access token
        access_token = cls.create_access_token(
            data={"sub": str(user.id), "username": user.username},
            expires_delta=access_token_expires
        )

        # Create refresh token
        refresh_token = cls.create_refresh_token(
            data={"sub": str(user.id)}
        )

        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer"
        }

    @classmethod
    async def get_current_user(
        cls,
        token: str = Depends(get_oauth2_scheme()),
        db: Session = Depends(get_db),
        require_verified: bool = True
    ) -> User:
        """
        Get the current user from the JWT token.

        Args:
            token: The JWT token
            db: Database session
            require_verified: Whether to require email verification

        Returns:
            User: The authenticated user

        Raises:
            HTTPException: If the token is invalid or the user doesn't exist
        """
        credentials_exception = HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

        if not token:
            raise credentials_exception

        try:
            payload = jwt.decode(
                token,
                settings.SECRET_KEY,
                algorithms=[settings.ALGORITHM]
            )
            user_id: str = payload.get("sub")
            token_type: str = payload.get("type")

            if user_id is None or token_type != "access":
                raise credentials_exception

        except JWTError as e:
            logger.error(f"JWT Error: {e}")
            raise credentials_exception

        user = db.query(User).filter(User.id == user_id).first()
        if user is None:
            logger.error(f"User not found: {user_id}")
            raise credentials_exception

        # Check if user is active
        if not user.is_active or user.status != UserStatus.ACTIVE:
            logger.error(f"User not active: {user_id}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="User account is not active"
            )

        # Check if email is verified if required
        if require_verified and not user.is_verified:
            logger.error(f"Email not verified: {user_id}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Email verification required"
            )

        # Update last login time
        user.last_login = datetime.utcnow()
        db.add(user)
        db.commit()

        return user

    @classmethod
    def refresh_access_token(
        cls,
        refresh_token: str,
        db: Session
    ) -> Dict[str, str]:
        """
        Refresh an access token using a refresh token.

        Args:
            refresh_token: The refresh token
            db: Database session

        Returns:
            Dict with new access_token and refresh_token

        Raises:
            HTTPException: If the refresh token is invalid
        """
        credentials_exception = HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate refresh token",
            headers={"WWW-Authenticate": "Bearer"},
        )

        try:
            payload = jwt.decode(
                refresh_token,
                settings.SECRET_KEY,
                algorithms=[settings.ALGORITHM]
            )

            user_id = payload.get("sub")
            token_type = payload.get("type")

            if user_id is None or token_type != "refresh":
                logger.error("Invalid token type or missing user ID")
                raise credentials_exception

            # Verify user exists
            user = db.query(User).filter(User.id == user_id).first()
            if user is None:
                logger.error(f"User not found: {user_id}")
                raise credentials_exception

            # Check if user is active
            if not user.is_active or user.status != UserStatus.ACTIVE:
                logger.error(f"User not active: {user_id}")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="User account is not active"
                )

            # Create new tokens
            return cls.create_tokens(str(user.id))

        except JWTError as e:
            logger.error(f"JWT Error during refresh: {e}")
            raise credentials_exception

    @classmethod
    def request_password_reset(
        cls,
        email: str,
        db: Session
    ) -> None:
        """
        Initiate a password reset request.

        Args:
            email: The user's email address
            db: Database session

        Raises:
            HTTPException: If the email is not found or there's an error
        """
        user = db.query(User).filter(User.email == email).first()
        if not user:
            # Don't reveal that the email doesn't exist
            logger.warning(f"Password reset requested for non-existent email: {email}")
            return

        # Check if there's a recent reset request
        if user.reset_password_sent_at and \
           (datetime.utcnow() - user.reset_password_sent_at) < timedelta(minutes=5):
            logger.warning(f"Password reset requested too soon for user: {user.id}")
            return

        # Generate a reset token and send email
        UserEmailService.send_password_reset_email(db, user)

    @classmethod
    def reset_password(
        cls,
        token: str,
        new_password: str,
        db: Session
    ) -> User:
        """
        Reset a user's password using a valid reset token.

        Args:
            token: The password reset token
            new_password: The new password
            db: Database session

        Returns:
            User: The updated user

        Raises:
            HTTPException: If the token is invalid or expired
        """
        # Find user by reset token
        user = db.query(User).filter(User.reset_password_token == token).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired reset token"
            )

        # Check if token is expired (1 hour)
        if not user.reset_password_sent_at or \
           (datetime.utcnow() - user.reset_password_sent_at) > timedelta(hours=cls.PASSWORD_RESET_TOKEN_EXPIRE_HOURS):
            # Clear the expired token
            user.reset_password_token = None
            user.reset_password_sent_at = None
            db.add(user)
            db.commit()

            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Password reset token has expired"
            )

        # Update password and clear reset token
        user.hashed_password = cls.get_password_hash(new_password)
        user.reset_password_token = None
        user.reset_password_sent_at = None
        user.failed_login_attempts = 0  # Reset failed login attempts

        db.add(user)
        db.commit()
        db.refresh(user)

        return user

    @classmethod
    def send_verification_email(
        cls,
        user: User,
        db: Session
    ) -> None:
        """
        Send a verification email to the user.

        Args:
            user: The user to send the verification email to
            db: Database session
        """
        UserEmailService.send_verification_email(db, user)

    @classmethod
    def verify_email(
        cls,
        token: str,
        db: Session
    ) -> User:
        """
        Verify a user's email using a verification token.

        Args:
            token: The verification token
            db: Database session

        Returns:
            User: The verified user

        Raises:
            HTTPException: If the token is invalid or expired
        """
        user = db.query(User).filter(User.email_verification_token == token).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid or expired verification token"
            )

        # Check if token is expired (7 days)
        if not user.email_verification_sent_at or \
           (datetime.utcnow() - user.email_verification_sent_at) > timedelta(days=cls.EMAIL_VERIFICATION_TOKEN_EXPIRE_DAYS):
            # Clear the expired token
            user.email_verification_token = None
            user.email_verification_sent_at = None
            db.add(user)
            db.commit()

            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email verification token has expired"
            )

        # Mark email as verified and clear token
        user.is_verified = True
        user.email_verification_token = None
        user.email_verification_sent_at = None

        # If this is the first verification, send welcome email
        send_welcome = not user.is_verified

        user.is_verified = True
        db.add(user)
        db.commit()
        db.refresh(user)

        if send_welcome:
            UserEmailService.send_welcome_email(user)

        return user

    @classmethod
    async def get_current_active_user(
        cls,
        current_user: User = Depends(get_current_user)
    ) -> User:
        """
        Get the current active user.

        Args:
            current_user: The current authenticated user

        Returns:
            User: The active user

        Raises:
            HTTPException: If the user is not active
        """
        if not current_user.is_active:
            raise HTTPException(status_code=400, detail="Inactive user")
        return current_user

    @classmethod
    async def get_current_admin_user(
        cls,
        current_user: User = Depends(get_current_user)
    ) -> User:
        """
        Get the current admin user.

        Args:
            current_user: The current authenticated user

        Returns:
            User: The admin user

        Raises:
            HTTPException: If the user is not an admin
        """
        if not current_user.is_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="The user doesn't have enough privileges"
            )
        return current_user


# Standalone dependency functions for FastAPI
async def get_current_user(
    token: str = Depends(get_oauth2_scheme()),
    db: Session = Depends(get_db)
) -> User:
    """
    Get the current user from the JWT token.
    Standalone function for FastAPI dependency injection.
    """
    return await AuthService.get_current_user(token=token, db=db, require_verified=False)


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get the current active user.
    Standalone function for FastAPI dependency injection.
    """
    return await AuthService.get_current_active_user(current_user=current_user)


async def get_current_admin_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """
    Get the current admin user.
    Standalone function for FastAPI dependency injection.
    """
    return await AuthService.get_current_admin_user(current_user=current_user)
