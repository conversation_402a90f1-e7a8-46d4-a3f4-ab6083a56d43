"""
User service for user management operations.
"""
from typing import List, Optional, Dict, Any
import logging

from sqlalchemy.orm import Session

from app.services.business.user_management.db.models import User, UserProfile, Role
from app.services.business.user_management.models.enums import UserStatus
from app.services.business.user_management.db import (
    get_db,
    get_by_id,
    get_by_field,
    create,
    update,
    delete,
    NotFoundError,
    CreateError,
    UpdateError,
    DeleteError
)
from app.services.business.user_management.services.auth_service import AuthService

# Configure logging
logger = logging.getLogger(__name__)

class UserService:
    """
    Service for handling user-related operations.
    """

    @classmethod
    def get_user_by_id(
        cls,
        db: Session,
        user_id: str,
        include_inactive: bool = False
    ) -> Optional[User]:
        """
        Get a user by ID.

        Args:
            db: Database session
            user_id: The ID of the user to retrieve
            include_inactive: If True, includes inactive users

        Returns:
            Optional[User]: The user if found, None otherwise
        """
        try:
            user = get_by_id(db, User, user_id, raise_not_found=False)
            if not user:
                return None

            if not include_inactive and (not user.is_active or user.status != UserStatus.ACTIVE):
                return None

            return user

        except Exception as e:
            logger.error(f"Error getting user by ID {user_id}: {str(e)}")
            raise

    @classmethod
    def get_user_by_email(
        cls,
        db: Session,
        email: str,
        include_inactive: bool = False
    ) -> Optional[User]:
        """
        Get a user by email.

        Args:
            db: Database session
            email: The email of the user to retrieve
            include_inactive: If True, includes inactive users

        Returns:
            Optional[User]: The user if found, None otherwise
        """
        try:
            user = get_by_field(db, User, "email", email, case_sensitive=False)
            if not user:
                return None

            if not include_inactive and (not user.is_active or user.status != UserStatus.ACTIVE):
                return None

            return user

        except Exception as e:
            logger.error(f"Error getting user by email {email}: {str(e)}")
            raise

    @classmethod
    def get_user_by_username(
        cls,
        db: Session,
        username: str,
        include_inactive: bool = False
    ) -> Optional[User]:
        """
        Get a user by username.

        Args:
            db: Database session
            username: The username of the user to retrieve
            include_inactive: If True, includes inactive users

        Returns:
            Optional[User]: The user if found, None otherwise
        """
        try:
            user = get_by_field(db, User, "username", username, case_sensitive=False)
            if not user:
                return None

            if not include_inactive and (not user.is_active or user.status != UserStatus.ACTIVE):
                return None

            return user

        except Exception as e:
            logger.error(f"Error getting user by username {username}: {str(e)}")
            raise

    @classmethod
    def create_user(
        cls,
        db: Session,
        email: str,
        password: str,
        first_name: Optional[str] = None,
        last_name: Optional[str] = None,
        username: Optional[str] = None,
        is_active: bool = True,
        is_verified: bool = False,
        status: UserStatus = UserStatus.PENDING,
        role_names: List[str] = None,
        profile_data: Optional[Dict[str, Any]] = None,
        commit: bool = True
    ) -> User:
        """
        Create a new user.

        Args:
            db: Database session
            email: User's email address (must be unique)
            password: Plain text password (will be hashed)
            first_name: User's first name
            last_name: User's last name
            username: Username (must be unique if provided)
            is_active: Whether the user is active
            is_verified: Whether the user's email is verified
            status: User status
            role_names: List of role names to assign to the user
            profile_data: Additional profile data
            commit: Whether to commit the transaction

        Returns:
            User: The created user

        Raises:
            ValueError: If username or email already exists
            CreateError: If there's an error creating the user
        """
        # Check if email already exists
        if cls.get_user_by_email(db, email, include_inactive=True):
            raise ValueError(f"Email {email} is already registered")

        # Check if username is provided and already exists
        if username and cls.get_user_by_username(db, username, include_inactive=True):
            raise ValueError(f"Username {username} is already taken")

        try:
            # Hash the password
            hashed_password = AuthService.get_password_hash(password)

            # Create user data
            user_data = {
                "email": email,
                "hashed_password": hashed_password,
                "first_name": first_name,
                "last_name": last_name,
                "username": username,
                "is_active": is_active,
                "is_verified": is_verified,
                "status": status
            }

            # Create the user
            user = create(db, User, user_data, commit=False)

            # Add roles if provided, otherwise assign default "user" role
            if role_names:
                for role_name in role_names:
                    role = db.query(Role).filter(Role.name == role_name).first()
                    if role:
                        user.roles.append(role)
            else:
                # Assign default "user" role
                default_role = db.query(Role).filter(Role.name == "user").first()
                if default_role:
                    user.roles.append(default_role)
                    logger.info(f"Assigned default 'user' role to {email}")
                else:
                    logger.warning(f"Default 'user' role not found for {email}")

            # Always create a user profile (with provided data or defaults)
            if not profile_data:
                profile_data = {}

            # Set user_id for the profile
            profile_data["user_id"] = user.id

            # Create the user profile
            profile = create(db, UserProfile, profile_data, commit=False)
            logger.info(f"Created user profile for {email}")

            if commit:
                db.commit()
                db.refresh(user)

            return user

        except Exception as e:
            db.rollback()
            logger.error(f"Error creating user: {str(e)}")
            raise CreateError(f"Error creating user: {str(e)}")
