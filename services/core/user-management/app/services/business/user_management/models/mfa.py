"""
Multi-Factor Authentication (MFA) database models.
"""

from datetime import datetime
from sqlalchemy import Column, String, Boolean, DateTime, ForeignKey, Text, Integer
from sqlalchemy.dialects.postgresql import UUID as PG_UUID, JSONB
from sqlalchemy.orm import relationship
from uuid import uuid4

from app.services.business.user_management.db.base import Base


class MFAMethod(Base):
    """MFA method model for storing user's MFA configurations."""
    __tablename__ = "mfa_methods"

    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    user_id = Column(PG_UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True)
    method_type = Column(String(50), nullable=False)  # 'totp', 'sms', 'email'
    secret = Column(Text, nullable=True)  # TOTP secret or encrypted phone/email
    is_active = Column(Boolean, default=False)
    metadata_ = Column("metadata", JSONB, nullable=True)  # Additional method-specific data

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    verified_at = Column(DateTime, nullable=True)
    last_used_at = Column(DateTime, nullable=True)

    # Relationship
    user = relationship("User", back_populates="mfa_methods")


class MFABackupCode(Base):
    """MFA backup codes for recovery."""
    __tablename__ = "mfa_backup_codes"

    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    user_id = Column(PG_UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True)
    code_hash = Column(String(255), nullable=False)  # Hashed backup code
    is_used = Column(Boolean, default=False)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    used_at = Column(DateTime, nullable=True)

    # Relationship
    user = relationship("User", back_populates="mfa_backup_codes")


class MFAAttempt(Base):
    """MFA authentication attempts for security monitoring."""
    __tablename__ = "mfa_attempts"

    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    user_id = Column(PG_UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True)
    method_type = Column(String(50), nullable=False)
    success = Column(Boolean, nullable=False)
    ip_address = Column(String(45), nullable=True)  # IPv6 support
    user_agent = Column(Text, nullable=True)
    failure_reason = Column(String(255), nullable=True)

    # Timestamps
    attempted_at = Column(DateTime, default=datetime.utcnow)

    # Relationship
    user = relationship("User", back_populates="mfa_attempts")
