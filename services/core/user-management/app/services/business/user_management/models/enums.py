"""
Enums for user management models.
"""
from enum import Enum


class UserStatus(str, Enum):
    """User account status."""
    PENDING = "pending"
    ACTIVE = "active"
    SUSPENDED = "suspended"
    DELETED = "deleted"


class UserRole(str, Enum):
    """User roles."""
    SUPER_ADMIN = "super_admin"
    ADMIN = "admin"
    MANAGER = "manager"
    USER = "user"
    GUEST = "guest"


class PermissionType(str, Enum):
    """Permission types for access control."""
    READ = "READ"
    WRITE = "WRITE"
    DELETE = "DELETE"
    MANAGE = "MANAGE"
