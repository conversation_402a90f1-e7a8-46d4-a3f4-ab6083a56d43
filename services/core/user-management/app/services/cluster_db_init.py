"""
Database initialization via PostgreSQL Cluster Service.

This module initializes the user management database using the PostgreSQL Cluster Service
instead of direct database connections.
"""

import logging
from typing import Dict, Any
from uuid import uuid4

from app.services.postgresql_client import postgresql_client

logger = logging.getLogger(__name__)


async def initialize_database_via_cluster() -> bool:
    """Initialize the database via PostgreSQL Cluster Service."""
    try:
        logger.info("🚀 Starting user management initialization via PostgreSQL Cluster Service...")

        # Check if PostgreSQL Cluster Service is available
        if not await postgresql_client.health_check():
            logger.error("❌ PostgreSQL Cluster Service is not available")
            return False

        # Note: Tables and roles are created by the PostgreSQL Cluster Service during its startup
        # We only need to create the default admin user here
        logger.info("📋 Tables and roles are managed by PostgreSQL Cluster Service - skipping creation")

        # Create admin user (roles are already created by PostgreSQL Cluster Service)
        if not await create_admin_user_via_cluster():
            logger.error("❌ Failed to create admin user")
            return False

        logger.info("✅ User management initialization completed successfully via PostgreSQL Cluster Service")
        return True

    except Exception as e:
        logger.error(f"❌ User management initialization failed: {e}")
        return False





async def create_admin_user_via_cluster() -> bool:
    """Create default admin user via PostgreSQL Cluster Service."""
    try:
        logger.info("👤 Creating default admin user...")

        # Check if any users exist
        existing_users = await postgresql_client.select_records(
            table_name="users",
            columns=["id"],
            limit=1
        )

        # Handle both 'rows' and 'records' response formats
        existing_users_data = None
        if existing_users and existing_users.get("success"):
            existing_users_data = existing_users.get("rows") or existing_users.get("records")

        if existing_users_data:
            logger.info("✅ Users already exist, skipping admin creation")
            return True

        # Get super_admin role
        admin_role_result = await postgresql_client.select_records(
            table_name="roles",
            columns=["id"],
            where_clause="name = $1",
            params=["super_admin"],
            limit=1
        )

        # Debug: Let's see what roles actually exist
        all_roles_result = await postgresql_client.select_records(
            table_name="roles",
            columns=["id", "name", "display_name"]
        )

        # Handle both 'rows' and 'records' response formats
        all_roles_data = None
        if all_roles_result and all_roles_result.get("success"):
            all_roles_data = all_roles_result.get("rows") or all_roles_result.get("records")

        if all_roles_data:
            logger.info(f"🔍 All roles in database: {all_roles_data}")
        else:
            logger.warning("⚠️ No roles found in database or query failed")

        # Handle both 'rows' and 'records' response formats for admin role query
        admin_role_data = None
        if admin_role_result and admin_role_result.get("success"):
            admin_role_data = admin_role_result.get("rows") or admin_role_result.get("records")

        if not admin_role_data:
            logger.error("❌ Super admin role not found")
            logger.error(f"🔍 Admin role query result: {admin_role_result}")
            return False

        admin_role_id = admin_role_data[0]["id"]

        # Create admin user
        from app.services.business.user_management.services.auth_service import AuthService

        admin_user_id = str(uuid4())
        default_email = "<EMAIL>"
        default_username = "admin"
        default_password = "simba"

        # Create user via PostgreSQL Cluster Service
        user_result = await postgresql_client.create_user(
            username=default_username,
            email=default_email,
            password_hash=AuthService.get_password_hash(default_password),
            metadata={
                "first_name": "Admin",
                "last_name": "User",
                "is_active": True,
                "is_verified": True,
                "status": "ACTIVE"
            }
        )

        if not user_result or not user_result.get("success"):
            logger.error("❌ Failed to create admin user")
            return False

        created_user = user_result.get("user", {})
        user_id = created_user.get("id")

        if not user_id:
            logger.error("❌ Admin user created but no ID returned")
            return False

        # Assign super_admin role to user
        role_assignment_result = await postgresql_client.insert_record(
            table_name="user_roles",
            data={
                "user_id": user_id,
                "role_id": admin_role_id
            },
            returning=["user_id", "role_id"]
        )

        if role_assignment_result and role_assignment_result.get("success"):
            logger.info(f"✅ Created default admin user: {default_username} ({default_email})")
            logger.info(f"🔑 Default admin credentials - Username: {default_username}, Password: {default_password}")
            logger.warning("⚠️  Please change the default password after first login for security!")
            return True
        else:
            logger.error("❌ Failed to assign admin role to user")
            return False

    except Exception as e:
        logger.error(f"❌ Error creating admin user: {e}")
        return False


async def check_database_status() -> Dict[str, Any]:
    """Check the status of the database initialization."""
    try:
        # Check if PostgreSQL Cluster Service is available
        cluster_healthy = await postgresql_client.health_check()

        if not cluster_healthy:
            return {
                "status": "unhealthy",
                "message": "PostgreSQL Cluster Service unavailable",
                "cluster_service": False,
                "tables_exist": False,
                "roles_exist": False,
                "admin_exists": False
            }

        # Check if tables exist
        tables_result = await postgresql_client.list_tables()
        tables_exist = False
        if tables_result and tables_result.get("success"):
            table_names = [t["table_name"] for t in tables_result.get("tables", [])]
            required_tables = ["users", "roles", "user_roles", "user_profiles"]
            tables_exist = all(table in table_names for table in required_tables)

        # Check if roles exist
        roles_exist = False
        if tables_exist:
            roles_result = await postgresql_client.select_records(
                table_name="roles",
                columns=["COUNT(*)"],
                limit=1
            )
            # Handle both 'rows' and 'records' response formats
            roles_data = None
            if roles_result and roles_result.get("success"):
                roles_data = roles_result.get("rows") or roles_result.get("records")

            if roles_data:
                roles_count = roles_data[0].get("count", 0)
                roles_exist = roles_count > 0

        # Check if admin user exists
        admin_exists = False
        if tables_exist:
            admin_result = await postgresql_client.select_records(
                table_name="users",
                columns=["id"],
                where_clause="username = $1 OR email = $2",
                params=["admin", "<EMAIL>"],
                limit=1
            )
            # Handle both 'rows' and 'records' response formats
            admin_data = None
            if admin_result and admin_result.get("success"):
                admin_data = admin_result.get("rows") or admin_result.get("records")

            admin_exists = bool(admin_data)

        return {
            "status": "healthy" if all([cluster_healthy, tables_exist, roles_exist, admin_exists]) else "partial",
            "message": "Database status check completed",
            "cluster_service": cluster_healthy,
            "tables_exist": tables_exist,
            "roles_exist": roles_exist,
            "admin_exists": admin_exists
        }

    except Exception as e:
        logger.error(f"❌ Error checking database status: {e}")
        return {
            "status": "error",
            "message": str(e),
            "cluster_service": False,
            "tables_exist": False,
            "roles_exist": False,
            "admin_exists": False
        }
