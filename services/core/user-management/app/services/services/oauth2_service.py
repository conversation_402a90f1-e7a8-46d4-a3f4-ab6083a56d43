"""
OAuth2 service for handling third-party authentication.
"""
from datetime import datetime
from typing import Dict, Optional, Any, Tuple
import logging
from jose import jwt
from fastapi import HTTPException, status, Request
from sqlalchemy.orm import Session

from app.core.config import settings
from app.services.business.user_management.db.models import User
from app.services.business.user_management.models.enums import UserStatus
from app.services.business.user_management.schemas.user import Token
from app.services.business.user_management.services.auth_service import AuthService

# Configure logging
logger = logging.getLogger(__name__)

class OAuth2Service:
    """
    Service for handling OAuth2 authentication flows.
    """
    
    PROVIDERS = {
        'google': {
            'authorize_url': 'https://accounts.google.com/o/oauth2/v2/auth',
            'token_url': 'https://oauth2.googleapis.com/token',
            'userinfo_url': 'https://www.googleapis.com/oauth2/v3/userinfo',
            'scopes': ['openid', 'email', 'profile']
        },
        'github': {
            'authorize_url': 'https://github.com/login/oauth/authorize',
            'token_url': 'https://github.com/login/oauth/access_token',
            'userinfo_url': 'https://api.github.com/user',
            'email_url': 'https://api.github.com/user/emails',
            'scopes': ['user:email']
        },
        'apple': {
            'authorize_url': 'https://appleid.apple.com/auth/authorize',
            'token_url': 'https://appleid.apple.com/auth/token',
            'scopes': ['name', 'email']
        }
    }
    
    @classmethod
    def get_authorization_url(
        cls, 
        provider: str, 
        redirect_uri: str,
        state: Optional[str] = None
    ) -> str:
        """
        Get the authorization URL for the specified OAuth2 provider.
        
        Args:
            provider: The OAuth2 provider (google, github, apple)
            redirect_uri: The redirect URI after authorization
            state: Optional state parameter for CSRF protection
            
        Returns:
            str: The authorization URL
        """
        provider_config = cls.PROVIDERS.get(provider.lower())
        if not provider_config:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported OAuth provider: {provider}"
            )
            
        client_id = getattr(settings, f"{provider.upper()}_OAUTH_CLIENT_ID", None)
        if not client_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"{provider.capitalize()} OAuth client ID not configured"
            )
            
        from urllib.parse import urlencode
        params = {
            'client_id': client_id,
            'redirect_uri': redirect_uri,
            'response_type': 'code',
            'scope': ' '.join(provider_config['scopes']),
            'access_type': 'offline',
            'prompt': 'consent',
        }
        
        if state:
            params['state'] = state
            
        if provider.lower() == 'apple':
            params['response_mode'] = 'form_post'
            
        return f"{provider_config['authorize_url']}?{urlencode(params)}"
    
    @classmethod
    async def handle_oauth_callback(
        cls,
        db: Session,
        provider: str,
        code: str,
        redirect_uri: str,
    ) -> Tuple[User, bool]:
        """
        Handle OAuth callback and return the authenticated user.
        
        Args:
            db: Database session
            provider: The OAuth provider (google, github, apple)
            code: The authorization code from the OAuth provider
            redirect_uri: The redirect URI used in the authorization request
            
        Returns:
            Tuple[User, bool]: The authenticated user and a boolean indicating if the user is new
        """
        provider = provider.lower()
        if provider not in cls.PROVIDERS:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported OAuth provider: {provider}"
            )
            
        # Get OAuth tokens
        tokens = await cls._get_oauth_tokens(provider, code, redirect_uri)
        
        # Get user info
        user_info = await cls._get_user_info(provider, tokens['access_token'])
        
        # Find or create user
        user, is_new = await cls._find_or_create_user(db, provider, user_info)
        
        return user, is_new
    
    @staticmethod
    async def _get_oauth_tokens(
        provider: str, 
        code: str, 
        redirect_uri: str
    ) -> Dict[str, Any]:
        """Exchange authorization code for OAuth tokens."""
        import httpx
        
        provider_config = OAuth2Service.PROVIDERS[provider]
        client_id = getattr(settings, f"{provider.upper()}_OAUTH_CLIENT_ID")
        client_secret = getattr(settings, f"{provider.upper()}_OAUTH_CLIENT_SECRET")
        
        data = {
            'client_id': client_id,
            'client_secret': client_secret,
            'code': code,
            'grant_type': 'authorization_code',
            'redirect_uri': redirect_uri,
        }
        
        headers = {'Accept': 'application/json'}
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                provider_config['token_url'],
                data=data,
                headers=headers
            )
            
        if response.status_code != 200:
            logger.error(f"Failed to get OAuth tokens: {response.text}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to authenticate with OAuth provider"
            )
            
        return response.json()
    
    @staticmethod
    async def _get_user_info(provider: str, access_token: str) -> Dict[str, Any]:
        """Get user info from OAuth provider."""
        import httpx
        
        provider_config = OAuth2Service.PROVIDERS[provider]
        headers = {'Authorization': f'Bearer {access_token}'}
        
        async with httpx.AsyncClient() as client:
            # Get user info
            userinfo_url = provider_config['userinfo_url']
            response = await client.get(userinfo_url, headers=headers)
            
            if response.status_code != 200:
                logger.error(f"Failed to get user info: {response.text}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Failed to get user info from OAuth provider"
                )
                
            user_info = response.json()
            
            # For GitHub, we need to make an additional request to get the primary email
            if provider == 'github' and 'email' not in user_info:
                email_response = await client.get(
                    provider_config['email_url'],
                    headers=headers
                )
                if email_response.status_code == 200:
                    emails = email_response.json()
                    primary_email = next(
                        (email['email'] for email in emails if email.get('primary') and email.get('verified')),
                        None
                    )
                    if primary_email:
                        user_info['email'] = primary_email
            
            return user_info
    
    @staticmethod
    async def _find_or_create_user(
        db: Session,
        provider: str,
        user_info: Dict[str, Any]
    ) -> Tuple[User, bool]:
        """Find or create a user based on OAuth provider info."""
        from sqlalchemy import or_
        
        # Extract user info based on provider
        if provider == 'google':
            email = user_info.get('email')
            first_name = user_info.get('given_name', '')
            last_name = user_info.get('family_name', '')
            oauth_id = user_info.get('sub')
        elif provider == 'github':
            email = user_info.get('email')
            name_parts = (user_info.get('name') or '').split(' ', 1)
            first_name = name_parts[0] if name_parts else ''
            last_name = name_parts[1] if len(name_parts) > 1 else ''
            oauth_id = str(user_info.get('id'))
        elif provider == 'apple':
            email = user_info.get('email')
            name = user_info.get('name', {})
            first_name = name.get('firstName', '')
            last_name = name.get('lastName', '')
            oauth_id = user_info.get('sub')
        else:
            raise ValueError(f"Unsupported OAuth provider: {provider}")
        
        if not email or not oauth_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Could not retrieve required user information from OAuth provider"
            )
        
        # Check if user already exists with this OAuth account
        user = db.query(User).filter(
            User.oauth_provider == provider,
            User.oauth_account_id == oauth_id
        ).first()
        
        if user:
            # Update user info if needed
            update_needed = False
            if user.email != email:
                user.email = email
                update_needed = True
            if user.first_name != first_name:
                user.first_name = first_name
                update_needed = True
            if user.last_name != last_name:
                user.last_name = last_name
                update_needed = True
            
            if update_needed:
                db.add(user)
                db.commit()
                db.refresh(user)
                
            return user, False
        
        # Check if user with this email already exists
        existing_user = db.query(User).filter(User.email == email).first()
        if existing_user:
            # Link OAuth account to existing user
            existing_user.oauth_provider = provider
            existing_user.oauth_account_id = oauth_id
            existing_user.oauth_account_data = user_info
            db.add(existing_user)
            db.commit()
            db.refresh(existing_user)
            return existing_user, False
        
        # Create new user
        new_user = User(
            email=email,
            first_name=first_name,
            last_name=last_name,
            oauth_provider=provider,
            oauth_account_id=oauth_id,
            oauth_account_data=user_info,
            is_verified=True,  # OAuth providers typically verify emails
            status=UserStatus.ACTIVE
        )
        
        db.add(new_user)
        db.commit()
        db.refresh(new_user)
        
        return new_user, True


# Create a global instance
oauth2_service = OAuth2Service()
