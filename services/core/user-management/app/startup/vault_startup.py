"""
Vault Integration Startup for User Management Service

Handles Vault initialization and configuration management during service startup.
Uses HTTP API calls to vault-consul service (proper microservice architecture).
"""

import asyncio
import logging
import os
import aiohttp
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class VaultStartupManager:
    """
    Manages Vault integration during service startup.
    Uses HTTP API calls to vault-consul service.
    """

    def __init__(self):
        self.service_name = "user-management"
        self.vault_service_url = os.getenv("VAULT_SERVICE_URL", "http://localhost:7200")
        self._initialized = False
        self._config_cache: Dict[str, Any] = {}
        self._secrets_cache: Dict[str, str] = {}

    async def initialize_vault(self) -> bool:
        """Initialize Vault integration for the service."""
        try:
            logger.info(f"🔐 Initializing Vault for {self.service_name}")

            # Test connection to vault-consul service
            vault_available = await self._test_vault_connection()

            if vault_available:
                # Load configuration and secrets from vault service
                await self._load_config_from_vault()
                await self._load_secrets_from_vault()

                self._initialized = True
                logger.info(f"✅ Vault integration successful for {self.service_name}")

                # Log configuration status
                self._log_vault_status()

                return True
            else:
                logger.warning(f"⚠️ Vault service not available for {self.service_name}, using environment variables")
                return False

        except Exception as e:
            logger.error(f"❌ Vault initialization failed for {self.service_name}: {e}")
            return False

    async def _test_vault_connection(self) -> bool:
        """Test connection to vault-consul service."""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.vault_service_url}/health", timeout=aiohttp.ClientTimeout(total=5)) as response:
                    return response.status == 200
        except Exception as e:
            logger.error(f"❌ Failed to connect to vault service: {e}")
            return False

    async def _load_config_from_vault(self) -> None:
        """Load configuration from vault service."""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.vault_service_url}/api/v1/config/{self.service_name}") as response:
                    if response.status == 200:
                        config_data = await response.json()
                        self._config_cache.update(config_data.get("config", {}))
                        logger.info(f"✅ Loaded configuration from vault for {self.service_name}")
                    else:
                        logger.warning(f"⚠️ Failed to load config from vault: {response.status}")
        except Exception as e:
            logger.error(f"❌ Error loading config from vault: {e}")

    async def _load_secrets_from_vault(self) -> None:
        """Load secrets from vault service."""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.vault_service_url}/api/v1/secrets/{self.service_name}") as response:
                    if response.status == 200:
                        secrets_data = await response.json()
                        self._secrets_cache.update(secrets_data.get("secrets", {}))
                        logger.info(f"✅ Loaded secrets from vault for {self.service_name}")
                    else:
                        logger.warning(f"⚠️ Failed to load secrets from vault: {response.status}")
        except Exception as e:
            logger.error(f"❌ Error loading secrets from vault: {e}")

    def _log_vault_status(self) -> None:
        """Log Vault integration status."""
        try:
            if self._initialized:
                config_summary = {
                    "vault_enabled": True,
                    "environment": self.get_config("ENVIRONMENT"),
                    "database_configured": bool(self.get_config("DATABASE_HOST")),
                    "redis_configured": bool(self.get_config("REDIS_HOST")),
                    "mongodb_configured": bool(self.get_config("MONGODB_HOST")),
                    "secrets_available": bool(self.get_secret("JWT_SECRET_KEY"))
                }

                logger.info(f"🔐 Vault Status: {config_summary}")

        except Exception as e:
            logger.error(f"❌ Error logging Vault status: {e}")

    def get_config(self, key: str, default: Any = None) -> Any:
        """Get configuration value from Vault cache or environment."""
        if self._initialized and key in self._config_cache:
            return self._config_cache[key]
        else:
            return os.getenv(key, default)

    def get_secret(self, key: str, default: str = None) -> str:
        """Get secret value from Vault cache or environment."""
        if self._initialized and key in self._secrets_cache:
            return self._secrets_cache[key]
        else:
            return os.getenv(key, default)

    def get_database_config(self) -> Dict[str, Any]:
        """Get database configuration."""
        return {
            "host": self.get_config("DATABASE_HOST", "localhost"),
            "port": int(self.get_config("DATABASE_PORT", "5432")),
            "database": self.get_config("DATABASE_NAME", "llm_db"),
            "user": self.get_config("DATABASE_USER", "postgres"),
            "password": self.get_secret("DATABASE_PASSWORD", "postgres"),
        }

    def get_redis_config(self) -> Dict[str, Any]:
        """Get Redis configuration."""
        return {
            "host": self.get_config("REDIS_HOST", "localhost"),
            "port": int(self.get_config("REDIS_PORT", "6379")),
            "db": int(self.get_config("REDIS_DB", "0")),
            "password": self.get_secret("REDIS_PASSWORD"),
        }

    def get_mongodb_config(self) -> Dict[str, Any]:
        """Get MongoDB configuration."""
        return {
            "host": self.get_config("MONGODB_HOST", "localhost"),
            "port": int(self.get_config("MONGODB_PORT", "27017")),
            "database": self.get_config("MONGODB_DATABASE", "simba_model_db"),
            "username": self.get_config("MONGODB_USER", "admin"),
            "password": self.get_secret("MONGODB_PASSWORD", "admin"),
        }

    async def refresh_secrets(self) -> None:
        """Refresh secrets from Vault."""
        if self._initialized:
            await self._load_secrets_from_vault()
            logger.info(f"🔄 Secrets refreshed for {self.service_name}")

    async def close(self) -> None:
        """Close Vault integration."""
        try:
            self._initialized = False
            self._config_cache.clear()
            self._secrets_cache.clear()
            logger.info(f"✅ Vault integration closed for {self.service_name}")

        except Exception as e:
            logger.error(f"❌ Error closing Vault integration: {e}")


# Global instance for the service
vault_startup_manager = VaultStartupManager()


# Convenience functions for service integration
async def initialize_vault_for_service() -> bool:
    """Initialize Vault for the service."""
    return await vault_startup_manager.initialize_vault()


async def close_vault_for_service() -> None:
    """Close Vault for the service."""
    await vault_startup_manager.close()


def get_vault_config(key: str, default: Any = None) -> Any:
    """Get configuration from Vault."""
    return vault_startup_manager.get_config(key, default)


def get_vault_secret(key: str, default: str = None) -> str:
    """Get secret from Vault."""
    return vault_startup_manager.get_secret(key, default)
