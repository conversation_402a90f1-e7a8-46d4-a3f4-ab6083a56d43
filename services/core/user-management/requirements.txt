# User-Management-Service Service Dependencies
# Role: Authentication, authorization, user profiles
# Technology: FastAPI + Keycloak + PostgreSQL

fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6
loguru==0.7.2
python-decouple==3.8
requests==2.31.0
aiofiles==23.2.1
redis==5.0.1
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.7
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
pyotp==2.9.0
python-keycloak==3.7.0
# Auto-added missing packages for user-management
pydantic

# Vault integration dependencies
httpx>=0.24.0
aiohttp==3.9.1
email-validator>=2.0.0

# Testing dependencies
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
httpx==0.25.2


PyJWT==2.10.1