# Billing Service Dockerfile
# Role: Billing and subscription management
# Category: core
# Technology: FastAPI + Distributed Model Flow + Vault
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    wget \
    git \
    && rm -rf /var/lib/apt/lists/*

# Set Python path and environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Vault integration environment variables
ENV VAULT_URL=http://vault:8200
ENV VAULT_AUTH_METHOD=kubernetes
ENV SERVICE_NAME=billing
ENV ENVIRONMENT=production

# Copy shared dependencies first (for better caching)
COPY ../shared/ ./shared/

# Copy requirements and install dependencies
COPY requirements.txt .
RUN pip3 install --no-cache-dir --upgrade pip
RUN pip3 install --no-cache-dir -r requirements.txt

# Copy application code
COPY app/ ./app/
COPY main.py .

# Create directories for models and data
RUN mkdir -p /app/data/models /app/data/cache /app/logs

# Create non-root user for security
RUN useradd --create-home --shell /bin/bash --uid 1000 simbaai
RUN chown -R simbaai:simbaai /app
USER simbaai

# Expose port
EXPOSE 7309

# Health check with Vault status
HEALTHCHECK --interval=30s --timeout=30s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:7309/health || exit 1

# Run the application
CMD ["python", "main.py"]
