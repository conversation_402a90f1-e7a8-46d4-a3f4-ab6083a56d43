# Billing-Service Service Dependencies
# Role: Payment processing and subscriptions
# Technology: FastAPI + Payment Providers

fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6
loguru==0.7.2
python-decouple==3.8
requests==2.31.0
aiofiles==23.2.1
redis==5.0.1
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.7
stripe==7.8.0
paypalrestsdk==1.13.3
# Auto-added missing packages for billing
pydantic>=2.0.0
pydantic-settings>=2.0.0

# Vault integration dependencies
httpx>=0.24.0

# Payment provider dependencies
stripe>=7.8.0
requests>=2.31.0

# Database and ORM
sqlalchemy>=2.0.0
psycopg2-binary>=2.9.0
alembic>=1.12.0

# Caching and session management
redis>=5.0.0

# Authentication and security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.6

# Async support
asyncpg>=0.29.0
aioredis>=2.0.0

# Monitoring and logging
loguru>=0.7.0

# Configuration management
python-decouple>=3.8

# Date and time handling
python-dateutil>=2.8.0

# Decimal handling for financial calculations
# (built-in decimal module is sufficient)

# PDF generation for receipts and invoices
reportlab>=4.0.0

# Security dependencies
slowapi>=0.1.9  # Rate limiting
cryptography>=41.0.0  # Enhanced encryption
bcrypt>=4.0.0  # Password hashing
python-jose[cryptography]>=3.3.0  # JWT handling
pydantic[email]>=2.0.0  # Email validation

# Monitoring and metrics
prometheus-client>=0.17.0
structlog>=23.1.0  # Structured logging

# Testing dependencies (optional)
pytest>=7.0.0
pytest-asyncio>=0.21.0
httpx>=0.24.0  # for testing async endpoints
PyJWT==2.10.1
pytest-mock>=3.11.0  # Mocking for tests
factory-boy>=3.3.0  # Test data factories