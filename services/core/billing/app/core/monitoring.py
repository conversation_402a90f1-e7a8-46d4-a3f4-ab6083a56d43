"""
Monitoring and metrics collection for the Billing Service.

Provides Prometheus metrics, health checks, and performance monitoring.
"""

import time
import psutil
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from functools import wraps
from contextlib import asynccontextmanager

from prometheus_client import (
    Counter, Histogram, Gauge, Info, CollectorRegistry,
    generate_latest, CONTENT_TYPE_LATEST
)
from fastapi import Request, Response
from fastapi.responses import PlainTextResponse
import structlog

logger = structlog.get_logger(__name__)

# Create custom registry for billing service metrics
billing_registry = CollectorRegistry()

# Request metrics
request_count = Counter(
    'billing_requests_total',
    'Total number of requests',
    ['method', 'endpoint', 'status_code'],
    registry=billing_registry
)

request_duration = Histogram(
    'billing_request_duration_seconds',
    'Request duration in seconds',
    ['method', 'endpoint'],
    registry=billing_registry
)

# Business metrics
payment_count = Counter(
    'billing_payments_total',
    'Total number of payment attempts',
    ['provider', 'status'],
    registry=billing_registry
)

payment_amount = Histogram(
    'billing_payment_amount_usd',
    'Payment amounts in USD',
    ['provider', 'status'],
    buckets=[1, 5, 10, 25, 50, 100, 250, 500, 1000, float('inf')],
    registry=billing_registry
)

subscription_count = Gauge(
    'billing_active_subscriptions',
    'Number of active subscriptions',
    ['plan_type'],
    registry=billing_registry
)

usage_tracking_count = Counter(
    'billing_usage_tracking_total',
    'Total usage tracking events',
    ['service', 'usage_type'],
    registry=billing_registry
)

invoice_count = Counter(
    'billing_invoices_total',
    'Total number of invoices',
    ['status'],
    registry=billing_registry
)

# System metrics
system_info = Info(
    'billing_system_info',
    'System information',
    registry=billing_registry
)

memory_usage = Gauge(
    'billing_memory_usage_bytes',
    'Memory usage in bytes',
    registry=billing_registry
)

cpu_usage = Gauge(
    'billing_cpu_usage_percent',
    'CPU usage percentage',
    registry=billing_registry
)

# Error metrics
error_count = Counter(
    'billing_errors_total',
    'Total number of errors',
    ['error_type', 'error_code'],
    registry=billing_registry
)

# Database metrics
db_connection_pool = Gauge(
    'billing_db_connections',
    'Database connection pool status',
    ['status'],
    registry=billing_registry
)

db_query_duration = Histogram(
    'billing_db_query_duration_seconds',
    'Database query duration',
    ['operation', 'table'],
    registry=billing_registry
)

# External service metrics
external_service_duration = Histogram(
    'billing_external_service_duration_seconds',
    'External service call duration',
    ['service', 'endpoint', 'status'],
    registry=billing_registry
)

external_service_errors = Counter(
    'billing_external_service_errors_total',
    'External service errors',
    ['service', 'error_type'],
    registry=billing_registry
)


class MetricsCollector:
    """Centralized metrics collection and management."""
    
    def __init__(self):
        self.start_time = time.time()
        self._setup_system_info()
        
    def _setup_system_info(self):
        """Setup system information metrics."""
        system_info.info({
            'version': '1.0.0',
            'service': 'billing',
            'python_version': '3.11',
            'start_time': datetime.utcnow().isoformat()
        })
    
    def record_request(
        self,
        method: str,
        endpoint: str,
        status_code: int,
        duration: float
    ):
        """Record HTTP request metrics."""
        request_count.labels(
            method=method,
            endpoint=endpoint,
            status_code=str(status_code)
        ).inc()
        
        request_duration.labels(
            method=method,
            endpoint=endpoint
        ).observe(duration)
    
    def record_payment(
        self,
        provider: str,
        status: str,
        amount: float
    ):
        """Record payment metrics."""
        payment_count.labels(
            provider=provider,
            status=status
        ).inc()
        
        payment_amount.labels(
            provider=provider,
            status=status
        ).observe(amount)
    
    def record_usage_tracking(
        self,
        service: str,
        usage_type: str
    ):
        """Record usage tracking metrics."""
        usage_tracking_count.labels(
            service=service,
            usage_type=usage_type
        ).inc()
    
    def record_invoice(self, status: str):
        """Record invoice metrics."""
        invoice_count.labels(status=status).inc()
    
    def record_error(
        self,
        error_type: str,
        error_code: str
    ):
        """Record error metrics."""
        error_count.labels(
            error_type=error_type,
            error_code=error_code
        ).inc()
    
    def record_external_service_call(
        self,
        service: str,
        endpoint: str,
        status: str,
        duration: float
    ):
        """Record external service call metrics."""
        external_service_duration.labels(
            service=service,
            endpoint=endpoint,
            status=status
        ).observe(duration)
    
    def record_external_service_error(
        self,
        service: str,
        error_type: str
    ):
        """Record external service error."""
        external_service_errors.labels(
            service=service,
            error_type=error_type
        ).inc()
    
    def update_system_metrics(self):
        """Update system resource metrics."""
        try:
            # Memory usage
            memory_info = psutil.virtual_memory()
            memory_usage.set(memory_info.used)
            
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_usage.set(cpu_percent)
            
        except Exception as e:
            logger.warning("Failed to update system metrics", error=str(e))
    
    def update_subscription_metrics(self, subscription_data: Dict[str, int]):
        """Update subscription metrics."""
        for plan_type, count in subscription_data.items():
            subscription_count.labels(plan_type=plan_type).set(count)
    
    def update_db_metrics(
        self,
        active_connections: int,
        idle_connections: int,
        total_connections: int
    ):
        """Update database connection metrics."""
        db_connection_pool.labels(status='active').set(active_connections)
        db_connection_pool.labels(status='idle').set(idle_connections)
        db_connection_pool.labels(status='total').set(total_connections)


# Global metrics collector instance
metrics_collector = MetricsCollector()


def track_request_metrics(func):
    """Decorator to track request metrics."""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        request = None
        
        # Find request object in args/kwargs
        for arg in args:
            if isinstance(arg, Request):
                request = arg
                break
        
        if not request and 'request' in kwargs:
            request = kwargs['request']
        
        try:
            result = await func(*args, **kwargs)
            status_code = getattr(result, 'status_code', 200)
            
            if request:
                duration = time.time() - start_time
                metrics_collector.record_request(
                    method=request.method,
                    endpoint=request.url.path,
                    status_code=status_code,
                    duration=duration
                )
            
            return result
            
        except Exception as e:
            if request:
                duration = time.time() - start_time
                status_code = getattr(e, 'status_code', 500)
                metrics_collector.record_request(
                    method=request.method,
                    endpoint=request.url.path,
                    status_code=status_code,
                    duration=duration
                )
            raise
    
    return wrapper


@asynccontextmanager
async def track_external_service_call(
    service: str,
    endpoint: str
):
    """Context manager to track external service calls."""
    start_time = time.time()
    status = "success"
    
    try:
        yield
    except Exception as e:
        status = "error"
        metrics_collector.record_external_service_error(
            service=service,
            error_type=type(e).__name__
        )
        raise
    finally:
        duration = time.time() - start_time
        metrics_collector.record_external_service_call(
            service=service,
            endpoint=endpoint,
            status=status,
            duration=duration
        )


@asynccontextmanager
async def track_db_query(operation: str, table: str):
    """Context manager to track database queries."""
    start_time = time.time()
    
    try:
        yield
    finally:
        duration = time.time() - start_time
        db_query_duration.labels(
            operation=operation,
            table=table
        ).observe(duration)


async def get_metrics() -> str:
    """Get Prometheus metrics in text format."""
    return generate_latest(billing_registry).decode('utf-8')


async def get_health_check() -> Dict[str, Any]:
    """Get comprehensive health check information."""
    try:
        # Basic health info
        uptime = time.time() - metrics_collector.start_time
        
        # System resources
        memory_info = psutil.virtual_memory()
        cpu_percent = psutil.cpu_percent(interval=0.1)
        
        # Health status
        health_status = "healthy"
        issues = []
        
        # Check memory usage
        if memory_info.percent > 90:
            health_status = "degraded"
            issues.append("High memory usage")
        
        # Check CPU usage
        if cpu_percent > 90:
            health_status = "degraded"
            issues.append("High CPU usage")
        
        return {
            "status": health_status,
            "timestamp": datetime.utcnow().isoformat(),
            "uptime_seconds": uptime,
            "version": "1.0.0",
            "service": "billing",
            "system": {
                "memory": {
                    "total": memory_info.total,
                    "used": memory_info.used,
                    "percent": memory_info.percent
                },
                "cpu": {
                    "percent": cpu_percent
                }
            },
            "issues": issues
        }
        
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        return {
            "status": "unhealthy",
            "timestamp": datetime.utcnow().isoformat(),
            "error": str(e)
        }


# Background task to update system metrics
async def update_system_metrics_task():
    """Background task to periodically update system metrics."""
    while True:
        try:
            metrics_collector.update_system_metrics()
            await asyncio.sleep(30)  # Update every 30 seconds
        except Exception as e:
            logger.error("Failed to update system metrics", error=str(e))
            await asyncio.sleep(60)  # Wait longer on error
