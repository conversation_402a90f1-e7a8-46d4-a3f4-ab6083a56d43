"""
Custom exceptions for the Billing Service.

Defines specific exception types for different error scenarios
with proper error codes and messages for API responses.
"""

from typing import Any, Dict, Optional, List
from fastapi import HTTPException, status
import structlog

logger = structlog.get_logger(__name__)


class BillingServiceError(Exception):
    """Base exception for billing service errors."""
    
    def __init__(
        self,
        message: str,
        error_code: str = "BILLING_ERROR",
        details: Optional[Dict[str, Any]] = None,
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        self.status_code = status_code
        super().__init__(message)


class ValidationError(BillingServiceError):
    """Raised when input validation fails."""
    
    def __init__(
        self,
        message: str,
        field: Optional[str] = None,
        value: Optional[Any] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        error_details = details or {}
        if field:
            error_details["field"] = field
        if value is not None:
            error_details["invalid_value"] = str(value)
            
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            details=error_details,
            status_code=status.HTTP_400_BAD_REQUEST
        )


class PaymentError(BillingServiceError):
    """Raised when payment processing fails."""
    
    def __init__(
        self,
        message: str,
        payment_id: Optional[str] = None,
        provider: Optional[str] = None,
        provider_error: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        error_details = details or {}
        if payment_id:
            error_details["payment_id"] = payment_id
        if provider:
            error_details["provider"] = provider
        if provider_error:
            error_details["provider_error"] = provider_error
            
        super().__init__(
            message=message,
            error_code="PAYMENT_ERROR",
            details=error_details,
            status_code=status.HTTP_402_PAYMENT_REQUIRED
        )


class SubscriptionError(BillingServiceError):
    """Raised when subscription operations fail."""
    
    def __init__(
        self,
        message: str,
        subscription_id: Optional[str] = None,
        user_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        error_details = details or {}
        if subscription_id:
            error_details["subscription_id"] = subscription_id
        if user_id:
            error_details["user_id"] = user_id
            
        super().__init__(
            message=message,
            error_code="SUBSCRIPTION_ERROR",
            details=error_details,
            status_code=status.HTTP_400_BAD_REQUEST
        )


class InvoiceError(BillingServiceError):
    """Raised when invoice operations fail."""
    
    def __init__(
        self,
        message: str,
        invoice_id: Optional[str] = None,
        user_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        error_details = details or {}
        if invoice_id:
            error_details["invoice_id"] = invoice_id
        if user_id:
            error_details["user_id"] = user_id
            
        super().__init__(
            message=message,
            error_code="INVOICE_ERROR",
            details=error_details,
            status_code=status.HTTP_400_BAD_REQUEST
        )


class UsageTrackingError(BillingServiceError):
    """Raised when usage tracking fails."""
    
    def __init__(
        self,
        message: str,
        user_id: Optional[str] = None,
        service: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        error_details = details or {}
        if user_id:
            error_details["user_id"] = user_id
        if service:
            error_details["service"] = service
            
        super().__init__(
            message=message,
            error_code="USAGE_TRACKING_ERROR",
            details=error_details,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


class DatabaseError(BillingServiceError):
    """Raised when database operations fail."""
    
    def __init__(
        self,
        message: str,
        operation: Optional[str] = None,
        table: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        error_details = details or {}
        if operation:
            error_details["operation"] = operation
        if table:
            error_details["table"] = table
            
        super().__init__(
            message=message,
            error_code="DATABASE_ERROR",
            details=error_details,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


class ExternalServiceError(BillingServiceError):
    """Raised when external service calls fail."""
    
    def __init__(
        self,
        message: str,
        service: Optional[str] = None,
        endpoint: Optional[str] = None,
        status_code: Optional[int] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        error_details = details or {}
        if service:
            error_details["service"] = service
        if endpoint:
            error_details["endpoint"] = endpoint
        if status_code:
            error_details["external_status_code"] = status_code
            
        super().__init__(
            message=message,
            error_code="EXTERNAL_SERVICE_ERROR",
            details=error_details,
            status_code=status.HTTP_502_BAD_GATEWAY
        )


class ConfigurationError(BillingServiceError):
    """Raised when configuration is invalid or missing."""
    
    def __init__(
        self,
        message: str,
        config_key: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        error_details = details or {}
        if config_key:
            error_details["config_key"] = config_key
            
        super().__init__(
            message=message,
            error_code="CONFIGURATION_ERROR",
            details=error_details,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


class RateLimitError(BillingServiceError):
    """Raised when rate limits are exceeded."""
    
    def __init__(
        self,
        message: str = "Rate limit exceeded",
        limit: Optional[str] = None,
        retry_after: Optional[int] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        error_details = details or {}
        if limit:
            error_details["limit"] = limit
        if retry_after:
            error_details["retry_after"] = retry_after
            
        super().__init__(
            message=message,
            error_code="RATE_LIMIT_ERROR",
            details=error_details,
            status_code=status.HTTP_429_TOO_MANY_REQUESTS
        )


class AuthenticationError(BillingServiceError):
    """Raised when authentication fails."""
    
    def __init__(
        self,
        message: str = "Authentication failed",
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            message=message,
            error_code="AUTHENTICATION_ERROR",
            details=details,
            status_code=status.HTTP_401_UNAUTHORIZED
        )


class AuthorizationError(BillingServiceError):
    """Raised when authorization fails."""
    
    def __init__(
        self,
        message: str = "Insufficient permissions",
        required_permission: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        error_details = details or {}
        if required_permission:
            error_details["required_permission"] = required_permission
            
        super().__init__(
            message=message,
            error_code="AUTHORIZATION_ERROR",
            details=error_details,
            status_code=status.HTTP_403_FORBIDDEN
        )


class NotFoundError(BillingServiceError):
    """Raised when a resource is not found."""
    
    def __init__(
        self,
        message: str,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        error_details = details or {}
        if resource_type:
            error_details["resource_type"] = resource_type
        if resource_id:
            error_details["resource_id"] = resource_id
            
        super().__init__(
            message=message,
            error_code="NOT_FOUND_ERROR",
            details=error_details,
            status_code=status.HTTP_404_NOT_FOUND
        )


class ConflictError(BillingServiceError):
    """Raised when a resource conflict occurs."""
    
    def __init__(
        self,
        message: str,
        resource_type: Optional[str] = None,
        conflicting_field: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        error_details = details or {}
        if resource_type:
            error_details["resource_type"] = resource_type
        if conflicting_field:
            error_details["conflicting_field"] = conflicting_field
            
        super().__init__(
            message=message,
            error_code="CONFLICT_ERROR",
            details=error_details,
            status_code=status.HTTP_409_CONFLICT
        )


# Exception mapping for common errors
EXCEPTION_MAPPING = {
    "validation": ValidationError,
    "payment": PaymentError,
    "subscription": SubscriptionError,
    "invoice": InvoiceError,
    "usage": UsageTrackingError,
    "database": DatabaseError,
    "external": ExternalServiceError,
    "config": ConfigurationError,
    "rate_limit": RateLimitError,
    "auth": AuthenticationError,
    "authz": AuthorizationError,
    "not_found": NotFoundError,
    "conflict": ConflictError,
}


def create_exception(
    error_type: str,
    message: str,
    **kwargs
) -> BillingServiceError:
    """
    Factory function to create appropriate exception type.
    
    Args:
        error_type: Type of error (key from EXCEPTION_MAPPING)
        message: Error message
        **kwargs: Additional arguments for specific exception types
        
    Returns:
        Appropriate exception instance
    """
    exception_class = EXCEPTION_MAPPING.get(error_type, BillingServiceError)
    return exception_class(message, **kwargs)


def log_exception(
    exception: Exception,
    context: Optional[Dict[str, Any]] = None,
    user_id: Optional[str] = None,
    request_id: Optional[str] = None
):
    """
    Log exception with structured context.
    
    Args:
        exception: Exception to log
        context: Additional context information
        user_id: User ID if available
        request_id: Request ID if available
    """
    log_data = {
        "exception_type": type(exception).__name__,
        "exception_message": str(exception),
    }
    
    if context:
        log_data.update(context)
    
    if user_id:
        log_data["user_id"] = user_id
        
    if request_id:
        log_data["request_id"] = request_id
    
    if isinstance(exception, BillingServiceError):
        log_data.update({
            "error_code": exception.error_code,
            "status_code": exception.status_code,
            "details": exception.details
        })
        
        # Log at appropriate level based on status code
        if exception.status_code >= 500:
            logger.error("Billing service error", **log_data)
        elif exception.status_code >= 400:
            logger.warning("Client error", **log_data)
        else:
            logger.info("Exception occurred", **log_data)
    else:
        logger.error("Unexpected exception", **log_data, exc_info=True)
