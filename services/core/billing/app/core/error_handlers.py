"""
Error handlers for the Billing Service.

Provides centralized error handling with proper HTTP responses,
logging, and error tracking.
"""

import traceback
import uuid
from typing import Dict, Any, Optional
from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
from sqlalchemy.exc import SQLAlchemyError, IntegrityError, OperationalError
from pydantic import ValidationError as PydanticValidationError
import structlog

from app.core.exceptions import (
    BillingServiceError,
    ValidationError,
    PaymentError,
    DatabaseError,
    ExternalServiceError,
    log_exception
)

logger = structlog.get_logger(__name__)


class ErrorResponse:
    """Standardized error response format."""
    
    def __init__(
        self,
        error_code: str,
        message: str,
        details: Optional[Dict[str, Any]] = None,
        request_id: Optional[str] = None,
        timestamp: Optional[str] = None
    ):
        self.error_code = error_code
        self.message = message
        self.details = details or {}
        self.request_id = request_id or str(uuid.uuid4())
        self.timestamp = timestamp
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON response."""
        response = {
            "error": {
                "code": self.error_code,
                "message": self.message,
                "request_id": self.request_id
            }
        }
        
        if self.details:
            response["error"]["details"] = self.details
            
        if self.timestamp:
            response["error"]["timestamp"] = self.timestamp
            
        return response


async def billing_service_error_handler(
    request: Request,
    exc: BillingServiceError
) -> JSONResponse:
    """Handle custom billing service errors."""
    request_id = getattr(request.state, "request_id", str(uuid.uuid4()))
    
    # Log the exception
    log_exception(
        exc,
        context={
            "path": request.url.path,
            "method": request.method,
            "client_ip": request.client.host if request.client else None
        },
        request_id=request_id
    )
    
    # Create error response
    error_response = ErrorResponse(
        error_code=exc.error_code,
        message=exc.message,
        details=exc.details,
        request_id=request_id
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response.to_dict(),
        headers={"X-Request-ID": request_id}
    )


async def http_exception_handler(
    request: Request,
    exc: HTTPException
) -> JSONResponse:
    """Handle FastAPI HTTP exceptions."""
    request_id = getattr(request.state, "request_id", str(uuid.uuid4()))
    
    # Map common HTTP status codes to error codes
    error_code_mapping = {
        400: "BAD_REQUEST",
        401: "UNAUTHORIZED",
        403: "FORBIDDEN",
        404: "NOT_FOUND",
        405: "METHOD_NOT_ALLOWED",
        409: "CONFLICT",
        422: "UNPROCESSABLE_ENTITY",
        429: "RATE_LIMITED",
        500: "INTERNAL_SERVER_ERROR",
        502: "BAD_GATEWAY",
        503: "SERVICE_UNAVAILABLE",
        504: "GATEWAY_TIMEOUT"
    }
    
    error_code = error_code_mapping.get(exc.status_code, "HTTP_ERROR")
    
    logger.warning(
        "HTTP exception",
        status_code=exc.status_code,
        detail=exc.detail,
        path=request.url.path,
        method=request.method,
        request_id=request_id
    )
    
    error_response = ErrorResponse(
        error_code=error_code,
        message=str(exc.detail),
        request_id=request_id
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response.to_dict(),
        headers={"X-Request-ID": request_id}
    )


async def validation_error_handler(
    request: Request,
    exc: RequestValidationError
) -> JSONResponse:
    """Handle Pydantic validation errors."""
    request_id = getattr(request.state, "request_id", str(uuid.uuid4()))
    
    # Extract validation details
    validation_details = []
    for error in exc.errors():
        field_path = " -> ".join(str(loc) for loc in error["loc"])
        validation_details.append({
            "field": field_path,
            "message": error["msg"],
            "type": error["type"],
            "input": error.get("input")
        })
    
    logger.warning(
        "Validation error",
        validation_errors=validation_details,
        path=request.url.path,
        method=request.method,
        request_id=request_id
    )
    
    error_response = ErrorResponse(
        error_code="VALIDATION_ERROR",
        message="Request validation failed",
        details={"validation_errors": validation_details},
        request_id=request_id
    )
    
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=error_response.to_dict(),
        headers={"X-Request-ID": request_id}
    )


async def sqlalchemy_error_handler(
    request: Request,
    exc: SQLAlchemyError
) -> JSONResponse:
    """Handle SQLAlchemy database errors."""
    request_id = getattr(request.state, "request_id", str(uuid.uuid4()))
    
    # Determine error type and message
    if isinstance(exc, IntegrityError):
        error_code = "DATABASE_INTEGRITY_ERROR"
        message = "Database integrity constraint violation"
        status_code = status.HTTP_409_CONFLICT
        
        # Extract constraint details if available
        details = {}
        if hasattr(exc, 'orig') and exc.orig:
            details["constraint_error"] = str(exc.orig)
            
    elif isinstance(exc, OperationalError):
        error_code = "DATABASE_OPERATIONAL_ERROR"
        message = "Database operation failed"
        status_code = status.HTTP_503_SERVICE_UNAVAILABLE
        details = {"operation_error": "Database temporarily unavailable"}
        
    else:
        error_code = "DATABASE_ERROR"
        message = "Database error occurred"
        status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        details = {}
    
    # Log the error (without sensitive details)
    logger.error(
        "Database error",
        error_type=type(exc).__name__,
        error_code=error_code,
        path=request.url.path,
        method=request.method,
        request_id=request_id
    )
    
    error_response = ErrorResponse(
        error_code=error_code,
        message=message,
        details=details,
        request_id=request_id
    )
    
    return JSONResponse(
        status_code=status_code,
        content=error_response.to_dict(),
        headers={"X-Request-ID": request_id}
    )


async def generic_exception_handler(
    request: Request,
    exc: Exception
) -> JSONResponse:
    """Handle unexpected exceptions."""
    request_id = getattr(request.state, "request_id", str(uuid.uuid4()))
    
    # Log the full exception with traceback
    logger.error(
        "Unexpected exception",
        exception_type=type(exc).__name__,
        exception_message=str(exc),
        path=request.url.path,
        method=request.method,
        request_id=request_id,
        traceback=traceback.format_exc()
    )
    
    # Don't expose internal error details in production
    error_response = ErrorResponse(
        error_code="INTERNAL_SERVER_ERROR",
        message="An unexpected error occurred",
        request_id=request_id
    )
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=error_response.to_dict(),
        headers={"X-Request-ID": request_id}
    )


def setup_error_handlers(app):
    """Setup all error handlers for the FastAPI app."""
    
    # Custom billing service errors
    app.add_exception_handler(BillingServiceError, billing_service_error_handler)
    
    # FastAPI HTTP exceptions
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)
    
    # Validation errors
    app.add_exception_handler(RequestValidationError, validation_error_handler)
    
    # Database errors
    app.add_exception_handler(SQLAlchemyError, sqlalchemy_error_handler)
    
    # Generic exception handler (catch-all)
    app.add_exception_handler(Exception, generic_exception_handler)
    
    logger.info("Error handlers configured")


# Utility functions for error handling
def create_error_response(
    error_code: str,
    message: str,
    status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
    details: Optional[Dict[str, Any]] = None,
    request_id: Optional[str] = None
) -> JSONResponse:
    """Create a standardized error response."""
    error_response = ErrorResponse(
        error_code=error_code,
        message=message,
        details=details,
        request_id=request_id or str(uuid.uuid4())
    )
    
    return JSONResponse(
        status_code=status_code,
        content=error_response.to_dict(),
        headers={"X-Request-ID": error_response.request_id}
    )


def handle_payment_provider_error(
    provider: str,
    error: Exception,
    payment_id: Optional[str] = None
) -> PaymentError:
    """Convert payment provider errors to standardized format."""
    provider_error_msg = str(error)
    
    # Map common provider errors
    if "insufficient_funds" in provider_error_msg.lower():
        message = "Insufficient funds for payment"
    elif "card_declined" in provider_error_msg.lower():
        message = "Payment method declined"
    elif "expired" in provider_error_msg.lower():
        message = "Payment method expired"
    elif "invalid" in provider_error_msg.lower():
        message = "Invalid payment information"
    else:
        message = f"Payment processing failed via {provider}"
    
    return PaymentError(
        message=message,
        payment_id=payment_id,
        provider=provider,
        provider_error=provider_error_msg
    )


def handle_database_error(
    operation: str,
    error: Exception,
    table: Optional[str] = None
) -> DatabaseError:
    """Convert database errors to standardized format."""
    if isinstance(error, IntegrityError):
        message = f"Database integrity error during {operation}"
    elif isinstance(error, OperationalError):
        message = f"Database operation failed: {operation}"
    else:
        message = f"Database error during {operation}"
    
    return DatabaseError(
        message=message,
        operation=operation,
        table=table,
        details={"original_error": str(error)}
    )
