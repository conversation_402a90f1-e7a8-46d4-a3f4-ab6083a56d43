"""
Database migration management for the Billing Service.

Provides automated database schema management, indexing strategies, and migration utilities.
"""

import os
import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime
from pathlib import Path
from sqlalchemy import text, MetaData, Table, Index, Column, String, DateTime, Boolean
from sqlalchemy.ext.asyncio import AsyncSession
import structlog

from app.core.database import db_manager
from app.core.exceptions import DatabaseError

logger = structlog.get_logger(__name__)


class MigrationManager:
    """Database migration and schema management."""
    
    def __init__(self):
        self.migrations_table = "billing_migrations"
        self.migrations_dir = Path(__file__).parent.parent / "migrations"
        self.migrations_dir.mkdir(exist_ok=True)
    
    async def ensure_migrations_table(self, session: AsyncSession):
        """Ensure the migrations tracking table exists."""
        create_table_sql = f"""
        CREATE TABLE IF NOT EXISTS {self.migrations_table} (
            id SERIAL PRIMARY KEY,
            migration_name VARCHAR(255) NOT NULL UNIQUE,
            applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            checksum VARCHAR(64),
            execution_time_ms INTEGER,
            success BOOLEAN DEFAULT TRUE
        );
        
        CREATE INDEX IF NOT EXISTS idx_migrations_name 
        ON {self.migrations_table} (migration_name);
        
        CREATE INDEX IF NOT EXISTS idx_migrations_applied_at 
        ON {self.migrations_table} (applied_at);
        """
        
        try:
            await session.execute(text(create_table_sql))
            await session.commit()
            logger.info("Migrations table ensured")
        except Exception as e:
            await session.rollback()
            logger.error("Failed to create migrations table", error=str(e))
            raise DatabaseError(
                "Failed to create migrations table",
                operation="create_migrations_table",
                details={"error": str(e)}
            )
    
    async def get_applied_migrations(self, session: AsyncSession) -> List[str]:
        """Get list of applied migrations."""
        try:
            result = await session.execute(
                text(f"SELECT migration_name FROM {self.migrations_table} WHERE success = TRUE ORDER BY applied_at")
            )
            return [row[0] for row in result.fetchall()]
        except Exception as e:
            logger.error("Failed to get applied migrations", error=str(e))
            return []
    
    async def record_migration(
        self,
        session: AsyncSession,
        migration_name: str,
        execution_time_ms: int,
        checksum: str,
        success: bool = True
    ):
        """Record a migration execution."""
        try:
            await session.execute(
                text(f"""
                INSERT INTO {self.migrations_table} 
                (migration_name, execution_time_ms, checksum, success)
                VALUES (:name, :time, :checksum, :success)
                """),
                {
                    "name": migration_name,
                    "time": execution_time_ms,
                    "checksum": checksum,
                    "success": success
                }
            )
            await session.commit()
        except Exception as e:
            await session.rollback()
            logger.error("Failed to record migration", migration=migration_name, error=str(e))
            raise
    
    async def run_migration(self, session: AsyncSession, migration_sql: str, migration_name: str):
        """Execute a single migration."""
        start_time = datetime.now()
        
        try:
            # Execute migration SQL
            for statement in migration_sql.split(';'):
                statement = statement.strip()
                if statement:
                    await session.execute(text(statement))
            
            await session.commit()
            
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            # Record successful migration
            await self.record_migration(
                session,
                migration_name,
                execution_time,
                self._calculate_checksum(migration_sql),
                success=True
            )
            
            logger.info("Migration applied successfully", 
                       migration=migration_name, 
                       execution_time_ms=execution_time)
            
        except Exception as e:
            await session.rollback()
            
            execution_time = int((datetime.now() - start_time).total_seconds() * 1000)
            
            # Record failed migration
            try:
                await self.record_migration(
                    session,
                    migration_name,
                    execution_time,
                    self._calculate_checksum(migration_sql),
                    success=False
                )
            except:
                pass  # Don't fail if we can't record the failure
            
            logger.error("Migration failed", migration=migration_name, error=str(e))
            raise DatabaseError(
                f"Migration {migration_name} failed",
                operation="run_migration",
                details={"migration": migration_name, "error": str(e)}
            )
    
    def _calculate_checksum(self, content: str) -> str:
        """Calculate checksum for migration content."""
        import hashlib
        return hashlib.sha256(content.encode()).hexdigest()[:16]
    
    async def apply_migrations(self):
        """Apply all pending migrations."""
        async with db_manager.get_async_session() as session:
            # Ensure migrations table exists
            await self.ensure_migrations_table(session)
            
            # Get applied migrations
            applied_migrations = await self.get_applied_migrations(session)
            
            # Get available migrations
            migration_files = sorted([
                f for f in self.migrations_dir.glob("*.sql")
                if f.is_file()
            ])
            
            pending_migrations = [
                f for f in migration_files
                if f.stem not in applied_migrations
            ]
            
            if not pending_migrations:
                logger.info("No pending migrations")
                return
            
            logger.info(f"Applying {len(pending_migrations)} pending migrations")
            
            for migration_file in pending_migrations:
                migration_name = migration_file.stem
                migration_sql = migration_file.read_text()
                
                logger.info(f"Applying migration: {migration_name}")
                await self.run_migration(session, migration_sql, migration_name)
            
            logger.info("All migrations applied successfully")


class IndexManager:
    """Database index management and optimization."""
    
    def __init__(self):
        self.recommended_indexes = self._get_recommended_indexes()
    
    def _get_recommended_indexes(self) -> List[Dict[str, Any]]:
        """Get recommended indexes for billing service tables."""
        return [
            # Users table indexes
            {
                "table": "users",
                "name": "idx_users_email",
                "columns": ["email"],
                "unique": True,
                "description": "Unique index for user email lookups"
            },
            {
                "table": "users",
                "name": "idx_users_created_at",
                "columns": ["created_at"],
                "description": "Index for user creation date queries"
            },
            
            # Subscriptions table indexes
            {
                "table": "subscriptions",
                "name": "idx_subscriptions_user_id",
                "columns": ["user_id"],
                "description": "Index for user subscription lookups"
            },
            {
                "table": "subscriptions",
                "name": "idx_subscriptions_status",
                "columns": ["status"],
                "description": "Index for subscription status filtering"
            },
            {
                "table": "subscriptions",
                "name": "idx_subscriptions_plan_id",
                "columns": ["plan_id"],
                "description": "Index for plan-based queries"
            },
            {
                "table": "subscriptions",
                "name": "idx_subscriptions_billing_cycle",
                "columns": ["next_billing_date"],
                "description": "Index for billing cycle processing"
            },
            
            # Payments table indexes
            {
                "table": "payments",
                "name": "idx_payments_user_id",
                "columns": ["user_id"],
                "description": "Index for user payment history"
            },
            {
                "table": "payments",
                "name": "idx_payments_status",
                "columns": ["status"],
                "description": "Index for payment status filtering"
            },
            {
                "table": "payments",
                "name": "idx_payments_provider",
                "columns": ["provider"],
                "description": "Index for provider-specific queries"
            },
            {
                "table": "payments",
                "name": "idx_payments_created_at",
                "columns": ["created_at"],
                "description": "Index for payment date range queries"
            },
            {
                "table": "payments",
                "name": "idx_payments_external_id",
                "columns": ["external_payment_id"],
                "unique": True,
                "description": "Unique index for external payment ID lookups"
            },
            
            # Invoices table indexes
            {
                "table": "invoices",
                "name": "idx_invoices_user_id",
                "columns": ["user_id"],
                "description": "Index for user invoice queries"
            },
            {
                "table": "invoices",
                "name": "idx_invoices_status",
                "columns": ["status"],
                "description": "Index for invoice status filtering"
            },
            {
                "table": "invoices",
                "name": "idx_invoices_due_date",
                "columns": ["due_date"],
                "description": "Index for overdue invoice processing"
            },
            {
                "table": "invoices",
                "name": "idx_invoices_billing_period",
                "columns": ["billing_period_start", "billing_period_end"],
                "description": "Composite index for billing period queries"
            },
            
            # Usage tracking table indexes
            {
                "table": "usage_records",
                "name": "idx_usage_user_service",
                "columns": ["user_id", "service", "usage_type"],
                "description": "Composite index for user usage queries"
            },
            {
                "table": "usage_records",
                "name": "idx_usage_timestamp",
                "columns": ["timestamp"],
                "description": "Index for time-based usage queries"
            },
            {
                "table": "usage_records",
                "name": "idx_usage_billing_period",
                "columns": ["billing_period"],
                "description": "Index for billing period aggregation"
            },
            
            # Webhook events table indexes
            {
                "table": "webhook_events",
                "name": "idx_webhook_provider_event",
                "columns": ["provider", "event_type"],
                "description": "Index for webhook event processing"
            },
            {
                "table": "webhook_events",
                "name": "idx_webhook_status",
                "columns": ["status"],
                "description": "Index for webhook status tracking"
            },
            {
                "table": "webhook_events",
                "name": "idx_webhook_created_at",
                "columns": ["created_at"],
                "description": "Index for webhook event chronology"
            }
        ]
    
    async def create_index(self, session: AsyncSession, index_config: Dict[str, Any]):
        """Create a single index."""
        table_name = index_config["table"]
        index_name = index_config["name"]
        columns = index_config["columns"]
        unique = index_config.get("unique", False)
        
        # Build CREATE INDEX statement
        unique_clause = "UNIQUE " if unique else ""
        columns_clause = ", ".join(columns)
        
        create_index_sql = f"""
        CREATE {unique_clause}INDEX IF NOT EXISTS {index_name}
        ON {table_name} ({columns_clause});
        """
        
        try:
            await session.execute(text(create_index_sql))
            logger.info(f"Created index: {index_name} on {table_name}")
        except Exception as e:
            logger.error(f"Failed to create index {index_name}", error=str(e))
            raise
    
    async def create_all_indexes(self):
        """Create all recommended indexes."""
        async with db_manager.get_async_session() as session:
            logger.info("Creating recommended database indexes")
            
            for index_config in self.recommended_indexes:
                try:
                    await self.create_index(session, index_config)
                except Exception as e:
                    logger.warning(
                        f"Failed to create index {index_config['name']}",
                        error=str(e)
                    )
            
            await session.commit()
            logger.info("Database indexes creation completed")
    
    async def analyze_table_performance(self, session: AsyncSession, table_name: str) -> Dict[str, Any]:
        """Analyze table performance and suggest optimizations."""
        try:
            # Get table statistics
            stats_query = f"""
            SELECT 
                schemaname,
                tablename,
                attname,
                n_distinct,
                correlation
            FROM pg_stats 
            WHERE tablename = '{table_name}';
            """
            
            result = await session.execute(text(stats_query))
            stats = result.fetchall()
            
            # Get table size
            size_query = f"""
            SELECT 
                pg_size_pretty(pg_total_relation_size('{table_name}')) as total_size,
                pg_size_pretty(pg_relation_size('{table_name}')) as table_size,
                pg_size_pretty(pg_total_relation_size('{table_name}') - pg_relation_size('{table_name}')) as index_size
            """
            
            size_result = await session.execute(text(size_query))
            size_info = size_result.fetchone()
            
            return {
                "table_name": table_name,
                "statistics": [dict(row._mapping) for row in stats],
                "size_info": dict(size_info._mapping) if size_info else {},
                "recommendations": self._generate_recommendations(table_name, stats)
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze table {table_name}", error=str(e))
            return {"error": str(e)}
    
    def _generate_recommendations(self, table_name: str, stats) -> List[str]:
        """Generate performance recommendations based on table statistics."""
        recommendations = []
        
        # Check for missing indexes on frequently queried columns
        high_cardinality_columns = [
            stat for stat in stats 
            if stat.n_distinct and stat.n_distinct > 100
        ]
        
        if high_cardinality_columns:
            recommendations.append(
                f"Consider adding indexes on high-cardinality columns: "
                f"{', '.join([col.attname for col in high_cardinality_columns])}"
            )
        
        # Check for correlation issues
        low_correlation_columns = [
            stat for stat in stats
            if stat.correlation and abs(stat.correlation) < 0.1
        ]
        
        if low_correlation_columns:
            recommendations.append(
                f"Consider clustering or reordering data for columns with low correlation: "
                f"{', '.join([col.attname for col in low_correlation_columns])}"
            )
        
        return recommendations


# Global instances
migration_manager = MigrationManager()
index_manager = IndexManager()


# Initialization function
async def initialize_database_schema():
    """Initialize database schema with migrations and indexes."""
    try:
        logger.info("Initializing database schema")
        
        # Apply migrations
        await migration_manager.apply_migrations()
        
        # Create indexes
        await index_manager.create_all_indexes()
        
        logger.info("Database schema initialization completed")
        
    except Exception as e:
        logger.error("Database schema initialization failed", error=str(e))
        raise DatabaseError(
            "Failed to initialize database schema",
            operation="schema_initialization",
            details={"error": str(e)}
        )
