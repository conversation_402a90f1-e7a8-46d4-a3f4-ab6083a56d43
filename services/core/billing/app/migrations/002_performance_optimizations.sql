-- Performance optimizations and additional indexes
-- Adds partitioning, materialized views, and performance enhancements

-- Create partitioned table for usage records (by month)
-- First, rename existing table
ALTER TABLE IF EXISTS usage_records RENAME TO usage_records_old;

-- Create new partitioned table
CREATE TABLE usage_records (
    id UUID DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    subscription_id UUID,
    service VARCHAR(100) NOT NULL,
    usage_type VARCHAR(100) NOT NULL,
    amount INTEGER NOT NULL,
    unit_cost DECIMAL(10,6) DEFAULT 0,
    total_cost DECIMAL(10,2) DEFAULT 0,
    billing_period VARCHAR(20) NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (id, billing_period)
) PARTITION BY LIST (billing_period);

-- Create partitions for current and next few months
CREATE TABLE usage_records_2024_01 PARTITION OF usage_records FOR VALUES IN ('2024-01');
CREATE TABLE usage_records_2024_02 PARTITION OF usage_records FOR VALUES IN ('2024-02');
CREATE TABLE usage_records_2024_03 PARTITION OF usage_records FOR VALUES IN ('2024-03');
CREATE TABLE usage_records_2024_04 PARTITION OF usage_records FOR VALUES IN ('2024-04');
CREATE TABLE usage_records_2024_05 PARTITION OF usage_records FOR VALUES IN ('2024-05');
CREATE TABLE usage_records_2024_06 PARTITION OF usage_records FOR VALUES IN ('2024-06');
CREATE TABLE usage_records_2024_07 PARTITION OF usage_records FOR VALUES IN ('2024-07');
CREATE TABLE usage_records_2024_08 PARTITION OF usage_records FOR VALUES IN ('2024-08');
CREATE TABLE usage_records_2024_09 PARTITION OF usage_records FOR VALUES IN ('2024-09');
CREATE TABLE usage_records_2024_10 PARTITION OF usage_records FOR VALUES IN ('2024-10');
CREATE TABLE usage_records_2024_11 PARTITION OF usage_records FOR VALUES IN ('2024-11');
CREATE TABLE usage_records_2024_12 PARTITION OF usage_records FOR VALUES IN ('2024-12');
CREATE TABLE usage_records_2025_01 PARTITION OF usage_records FOR VALUES IN ('2025-01');
CREATE TABLE usage_records_2025_02 PARTITION OF usage_records FOR VALUES IN ('2025-02');
CREATE TABLE usage_records_2025_03 PARTITION OF usage_records FOR VALUES IN ('2025-03');
CREATE TABLE usage_records_2025_04 PARTITION OF usage_records FOR VALUES IN ('2025-04');
CREATE TABLE usage_records_2025_05 PARTITION OF usage_records FOR VALUES IN ('2025-05');
CREATE TABLE usage_records_2025_06 PARTITION OF usage_records FOR VALUES IN ('2025-06');

-- Migrate data from old table if it exists
INSERT INTO usage_records SELECT * FROM usage_records_old WHERE EXISTS (SELECT 1 FROM usage_records_old);

-- Drop old table
DROP TABLE IF EXISTS usage_records_old;

-- Add foreign key constraints to partitioned table
ALTER TABLE usage_records ADD CONSTRAINT fk_usage_records_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
ALTER TABLE usage_records ADD CONSTRAINT fk_usage_records_subscription_id FOREIGN KEY (subscription_id) REFERENCES subscriptions(id) ON DELETE SET NULL;

-- Create materialized view for monthly usage aggregation
CREATE MATERIALIZED VIEW IF NOT EXISTS monthly_usage_summary AS
SELECT 
    user_id,
    service,
    usage_type,
    billing_period,
    SUM(amount) as total_amount,
    SUM(total_cost) as total_cost,
    COUNT(*) as record_count,
    MIN(timestamp) as period_start,
    MAX(timestamp) as period_end
FROM usage_records
GROUP BY user_id, service, usage_type, billing_period;

-- Create unique index on materialized view
CREATE UNIQUE INDEX idx_monthly_usage_summary_unique 
ON monthly_usage_summary (user_id, service, usage_type, billing_period);

-- Create materialized view for subscription metrics
CREATE MATERIALIZED VIEW IF NOT EXISTS subscription_metrics AS
SELECT 
    sp.id as plan_id,
    sp.name as plan_name,
    sp.price as plan_price,
    sp.currency as plan_currency,
    COUNT(s.id) as active_subscriptions,
    SUM(CASE WHEN s.status = 'active' THEN sp.price ELSE 0 END) as monthly_recurring_revenue,
    AVG(EXTRACT(EPOCH FROM (s.updated_at - s.created_at))/86400) as avg_subscription_age_days,
    COUNT(CASE WHEN s.cancel_at_period_end = true THEN 1 END) as pending_cancellations
FROM subscription_plans sp
LEFT JOIN subscriptions s ON sp.id = s.plan_id
GROUP BY sp.id, sp.name, sp.price, sp.currency;

-- Create index on subscription metrics
CREATE UNIQUE INDEX idx_subscription_metrics_plan_id ON subscription_metrics (plan_id);

-- Create materialized view for payment analytics
CREATE MATERIALIZED VIEW IF NOT EXISTS payment_analytics AS
SELECT 
    DATE_TRUNC('month', created_at) as month,
    provider,
    status,
    currency,
    COUNT(*) as transaction_count,
    SUM(amount) as total_amount,
    AVG(amount) as avg_amount,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful_count,
    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count,
    ROUND(
        COUNT(CASE WHEN status = 'completed' THEN 1 END)::DECIMAL / 
        NULLIF(COUNT(*), 0) * 100, 2
    ) as success_rate_percent
FROM payments
GROUP BY DATE_TRUNC('month', created_at), provider, status, currency;

-- Create index on payment analytics
CREATE INDEX idx_payment_analytics_month_provider ON payment_analytics (month, provider);

-- Add additional performance indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_subscriptions_user_status ON subscriptions (user_id, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_subscriptions_next_billing ON subscriptions (next_billing_date) WHERE status = 'active';
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_user_created ON payments (user_id, created_at DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_provider_status ON payments (provider, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_invoices_user_status ON invoices (user_id, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_invoices_due_date ON invoices (due_date) WHERE status IN ('sent', 'overdue');
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_webhook_events_provider_status ON webhook_events (provider, status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_usage_records_user_service ON usage_records (user_id, service, usage_type);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_usage_records_timestamp ON usage_records (timestamp);

-- Create partial indexes for common queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_subscriptions_active ON subscriptions (user_id, plan_id) WHERE status = 'active';
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_pending ON payments (created_at) WHERE status = 'pending';
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_invoices_overdue ON invoices (due_date, user_id) WHERE status = 'overdue';

-- Create composite indexes for complex queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_user_provider_status ON payments (user_id, provider, status, created_at DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_usage_billing_period_user ON usage_records (billing_period, user_id, service);

-- Add GIN indexes for JSONB columns
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_subscription_plans_features_gin ON subscription_plans USING GIN (features);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_subscriptions_metadata_gin ON subscriptions USING GIN (metadata);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_metadata_gin ON payments USING GIN (metadata);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_usage_records_metadata_gin ON usage_records USING GIN (metadata);

-- Create function to refresh materialized views
CREATE OR REPLACE FUNCTION refresh_billing_analytics()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY monthly_usage_summary;
    REFRESH MATERIALIZED VIEW CONCURRENTLY subscription_metrics;
    REFRESH MATERIALIZED VIEW CONCURRENTLY payment_analytics;
END;
$$ LANGUAGE plpgsql;

-- Create function to automatically create usage record partitions
CREATE OR REPLACE FUNCTION create_usage_partition(partition_date TEXT)
RETURNS void AS $$
DECLARE
    partition_name TEXT;
    start_date DATE;
    end_date DATE;
BEGIN
    partition_name := 'usage_records_' || partition_date;
    
    -- Check if partition already exists
    IF NOT EXISTS (
        SELECT 1 FROM pg_class c
        JOIN pg_namespace n ON n.oid = c.relnamespace
        WHERE c.relname = partition_name AND n.nspname = 'public'
    ) THEN
        EXECUTE format('CREATE TABLE %I PARTITION OF usage_records FOR VALUES IN (%L)', 
                      partition_name, partition_date);
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Create function to clean up old webhook events
CREATE OR REPLACE FUNCTION cleanup_old_webhook_events()
RETURNS void AS $$
BEGIN
    DELETE FROM webhook_events 
    WHERE status = 'processed' 
    AND created_at < NOW() - INTERVAL '30 days';
END;
$$ LANGUAGE plpgsql;

-- Add table statistics for query planner
ANALYZE subscription_plans;
ANALYZE users;
ANALYZE subscriptions;
ANALYZE payments;
ANALYZE invoices;
ANALYZE usage_records;
ANALYZE webhook_events;
ANALYZE payment_methods;
ANALYZE billing_addresses;
ANALYZE coupons;
ANALYZE coupon_redemptions;
