"""
Security middleware for the Billing Service.

Implements comprehensive security measures including:
- Rate limiting
- Request validation
- Security headers
- PCI compliance helpers
- Input sanitization
"""

import time
import hashlib
import ipaddress
from typing import Dict, List, Optional, Set
from fastapi import Request, Response, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
from slowapi.middleware import SlowAPIMiddleware
import structlog
from datetime import datetime, timedelta
import re

logger = structlog.get_logger(__name__)

# Rate limiter configuration
limiter = Limiter(key_func=get_remote_address)

# Security configuration
SECURITY_CONFIG = {
    "rate_limits": {
        "default": "100/minute",
        "payment": "10/minute",
        "webhook": "1000/minute",
        "auth": "5/minute"
    },
    "blocked_ips": set(),
    "allowed_origins": [
        "https://simbaai.com",
        "https://app.simbaai.com",
        "https://api.simbaai.com"
    ],
    "max_request_size": 1024 * 1024,  # 1MB
    "sensitive_fields": {
        "card_number", "cvv", "ssn", "account_number", 
        "routing_number", "pin", "password", "secret"
    }
}

# PCI DSS compliance patterns
PCI_PATTERNS = {
    "card_number": re.compile(r'\b(?:\d{4}[-\s]?){3}\d{4}\b'),
    "cvv": re.compile(r'\b\d{3,4}\b'),
    "ssn": re.compile(r'\b\d{3}-?\d{2}-?\d{4}\b'),
}


class SecurityMiddleware:
    """Comprehensive security middleware for billing service."""
    
    def __init__(self):
        self.blocked_ips: Set[str] = set()
        self.suspicious_patterns: Dict[str, int] = {}
        self.request_cache: Dict[str, List[float]] = {}
        
    async def __call__(self, request: Request, call_next):
        """Process request through security checks."""
        start_time = time.time()
        
        try:
            # Pre-request security checks
            await self._check_ip_blocking(request)
            await self._check_request_size(request)
            await self._check_suspicious_patterns(request)
            await self._sanitize_request(request)
            
            # Process request
            response = await call_next(request)
            
            # Post-request security headers
            self._add_security_headers(response)
            
            # Log security event
            await self._log_security_event(request, response, time.time() - start_time)
            
            return response
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error("Security middleware error", error=str(e), path=request.url.path)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Security check failed"
            )
    
    async def _check_ip_blocking(self, request: Request):
        """Check if IP is blocked."""
        client_ip = get_remote_address(request)
        
        if client_ip in self.blocked_ips:
            logger.warning("Blocked IP attempted access", ip=client_ip)
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
    
    async def _check_request_size(self, request: Request):
        """Check request size limits."""
        content_length = request.headers.get("content-length")
        if content_length and int(content_length) > SECURITY_CONFIG["max_request_size"]:
            logger.warning("Request size exceeded", size=content_length)
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail="Request too large"
            )
    
    async def _check_suspicious_patterns(self, request: Request):
        """Check for suspicious request patterns."""
        path = request.url.path
        user_agent = request.headers.get("user-agent", "")
        
        # Check for common attack patterns
        suspicious_patterns = [
            "sql", "union", "select", "drop", "delete", "insert",
            "script", "javascript", "eval", "exec", "system"
        ]
        
        path_lower = path.lower()
        ua_lower = user_agent.lower()
        
        for pattern in suspicious_patterns:
            if pattern in path_lower or pattern in ua_lower:
                client_ip = get_remote_address(request)
                logger.warning(
                    "Suspicious pattern detected",
                    pattern=pattern,
                    path=path,
                    ip=client_ip,
                    user_agent=user_agent
                )
                # Increment suspicion counter
                self.suspicious_patterns[client_ip] = self.suspicious_patterns.get(client_ip, 0) + 1
                
                # Block IP after multiple suspicious requests
                if self.suspicious_patterns[client_ip] > 5:
                    self.blocked_ips.add(client_ip)
                    logger.error("IP blocked for suspicious activity", ip=client_ip)
                
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid request"
                )
    
    async def _sanitize_request(self, request: Request):
        """Sanitize request data for PCI compliance."""
        if request.method in ["POST", "PUT", "PATCH"]:
            # Note: In a real implementation, you'd need to read and re-inject the body
            # This is a placeholder for PCI compliance logging
            logger.info("Request sanitization check", path=request.url.path)
    
    def _add_security_headers(self, response: Response):
        """Add security headers to response."""
        security_headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Content-Security-Policy": "default-src 'self'",
            "Referrer-Policy": "strict-origin-when-cross-origin",
            "Permissions-Policy": "geolocation=(), microphone=(), camera=()"
        }
        
        for header, value in security_headers.items():
            response.headers[header] = value
    
    async def _log_security_event(self, request: Request, response: Response, duration: float):
        """Log security-relevant events."""
        logger.info(
            "Security event",
            method=request.method,
            path=request.url.path,
            status_code=response.status_code,
            duration=duration,
            ip=get_remote_address(request),
            user_agent=request.headers.get("user-agent", ""),
            content_length=request.headers.get("content-length", "0")
        )


class PaymentSecurityValidator:
    """Specialized validator for payment-related security."""
    
    @staticmethod
    def validate_card_data(card_data: Dict) -> Dict:
        """Validate and sanitize card data."""
        if not card_data:
            return card_data
            
        # Mask sensitive data in logs
        masked_data = card_data.copy()
        
        if "card_number" in masked_data:
            card_number = str(masked_data["card_number"])
            if len(card_number) >= 4:
                masked_data["card_number"] = "*" * (len(card_number) - 4) + card_number[-4:]
        
        if "cvv" in masked_data:
            masked_data["cvv"] = "***"
            
        return masked_data
    
    @staticmethod
    def detect_pci_data(text: str) -> List[str]:
        """Detect potential PCI data in text."""
        detected = []
        
        for pattern_name, pattern in PCI_PATTERNS.items():
            if pattern.search(text):
                detected.append(pattern_name)
        
        return detected
    
    @staticmethod
    def sanitize_logs(log_data: Dict) -> Dict:
        """Sanitize log data to remove PCI information."""
        sanitized = {}
        
        for key, value in log_data.items():
            if key.lower() in SECURITY_CONFIG["sensitive_fields"]:
                sanitized[key] = "[REDACTED]"
            elif isinstance(value, str):
                # Check for PCI patterns
                detected = PaymentSecurityValidator.detect_pci_data(value)
                if detected:
                    sanitized[key] = f"[REDACTED-{','.join(detected).upper()}]"
                else:
                    sanitized[key] = value
            else:
                sanitized[key] = value
        
        return sanitized


# Rate limiting decorators
def rate_limit_payment(func):
    """Rate limit decorator for payment endpoints."""
    return limiter.limit(SECURITY_CONFIG["rate_limits"]["payment"])(func)

def rate_limit_auth(func):
    """Rate limit decorator for authentication endpoints."""
    return limiter.limit(SECURITY_CONFIG["rate_limits"]["auth"])(func)

def rate_limit_webhook(func):
    """Rate limit decorator for webhook endpoints."""
    return limiter.limit(SECURITY_CONFIG["rate_limits"]["webhook"])(func)

def rate_limit_default(func):
    """Default rate limit decorator."""
    return limiter.limit(SECURITY_CONFIG["rate_limits"]["default"])(func)


# Security utilities
def generate_request_id() -> str:
    """Generate unique request ID for tracking."""
    return hashlib.sha256(f"{time.time()}{datetime.utcnow()}".encode()).hexdigest()[:16]

def validate_ip_address(ip: str) -> bool:
    """Validate IP address format."""
    try:
        ipaddress.ip_address(ip)
        return True
    except ValueError:
        return False

def is_internal_ip(ip: str) -> bool:
    """Check if IP is from internal network."""
    try:
        ip_obj = ipaddress.ip_address(ip)
        return ip_obj.is_private or ip_obj.is_loopback
    except ValueError:
        return False


# Initialize security middleware instance
security_middleware = SecurityMiddleware()
