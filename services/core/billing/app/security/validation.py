"""
Input validation and sanitization for Billing Service.

Implements:
- Payment data validation
- PCI DSS compliance validation
- Input sanitization
- Business rule validation
- Data format validation
"""

import re
import uuid
from decimal import Decimal, InvalidOperation
from datetime import datetime, date
from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, validator, Field
from email_validator import validate_email, EmailNotValidError
import structlog

logger = structlog.get_logger(__name__)

# Validation patterns
PATTERNS = {
    "card_number": re.compile(r"^[0-9]{13,19}$"),
    "cvv": re.compile(r"^[0-9]{3,4}$"),
    "expiry_month": re.compile(r"^(0[1-9]|1[0-2])$"),
    "expiry_year": re.compile(r"^20[2-9][0-9]$"),
    "phone": re.compile(r"^\+?[1-9]\d{1,14}$"),
    "postal_code": re.compile(r"^[A-Z0-9\s\-]{3,10}$", re.IGNORECASE),
    "currency": re.compile(r"^[A-Z]{3}$"),
    "country_code": re.compile(r"^[A-Z]{2}$"),
    "amount": re.compile(r"^\d+(\.\d{1,2})?$"),
}

# Supported currencies
SUPPORTED_CURRENCIES = {
    "USD", "EUR", "GBP", "CAD", "AUD", "JPY", "CHF", "SEK", "NOK", "DKK",
    "KES", "UGX", "TZS", "RWF", "ZAR", "NGN", "GHS", "XOF", "XAF"
}

# Country codes
SUPPORTED_COUNTRIES = {
    "US", "CA", "GB", "DE", "FR", "IT", "ES", "NL", "BE", "AT", "CH", "SE",
    "NO", "DK", "FI", "AU", "NZ", "JP", "KE", "UG", "TZ", "RW", "ZA", "NG",
    "GH", "SN", "CI", "BF", "ML", "NE", "TD", "CF", "CM", "GA", "CG", "CD"
}


class ValidationError(Exception):
    """Custom validation error."""
    def __init__(self, field: str, message: str, code: str = "VALIDATION_ERROR"):
        self.field = field
        self.message = message
        self.code = code
        super().__init__(f"{field}: {message}")


class PaymentValidator:
    """Validator for payment-related data."""
    
    @staticmethod
    def validate_card_number(card_number: str) -> str:
        """Validate credit card number using Luhn algorithm."""
        if not card_number:
            raise ValidationError("card_number", "Card number is required")
        
        # Remove spaces and dashes
        card_number = re.sub(r"[\s\-]", "", card_number)
        
        # Check format
        if not PATTERNS["card_number"].match(card_number):
            raise ValidationError("card_number", "Invalid card number format")
        
        # Luhn algorithm validation
        def luhn_check(card_num: str) -> bool:
            digits = [int(d) for d in card_num]
            for i in range(len(digits) - 2, -1, -2):
                digits[i] *= 2
                if digits[i] > 9:
                    digits[i] -= 9
            return sum(digits) % 10 == 0
        
        if not luhn_check(card_number):
            raise ValidationError("card_number", "Invalid card number")
        
        return card_number
    
    @staticmethod
    def validate_cvv(cvv: str, card_type: Optional[str] = None) -> str:
        """Validate CVV code."""
        if not cvv:
            raise ValidationError("cvv", "CVV is required")
        
        if not PATTERNS["cvv"].match(cvv):
            raise ValidationError("cvv", "Invalid CVV format")
        
        # American Express uses 4-digit CVV
        if card_type == "amex" and len(cvv) != 4:
            raise ValidationError("cvv", "American Express requires 4-digit CVV")
        elif card_type != "amex" and len(cvv) != 3:
            raise ValidationError("cvv", "CVV must be 3 digits")
        
        return cvv
    
    @staticmethod
    def validate_expiry_date(month: str, year: str) -> tuple[str, str]:
        """Validate card expiry date."""
        if not PATTERNS["expiry_month"].match(month):
            raise ValidationError("expiry_month", "Invalid expiry month")
        
        if not PATTERNS["expiry_year"].match(year):
            raise ValidationError("expiry_year", "Invalid expiry year")
        
        # Check if card is expired
        expiry_date = date(int(year), int(month), 1)
        current_date = date.today().replace(day=1)
        
        if expiry_date < current_date:
            raise ValidationError("expiry_date", "Card has expired")
        
        return month, year
    
    @staticmethod
    def detect_card_type(card_number: str) -> str:
        """Detect card type from card number."""
        card_number = re.sub(r"[\s\-]", "", card_number)
        
        if card_number.startswith(("4",)):
            return "visa"
        elif card_number.startswith(("51", "52", "53", "54", "55")):
            return "mastercard"
        elif card_number.startswith(("34", "37")):
            return "amex"
        elif card_number.startswith(("6011", "65")):
            return "discover"
        else:
            return "unknown"


class AmountValidator:
    """Validator for monetary amounts."""
    
    @staticmethod
    def validate_amount(amount: Union[str, float, Decimal], currency: str = "USD") -> Decimal:
        """Validate monetary amount."""
        try:
            if isinstance(amount, str):
                # Remove currency symbols and spaces
                amount = re.sub(r"[^\d\.]", "", amount)
                decimal_amount = Decimal(amount)
            elif isinstance(amount, float):
                decimal_amount = Decimal(str(amount))
            else:
                decimal_amount = Decimal(amount)
        except (InvalidOperation, ValueError):
            raise ValidationError("amount", "Invalid amount format")
        
        # Check for negative amounts
        if decimal_amount < 0:
            raise ValidationError("amount", "Amount cannot be negative")
        
        # Check for zero amounts (except for refunds)
        if decimal_amount == 0:
            raise ValidationError("amount", "Amount cannot be zero")
        
        # Check maximum amount (prevent overflow)
        max_amount = Decimal("999999.99")
        if decimal_amount > max_amount:
            raise ValidationError("amount", f"Amount exceeds maximum of {max_amount}")
        
        # Check decimal places based on currency
        if currency in ["JPY", "KRW", "VND"]:  # Zero decimal currencies
            if decimal_amount % 1 != 0:
                raise ValidationError("amount", f"Currency {currency} does not support decimal places")
        else:
            # Round to 2 decimal places
            decimal_amount = decimal_amount.quantize(Decimal("0.01"))
        
        return decimal_amount
    
    @staticmethod
    def validate_currency(currency: str) -> str:
        """Validate currency code."""
        if not currency:
            raise ValidationError("currency", "Currency is required")
        
        currency = currency.upper()
        
        if not PATTERNS["currency"].match(currency):
            raise ValidationError("currency", "Invalid currency format")
        
        if currency not in SUPPORTED_CURRENCIES:
            raise ValidationError("currency", f"Unsupported currency: {currency}")
        
        return currency


class ContactValidator:
    """Validator for contact information."""
    
    @staticmethod
    def validate_email(email: str) -> str:
        """Validate email address."""
        if not email:
            raise ValidationError("email", "Email is required")
        
        try:
            validated_email = validate_email(email)
            return validated_email.email
        except EmailNotValidError as e:
            raise ValidationError("email", f"Invalid email: {str(e)}")
    
    @staticmethod
    def validate_phone(phone: str) -> str:
        """Validate phone number."""
        if not phone:
            raise ValidationError("phone", "Phone number is required")
        
        # Remove spaces and special characters
        phone_clean = re.sub(r"[\s\-\(\)]", "", phone)
        
        if not PATTERNS["phone"].match(phone_clean):
            raise ValidationError("phone", "Invalid phone number format")
        
        return phone_clean
    
    @staticmethod
    def validate_name(name: str, field_name: str = "name") -> str:
        """Validate person name."""
        if not name:
            raise ValidationError(field_name, f"{field_name.title()} is required")
        
        name = name.strip()
        
        if len(name) < 2:
            raise ValidationError(field_name, f"{field_name.title()} must be at least 2 characters")
        
        if len(name) > 100:
            raise ValidationError(field_name, f"{field_name.title()} must be less than 100 characters")
        
        # Check for valid characters (letters, spaces, hyphens, apostrophes)
        if not re.match(r"^[a-zA-Z\s\-\'\.]+$", name):
            raise ValidationError(field_name, f"Invalid characters in {field_name}")
        
        return name


class AddressValidator:
    """Validator for address information."""
    
    @staticmethod
    def validate_country(country: str) -> str:
        """Validate country code."""
        if not country:
            raise ValidationError("country", "Country is required")
        
        country = country.upper()
        
        if not PATTERNS["country_code"].match(country):
            raise ValidationError("country", "Invalid country code format")
        
        if country not in SUPPORTED_COUNTRIES:
            raise ValidationError("country", f"Unsupported country: {country}")
        
        return country
    
    @staticmethod
    def validate_postal_code(postal_code: str, country: str) -> str:
        """Validate postal code based on country."""
        if not postal_code:
            raise ValidationError("postal_code", "Postal code is required")
        
        postal_code = postal_code.upper().strip()
        
        # Country-specific validation
        country_patterns = {
            "US": re.compile(r"^\d{5}(-\d{4})?$"),
            "CA": re.compile(r"^[A-Z]\d[A-Z]\s?\d[A-Z]\d$"),
            "GB": re.compile(r"^[A-Z]{1,2}\d[A-Z\d]?\s?\d[A-Z]{2}$"),
            "DE": re.compile(r"^\d{5}$"),
            "FR": re.compile(r"^\d{5}$"),
        }
        
        pattern = country_patterns.get(country, PATTERNS["postal_code"])
        
        if not pattern.match(postal_code):
            raise ValidationError("postal_code", f"Invalid postal code for {country}")
        
        return postal_code


class BusinessValidator:
    """Validator for business rules."""
    
    @staticmethod
    def validate_subscription_interval(interval: str, interval_count: int) -> tuple[str, int]:
        """Validate subscription interval."""
        valid_intervals = ["day", "week", "month", "year"]
        
        if interval not in valid_intervals:
            raise ValidationError("interval", f"Invalid interval. Must be one of: {valid_intervals}")
        
        if interval_count < 1 or interval_count > 365:
            raise ValidationError("interval_count", "Interval count must be between 1 and 365")
        
        # Business rules
        if interval == "day" and interval_count > 365:
            raise ValidationError("interval_count", "Daily interval cannot exceed 365 days")
        elif interval == "week" and interval_count > 52:
            raise ValidationError("interval_count", "Weekly interval cannot exceed 52 weeks")
        elif interval == "month" and interval_count > 12:
            raise ValidationError("interval_count", "Monthly interval cannot exceed 12 months")
        elif interval == "year" and interval_count > 5:
            raise ValidationError("interval_count", "Yearly interval cannot exceed 5 years")
        
        return interval, interval_count
    
    @staticmethod
    def validate_trial_period(trial_days: int) -> int:
        """Validate trial period."""
        if trial_days < 0:
            raise ValidationError("trial_period_days", "Trial period cannot be negative")
        
        if trial_days > 365:
            raise ValidationError("trial_period_days", "Trial period cannot exceed 365 days")
        
        return trial_days


def sanitize_input(data: Any) -> Any:
    """Sanitize input data to prevent injection attacks."""
    if isinstance(data, str):
        # Remove potentially dangerous characters
        data = re.sub(r"[<>\"'&]", "", data)
        # Limit length
        if len(data) > 1000:
            data = data[:1000]
        return data.strip()
    elif isinstance(data, dict):
        return {key: sanitize_input(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [sanitize_input(item) for item in data]
    else:
        return data


def validate_uuid(uuid_string: str, field_name: str = "id") -> str:
    """Validate UUID format."""
    try:
        uuid.UUID(uuid_string)
        return uuid_string
    except ValueError:
        raise ValidationError(field_name, f"Invalid UUID format for {field_name}")


# Validation helper functions
def validate_payment_data(payment_data: Dict[str, Any]) -> Dict[str, Any]:
    """Comprehensive payment data validation."""
    validated = {}
    
    # Validate amount and currency
    if "amount" in payment_data:
        currency = payment_data.get("currency", "USD")
        validated["currency"] = AmountValidator.validate_currency(currency)
        validated["amount"] = AmountValidator.validate_amount(payment_data["amount"], currency)
    
    # Validate card data if present
    if "card_number" in payment_data:
        validated["card_number"] = PaymentValidator.validate_card_number(payment_data["card_number"])
        validated["card_type"] = PaymentValidator.detect_card_type(validated["card_number"])
        
        if "cvv" in payment_data:
            validated["cvv"] = PaymentValidator.validate_cvv(
                payment_data["cvv"], 
                validated["card_type"]
            )
        
        if "expiry_month" in payment_data and "expiry_year" in payment_data:
            month, year = PaymentValidator.validate_expiry_date(
                payment_data["expiry_month"],
                payment_data["expiry_year"]
            )
            validated["expiry_month"] = month
            validated["expiry_year"] = year
    
    # Validate contact information
    if "email" in payment_data:
        validated["email"] = ContactValidator.validate_email(payment_data["email"])
    
    if "phone" in payment_data:
        validated["phone"] = ContactValidator.validate_phone(payment_data["phone"])
    
    # Validate names
    for name_field in ["first_name", "last_name", "name"]:
        if name_field in payment_data:
            validated[name_field] = ContactValidator.validate_name(
                payment_data[name_field], 
                name_field
            )
    
    # Validate address
    if "country" in payment_data:
        validated["country"] = AddressValidator.validate_country(payment_data["country"])
        
        if "postal_code" in payment_data:
            validated["postal_code"] = AddressValidator.validate_postal_code(
                payment_data["postal_code"],
                validated["country"]
            )
    
    return validated
