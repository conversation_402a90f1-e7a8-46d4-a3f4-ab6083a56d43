"""
Enhanced authentication and authorization for Billing Service.

Implements:
- JWT token validation
- Role-based access control
- API key authentication
- Service-to-service authentication
- Payment-specific permissions
"""

import jwt
import hashlib
import secrets
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from fastapi import HTTPException, status, Depends, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel
import structlog
from enum import Enum

logger = structlog.get_logger(__name__)

# Security configuration
JWT_SECRET_KEY = "your-secret-key-here"  # Should come from Vault
JWT_ALGORITHM = "HS256"
JWT_EXPIRATION_HOURS = 0.15
API_KEY_LENGTH = 32

security = HTTPBearer()


class UserRole(str, Enum):
    """User roles for authorization."""
    ADMIN = "admin"
    USER = "user"
    SERVICE = "service"
    BILLING_ADMIN = "billing_admin"
    PAYMENT_PROCESSOR = "payment_processor"


class Permission(str, Enum):
    """Granular permissions for billing operations."""
    # Billing permissions
    VIEW_BILLING = "billing:view"
    MANAGE_BILLING = "billing:manage"
    TRACK_USAGE = "billing:track_usage"
    
    # Payment permissions
    PROCESS_PAYMENTS = "payments:process"
    VIEW_PAYMENTS = "payments:view"
    REFUND_PAYMENTS = "payments:refund"
    
    # Subscription permissions
    MANAGE_SUBSCRIPTIONS = "subscriptions:manage"
    VIEW_SUBSCRIPTIONS = "subscriptions:view"
    CANCEL_SUBSCRIPTIONS = "subscriptions:cancel"
    
    # Invoice permissions
    GENERATE_INVOICES = "invoices:generate"
    VIEW_INVOICES = "invoices:view"
    SEND_INVOICES = "invoices:send"
    
    # Webhook permissions
    RECEIVE_WEBHOOKS = "webhooks:receive"
    MANAGE_WEBHOOKS = "webhooks:manage"
    
    # Admin permissions
    ADMIN_ACCESS = "admin:access"
    VIEW_ALL_DATA = "admin:view_all"


# Role-permission mapping
ROLE_PERMISSIONS = {
    UserRole.ADMIN: [perm for perm in Permission],
    UserRole.BILLING_ADMIN: [
        Permission.VIEW_BILLING,
        Permission.MANAGE_BILLING,
        Permission.TRACK_USAGE,
        Permission.GENERATE_INVOICES,
        Permission.VIEW_INVOICES,
        Permission.SEND_INVOICES,
        Permission.VIEW_SUBSCRIPTIONS,
        Permission.MANAGE_SUBSCRIPTIONS,
        Permission.VIEW_PAYMENTS,
    ],
    UserRole.PAYMENT_PROCESSOR: [
        Permission.PROCESS_PAYMENTS,
        Permission.VIEW_PAYMENTS,
        Permission.REFUND_PAYMENTS,
        Permission.RECEIVE_WEBHOOKS,
    ],
    UserRole.USER: [
        Permission.VIEW_BILLING,
        Permission.VIEW_SUBSCRIPTIONS,
        Permission.VIEW_PAYMENTS,
        Permission.VIEW_INVOICES,
    ],
    UserRole.SERVICE: [
        Permission.TRACK_USAGE,
        Permission.VIEW_BILLING,
        Permission.RECEIVE_WEBHOOKS,
    ]
}


class TokenData(BaseModel):
    """Token payload data."""
    user_id: str
    email: Optional[str] = None
    role: UserRole
    permissions: List[Permission]
    service_name: Optional[str] = None
    expires_at: datetime


class APIKeyData(BaseModel):
    """API key data."""
    key_id: str
    service_name: str
    permissions: List[Permission]
    created_at: datetime
    last_used: Optional[datetime] = None
    is_active: bool = True


class AuthenticationError(HTTPException):
    """Custom authentication error."""
    def __init__(self, detail: str = "Authentication failed"):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            headers={"WWW-Authenticate": "Bearer"}
        )


class AuthorizationError(HTTPException):
    """Custom authorization error."""
    def __init__(self, detail: str = "Insufficient permissions"):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail
        )


class AuthManager:
    """Authentication and authorization manager."""
    
    def __init__(self):
        self.api_keys: Dict[str, APIKeyData] = {}
        self.revoked_tokens: set = set()
    
    def create_jwt_token(
        self,
        user_id: str,
        role: UserRole,
        email: Optional[str] = None,
        service_name: Optional[str] = None,
        expires_delta: Optional[timedelta] = None
    ) -> str:
        """Create JWT token."""
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(hours=JWT_EXPIRATION_HOURS)
        
        permissions = ROLE_PERMISSIONS.get(role, [])
        
        payload = {
            "user_id": user_id,
            "email": email,
            "role": role.value,
            "permissions": [perm.value for perm in permissions],
            "service_name": service_name,
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": "access_token"
        }
        
        token = jwt.encode(payload, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
        
        logger.info(
            "JWT token created",
            user_id=user_id,
            role=role.value,
            service_name=service_name,
            expires_at=expire.isoformat()
        )
        
        return token
    
    def verify_jwt_token(self, token: str) -> TokenData:
        """Verify and decode JWT token."""
        try:
            if token in self.revoked_tokens:
                raise AuthenticationError("Token has been revoked")
            
            payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
            
            # Validate token type
            if payload.get("type") != "access_token":
                raise AuthenticationError("Invalid token type")
            
            # Create token data
            token_data = TokenData(
                user_id=payload["user_id"],
                email=payload.get("email"),
                role=UserRole(payload["role"]),
                permissions=[Permission(perm) for perm in payload.get("permissions", [])],
                service_name=payload.get("service_name"),
                expires_at=datetime.fromtimestamp(payload["exp"])
            )
            
            return token_data
            
        except jwt.ExpiredSignatureError:
            raise AuthenticationError("Token has expired")
        except jwt.InvalidTokenError as e:
            logger.warning("Invalid JWT token", error=str(e))
            raise AuthenticationError("Invalid token")
    
    def create_api_key(
        self,
        service_name: str,
        permissions: List[Permission]
    ) -> tuple[str, APIKeyData]:
        """Create API key for service-to-service authentication."""
        key_id = secrets.token_urlsafe(16)
        api_key = secrets.token_urlsafe(API_KEY_LENGTH)
        
        key_data = APIKeyData(
            key_id=key_id,
            service_name=service_name,
            permissions=permissions,
            created_at=datetime.utcnow()
        )
        
        # Store hashed version
        key_hash = hashlib.sha256(api_key.encode()).hexdigest()
        self.api_keys[key_hash] = key_data
        
        logger.info(
            "API key created",
            key_id=key_id,
            service_name=service_name,
            permissions=[perm.value for perm in permissions]
        )
        
        return api_key, key_data
    
    def verify_api_key(self, api_key: str) -> APIKeyData:
        """Verify API key."""
        key_hash = hashlib.sha256(api_key.encode()).hexdigest()
        
        key_data = self.api_keys.get(key_hash)
        if not key_data:
            raise AuthenticationError("Invalid API key")
        
        if not key_data.is_active:
            raise AuthenticationError("API key is inactive")
        
        # Update last used timestamp
        key_data.last_used = datetime.utcnow()
        
        return key_data
    
    def revoke_token(self, token: str):
        """Revoke JWT token."""
        self.revoked_tokens.add(token)
        logger.info("Token revoked")
    
    def revoke_api_key(self, api_key: str):
        """Revoke API key."""
        key_hash = hashlib.sha256(api_key.encode()).hexdigest()
        if key_hash in self.api_keys:
            self.api_keys[key_hash].is_active = False
            logger.info("API key revoked", key_id=self.api_keys[key_hash].key_id)


# Global auth manager instance
auth_manager = AuthManager()


# Dependency functions
async def get_current_user(
    request: Request,
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> TokenData:
    """Get current authenticated user."""
    token = credentials.credentials
    
    # Check if it's an API key (longer format)
    if len(token) > 50:  # API keys are longer
        try:
            key_data = auth_manager.verify_api_key(token)
            # Convert API key data to token data format
            return TokenData(
                user_id=f"service:{key_data.service_name}",
                email=None,
                role=UserRole.SERVICE,
                permissions=key_data.permissions,
                service_name=key_data.service_name,
                expires_at=datetime.utcnow() + timedelta(days=365)  # API keys don't expire
            )
        except AuthenticationError:
            pass  # Fall through to JWT verification
    
    # Try JWT token
    return auth_manager.verify_jwt_token(token)


def require_permission(permission: Permission):
    """Decorator to require specific permission."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Extract current user from kwargs
            current_user = None
            for key, value in kwargs.items():
                if isinstance(value, TokenData):
                    current_user = value
                    break
            
            if not current_user:
                raise AuthenticationError("Authentication required")
            
            if permission not in current_user.permissions:
                logger.warning(
                    "Permission denied",
                    user_id=current_user.user_id,
                    required_permission=permission.value,
                    user_permissions=[p.value for p in current_user.permissions]
                )
                raise AuthorizationError(f"Permission required: {permission.value}")
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


def require_role(role: UserRole):
    """Decorator to require specific role."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Extract current user from kwargs
            current_user = None
            for key, value in kwargs.items():
                if isinstance(value, TokenData):
                    current_user = value
                    break
            
            if not current_user:
                raise AuthenticationError("Authentication required")
            
            if current_user.role != role:
                logger.warning(
                    "Role access denied",
                    user_id=current_user.user_id,
                    required_role=role.value,
                    user_role=current_user.role.value
                )
                raise AuthorizationError(f"Role required: {role.value}")
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator


# Convenience dependency functions
async def get_admin_user(current_user: TokenData = Depends(get_current_user)) -> TokenData:
    """Get current user with admin role."""
    if current_user.role not in [UserRole.ADMIN, UserRole.BILLING_ADMIN]:
        raise AuthorizationError("Admin access required")
    return current_user


async def get_service_user(current_user: TokenData = Depends(get_current_user)) -> TokenData:
    """Get current service user."""
    if current_user.role != UserRole.SERVICE:
        raise AuthorizationError("Service access required")
    return current_user
