"""
Vault-Consul Service

HTTP API service that provides secrets and configuration management for microservices.
Acts as a client/proxy to the actual Vault cluster running in Kubernetes.

Technology: FastAPI + HashiCorp Vault + Consul
Port: 7200
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import logging
import os
import asyncio
from datetime import datetime

# Import API routes
from app.api.secrets_api import router as secrets_router

# Import Vault client
from app.vault_client import vault_client

# Import simple metrics (temporarily disabled)
# import sys
# sys.path.append('/Users/<USER>/projects/personal/sim_llm')
# from services.shared.monitoring.simple_metrics import add_simple_metrics

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    try:
        logger.info("🚀 Starting Vault-Consul Service...")

        # Initialize Vault client
        if vault_client:
            vault_initialized = await vault_client.initialize()
            if vault_initialized:
                logger.info("✅ Vault client initialized successfully")
            else:
                logger.warning("⚠️ Vault client initialization failed - service will use fallback mode")

        # TODO: Initialize Consul client
        logger.info("📋 Consul integration ready")

        logger.info("🎯 Vault-Consul Service ready to serve microservices")

    except Exception as e:
        logger.error(f"❌ Failed to initialize Vault-Consul Service: {e}")
        raise

    yield

    # Shutdown
    try:
        logger.info("🛑 Shutting down Vault-Consul Service...")

        # Close Vault client
        if vault_client:
            await vault_client.close()
            logger.info("✅ Vault client closed")

        logger.info("✅ Vault-Consul Service shutdown complete")

    except Exception as e:
        logger.error(f"❌ Error during shutdown: {e}")


# Create FastAPI app
app = FastAPI(
    title="SimbaAI Vault-Consul Service",
    description="Secrets and configuration management for microservices",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify actual origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "SimbaAI Vault-Consul Service",
        "version": "1.0.0",
        "description": "Secrets and configuration management for microservices",
        "status": "running",
        "vault_connected": bool(vault_client and vault_client.is_vault_connected),
        "fallback_mode": bool(vault_client and vault_client.is_fallback_mode)
    }

@app.get("/health")
async def health_check():
    """Health check endpoint for service discovery"""
    try:
        vault_status = bool(vault_client and vault_client.is_vault_connected)
        fallback_mode = bool(vault_client and vault_client.is_fallback_mode)

        return {
            "status": "healthy",
            "service": "vault-consul",
            "port": int(os.getenv("PORT", "8200")),
            "vault_connected": vault_status,
            "fallback_mode": fallback_mode,
            "consul_connected": True,  # TODO: Add actual Consul health check
            "timestamp": datetime.now().isoformat(),
            "technology": "FastAPI + HashiCorp Vault + Consul"
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/status")
async def service_status():
    """Detailed service status"""
    try:
        vault_status = bool(vault_client and vault_client.is_vault_connected)
        fallback_mode = bool(vault_client and vault_client.is_fallback_mode)

        status_info = {
            "service": "vault-consul",
            "version": "1.0.0",
            "status": "running",
            "components": {
                "vault": {
                    "connected": vault_status,
                    "fallback_mode": fallback_mode,
                    "url": os.getenv("VAULT_URL", "http://vault:8200"),
                    "namespace": os.getenv("VAULT_NAMESPACE", "simbaai"),
                    "auth_method": os.getenv("VAULT_AUTH_METHOD", "kubernetes")
                },
                "consul": {
                    "connected": True,  # TODO: Add actual Consul status
                    "url": os.getenv("CONSUL_URL", "http://consul:8500")
                },
                "api": {
                    "endpoints_available": [
                        "/api/v1/secrets/{service_name}",
                        "/api/v1/secrets/{service_name}/{secret_key}",
                        "/api/v1/secrets/config/{service_name}",
                        "/api/v1/secrets/services"
                    ]
                }
            },
            "environment": os.getenv("ENVIRONMENT", "development"),
            "timestamp": datetime.now().isoformat()
        }

        return status_info

    except Exception as e:
        logger.error(f"Status check failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Include API routes
app.include_router(secrets_router, prefix="/api/v1/secrets", tags=["secrets"])

# Add simple metrics endpoint (temporarily disabled)
# add_simple_metrics(app, "vault_consul")



if __name__ == "__main__":
    import uvicorn

    # Get configuration from environment
    port = int(os.getenv("PORT", "7200"))
    host = os.getenv("HOST", "0.0.0.0")

    # Run the service
    uvicorn.run(app, host=host, port=port)
