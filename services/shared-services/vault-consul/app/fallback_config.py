"""
Fallback Configuration for SimbaAI Services

This module provides fallback configuration when Vault is not available.
It uses your exact .env configuration structure as defaults.

Location: /services/shared-services/vault-consul/app/fallback_config.py
"""

import os
from typing import Dict, Any

def get_fallback_config() -> Dict[str, Any]:
    """
    Get fallback configuration from your .env structure.

    This provides the exact configuration from your .env file
    when Vault is not available.
    """

    return {
        # ============================================================================
        # APPLICATION CORE SETTINGS (from your .env)
        # ============================================================================
        "ENVIRONMENT": os.getenv("ENVIRONMENT", "development"),
        "DEBUG": os.getenv("DEBUG", "true"),
        "LOG_LEVEL": os.getenv("LOG_LEVEL", "INFO"),
        "SECRET_KEY": os.getenv("SECRET_KEY", "your-secret-key-here"),

        # ============================================================================
        # API CONFIGURATION (from your .env)
        # ============================================================================
        "API_HOST": os.getenv("API_HOST", "0.0.0.0"),
        "API_PORT": os.getenv("API_PORT", "8000"),
        "API_BASE_URL": os.getenv("API_BASE_URL", "http://localhost:8000"),
        "API_V1_STR": os.getenv("API_V1_STR", "/api/v1"),
        "API_USERNAME": os.getenv("API_USERNAME", "admin"),
        "API_PASSWORD": os.getenv("API_PASSWORD", "simba"),

        # Authentication & Security (from your .env)
        "ACCESS_TOKEN_EXPIRE_MINUTES": os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"),
        "ALGORITHM": os.getenv("ALGORITHM", "HS256"),
        "SESSION_TTL_MINUTES": os.getenv("SESSION_TTL_MINUTES", "30"),

        # CORS Settings (from your .env)
        "CORS_ORIGINS": os.getenv("CORS_ORIGINS", "http://localhost:3000,http://localhost:8080"),

        # Rate Limiting (from your .env)
        "RATE_LIMIT_ENABLED": os.getenv("RATE_LIMIT_ENABLED", "true"),
        "RATE_LIMIT_DEFAULT": os.getenv("RATE_LIMIT_DEFAULT", "100/minute"),
        "RATE_LIMIT_PREMIUM": os.getenv("RATE_LIMIT_PREMIUM", "500/minute"),

        # ============================================================================
        # DASHBOARD CONFIGURATION (from your .env)
        # ============================================================================
        "DASHBOARD_ENABLED": os.getenv("DASHBOARD_ENABLED", "true"),
        "DASHBOARD_AUTH_REQUIRED": os.getenv("DASHBOARD_AUTH_REQUIRED", "true"),

        # ============================================================================
        # VECTOR DATABASE CONFIGURATION - WEAVIATE (from your .env)
        # ============================================================================
        "VECTOR_DB_ENABLED": os.getenv("VECTOR_DB_ENABLED", "true"),
        "VECTOR_DB_TYPE": os.getenv("VECTOR_DB_TYPE", "weaviate"),
        "WEAVIATE_URL": os.getenv("WEAVIATE_URL", "http://localhost:8080"),
        "WEAVIATE_API_KEY": os.getenv("WEAVIATE_API_KEY", ""),
        "WEAVIATE_CLASS_NAME": os.getenv("WEAVIATE_CLASS_NAME", "SimbaVectors"),
        "WEAVIATE_TIMEOUT": os.getenv("WEAVIATE_TIMEOUT", "30.0"),
        "WEAVIATE_GRPC_HOST": os.getenv("WEAVIATE_GRPC_HOST", "localhost"),
        "WEAVIATE_GRPC_PORT": os.getenv("WEAVIATE_GRPC_PORT", "50051"),
        "WEAVIATE_GRPC_SECURE": os.getenv("WEAVIATE_GRPC_SECURE", "false"),

        # Legacy Qdrant Configuration (from your .env)
        "QDRANT_ENABLED": os.getenv("QDRANT_ENABLED", "false"),
        "QDRANT_URL": os.getenv("QDRANT_URL", "http://localhost:1"),
        "QDRANT_API_KEY": os.getenv("QDRANT_API_KEY", "disabled"),
        "QDRANT_PREFER_GRPC": os.getenv("QDRANT_PREFER_GRPC", "false"),
        "QDRANT_TIMEOUT": os.getenv("QDRANT_TIMEOUT", "10.0"),

        # ============================================================================
        # AI MODELS & MACHINE LEARNING CONFIGURATION (from your .env)
        # ============================================================================
        "DISABLE_ML_MODELS": os.getenv("DISABLE_ML_MODELS", "false"),
        "LOAD_NLP_MODELS": os.getenv("LOAD_NLP_MODELS", "false"),
        "USE_LOCAL_MODELS": os.getenv("USE_LOCAL_MODELS", "true"),
        "SIM_LLM_PRELOAD_MODELS": os.getenv("SIM_LLM_PRELOAD_MODELS", "false"),
        "MAX_LOADED_MODELS": os.getenv("MAX_LOADED_MODELS", "3"),
        "MAX_AGENT_STEPS": os.getenv("MAX_AGENT_STEPS", "5"),

        # Default Models (from your .env)
        "DEFAULT_MODEL": os.getenv("DEFAULT_MODEL", "distilgpt2"),
        "FALLBACK_MODEL": os.getenv("FALLBACK_MODEL", "distilgpt2"),
        "LOCAL_MODEL_NAME": os.getenv("LOCAL_MODEL_NAME", "distilgpt2"),
        "TEST_MODEL": os.getenv("TEST_MODEL", "TinyLlama/TinyLlama-1.1B-Chat-v1.0"),
        "SIMBA_FINE_TUNING_BASE_MODEL": os.getenv("SIMBA_FINE_TUNING_BASE_MODEL", "TinyLlama/TinyLlama-1.1B-Chat-v1.0"),

        # Model Behavior (from your .env)
        "DEFAULT_SYSTEM_PROMPT": os.getenv("DEFAULT_SYSTEM_PROMPT", "You are a helpful, respectful, and honest assistant."),
        "DIRECT_LANGUAGE_GENERATION": os.getenv("DIRECT_LANGUAGE_GENERATION", "false"),
        "ENABLE_TOOLS": os.getenv("ENABLE_TOOLS", "true"),
        "ENABLE_FACE_DETECTION": os.getenv("ENABLE_FACE_DETECTION", "false"),

        # Local Model Configuration (from your .env)
        "LOCAL_MODEL_DEVICE": os.getenv("LOCAL_MODEL_DEVICE", "cpu"),
        "LOCAL_MODEL_FORCE_CPU": os.getenv("LOCAL_MODEL_FORCE_CPU", "true"),
        "LOCAL_MODEL_QUANTIZATION": os.getenv("LOCAL_MODEL_QUANTIZATION", "none"),
        "LOCAL_MODEL_PATH": os.getenv("LOCAL_MODEL_PATH", "${HF_HOME}/local_models/distilgpt2"),

        # Embedding Models (from your .env)
        "EMBEDDING_MODEL": os.getenv("EMBEDDING_MODEL", "all-MiniLM-L6-v2"),

        # Audio Models (from your .env)
        "USE_LOCAL_TTS": os.getenv("USE_LOCAL_TTS", "false"),
        "USE_LOCAL_WHISPER": os.getenv("USE_LOCAL_WHISPER", "false"),

        # ============================================================================
        # HUGGING FACE CONFIGURATION (from your .env)
        # ============================================================================
        "HF_HOME": os.getenv("HF_HOME", "./data/model_cache"),
        "HF_HUB_CACHE": os.getenv("HF_HUB_CACHE", "${HF_HOME}/hub"),
        "HF_ASSETS_CACHE": os.getenv("HF_ASSETS_CACHE", "${HF_HOME}/assets"),
        "HF_DATASETS_CACHE": os.getenv("HF_DATASETS_CACHE", "${HF_HOME}/datasets"),
        "HUGGINGFACE_HUB_CACHE": os.getenv("HUGGINGFACE_HUB_CACHE", "${HF_HOME}/hub"),
        "TRANSFORMERS_CACHE": os.getenv("TRANSFORMERS_CACHE", "${HF_HOME}"),

        # ============================================================================
        # FILE STORAGE & DIRECTORIES (from your .env)
        # ============================================================================
        "FILE_STORAGE_TYPE": os.getenv("FILE_STORAGE_TYPE", "local"),
        "LOCAL_STORAGE_PATH": os.getenv("LOCAL_STORAGE_PATH", "./storage"),
        "MODELS_DIR": os.getenv("MODELS_DIR", "./data/models"),
        "MODEL_CACHE_DIR": os.getenv("MODEL_CACHE_DIR", "./data/model_cache"),
        "TRAINING_DATA_DIR": os.getenv("TRAINING_DATA_DIR", "./data/training"),
        "UPLOAD_DIR": os.getenv("UPLOAD_DIR", "./uploads"),

        # ============================================================================
        # DATABASE CONFIGURATION (from your .env)
        # ============================================================================
        # MongoDB Configuration
        "MONGODB_URI": os.getenv("MONGODB_URI", "**************************************************************"),
        "MONGODB_URL": os.getenv("MONGODB_URL", "mongodb://localhost:27017/sim_llm"),
        "MONGODB_USERNAME": os.getenv("MONGODB_USERNAME", "admin"),
        "MONGODB_PASSWORD": os.getenv("MONGODB_PASSWORD", "admin"),
        "MONGODB_DB": os.getenv("MONGODB_DB", "sim_llm"),
        "MONGODB_AUTH_SOURCE": os.getenv("MONGODB_AUTH_SOURCE", "admin"),
        "MONGODB_MESSAGE_TTL_DAYS": os.getenv("MONGODB_MESSAGE_TTL_DAYS", "90"),

        # PostgreSQL Configuration
        "DATABASE_URL": os.getenv("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/simba_models_database"),
        "POSTGRES_URL": os.getenv("POSTGRES_URL", "postgresql+asyncpg://postgres:postgres@localhost:5432/simba_models_database"),
        "POSTGRES_HOST": os.getenv("POSTGRES_HOST", "localhost"),
        "POSTGRES_SERVER": os.getenv("POSTGRES_SERVER", "localhost"),
        "POSTGRES_PORT": os.getenv("POSTGRES_PORT", "5432"),
        "POSTGRES_USER": os.getenv("POSTGRES_USER", "postgres"),
        "POSTGRES_PASSWORD": os.getenv("POSTGRES_PASSWORD", "postgres"),
        "POSTGRES_DB": os.getenv("POSTGRES_DB", "simba_models_database"),
        "POSTGRES_DB_SCHEMA": os.getenv("POSTGRES_DB_SCHEMA", "public"),
        "POSTGRES_DB_USE_SSL": os.getenv("POSTGRES_DB_USE_SSL", "false"),
        "POSTGRES_SQL_ECHO": os.getenv("POSTGRES_SQL_ECHO", "false"),
        "POSTGRES_POOL_SIZE": os.getenv("POSTGRES_POOL_SIZE", "5"),
        "POSTGRES_MAX_OVERFLOW": os.getenv("POSTGRES_MAX_OVERFLOW", "10"),

        # ============================================================================
        # OBJECT STORAGE (MINIO) CONFIGURATION (from your .env)
        # ============================================================================
        "MINIO_ENDPOINT": os.getenv("MINIO_ENDPOINT", "localhost:9000"),
        "MINIO_ACCESS_KEY": os.getenv("MINIO_ACCESS_KEY", "minioadmin"),
        "MINIO_SECRET_KEY": os.getenv("MINIO_SECRET_KEY", "minioadmin"),
        "MINIO_SECURE": os.getenv("MINIO_SECURE", "false"),
        "MINIO_REGION": os.getenv("MINIO_REGION", "us-east-1"),

        # MinIO Buckets (from your .env)
        "MINIO_FILES_BUCKET": os.getenv("MINIO_FILES_BUCKET", "files"),
        "MINIO_MODELS_BUCKET": os.getenv("MINIO_MODELS_BUCKET", "models"),
        "MINIO_RESULTS_BUCKET": os.getenv("MINIO_RESULTS_BUCKET", "results"),
        "MINIO_BUCKET_NAME": os.getenv("MINIO_BUCKET_NAME", "models"),
        "MINIO_SIMBAAI_BUCKET": os.getenv("MINIO_SIMBAAI_BUCKET", "simbaai-models"),
        "SIMBAAI_MODELS_DIR": os.getenv("SIMBAAI_MODELS_DIR", "./data/simbaai_models"),
        "SIMBAAI_MODEL_CACHE_DIR": os.getenv("SIMBAAI_MODEL_CACHE_DIR", "./data/simbaai_model_cache"),

        # ============================================================================
        # REDIS CONFIGURATION (from your .env)
        # ============================================================================
        "REDIS_HOST": os.getenv("REDIS_HOST", "127.0.0.1"),
        "REDIS_PORT": os.getenv("REDIS_PORT", "6379"),
        "REDIS_PASSWORD": os.getenv("REDIS_PASSWORD", "admin"),
        "REDIS_DB": os.getenv("REDIS_DB", "0"),
        "REDIS_CELERY_DB": os.getenv("REDIS_CELERY_DB", "1"),
        "REDIS_CLUSTER_MODE": os.getenv("REDIS_CLUSTER_MODE", "false"),
        "REDIS_CLUSTER_CONFIG": os.getenv("REDIS_CLUSTER_CONFIG", '{"decode_responses":true,"socket_timeout":5,"socket_connect_timeout":5,"retry_on_timeout":true,"read_from_replicas":true,"health_check_interval":30,"max_attempts":5}'),

        # ============================================================================
        # CELERY CONFIGURATION (from your .env)
        # ============================================================================
        "CELERY_BROKER_URL": os.getenv("CELERY_BROKER_URL", "redis://:admin@redis:6379/1"),
        "CELERY_RESULT_BACKEND": os.getenv("CELERY_RESULT_BACKEND", "redis://:admin@redis:6379/1"),

        # ============================================================================
        # KAFKA CONFIGURATION (from your .env)
        # ============================================================================
        "KAFKA_ENABLED": os.getenv("KAFKA_ENABLED", "true"),
        "KAFKA_BOOTSTRAP_SERVERS": os.getenv("KAFKA_BOOTSTRAP_SERVERS", "localhost:9092"),

        # ============================================================================
        # MONITORING & OBSERVABILITY (from your .env)
        # ============================================================================
        "MONITORING_ENABLED": os.getenv("MONITORING_ENABLED", "true"),
        "MONITORING_ENVIRONMENT": os.getenv("MONITORING_ENVIRONMENT", "development"),
        "MONITORING_SERVICE_NAME": os.getenv("MONITORING_SERVICE_NAME", "sim_llm"),
        "MONITORING_LOG_LEVEL": os.getenv("MONITORING_LOG_LEVEL", "INFO"),
        "MONITORING_LOG_JSON": os.getenv("MONITORING_LOG_JSON", "false"),
        "MONITORING_METRICS_ENABLED": os.getenv("MONITORING_METRICS_ENABLED", "true"),
        "MONITORING_METRICS_PORT": os.getenv("MONITORING_METRICS_PORT", "9091"),
        "MONITORING_SESSION_METRICS_PORT": os.getenv("MONITORING_SESSION_METRICS_PORT", "9092"),
        "MONITORING_TRACING_ENABLED": os.getenv("MONITORING_TRACING_ENABLED", "true"),
        "MONITORING_OTLP_ENDPOINT": os.getenv("MONITORING_OTLP_ENDPOINT", "http://localhost:4317"),

        # ============================================================================
        # PAYMENT PROVIDERS CONFIGURATION (from your .env)
        # ============================================================================
        "PAYPAL_CLIENT_ID": os.getenv("PAYPAL_CLIENT_ID", "********************************************************************************"),
        "PAYPAL_CLIENT_SECRET": os.getenv("PAYPAL_CLIENT_SECRET", "********************************************************************************"),
        "PAYPAL_ENVIRONMENT": os.getenv("PAYPAL_ENVIRONMENT", "sandbox"),
        "STRIPE_SECRET_KEY": os.getenv("STRIPE_SECRET_KEY", ""),
        "STRIPE_PUBLISHABLE_KEY": os.getenv("STRIPE_PUBLISHABLE_KEY", ""),
        "STRIPE_WEBHOOK_SECRET": os.getenv("STRIPE_WEBHOOK_SECRET", ""),
        "MPESA_CONSUMER_KEY": os.getenv("MPESA_CONSUMER_KEY", "************************************************"),
        "MPESA_CONSUMER_SECRET": os.getenv("MPESA_CONSUMER_SECRET", "****************************************************************"),
        "MPESA_BUSINESS_SHORT_CODE": os.getenv("MPESA_BUSINESS_SHORT_CODE", "600997"),
        "MPESA_PASSKEY": os.getenv("MPESA_PASSKEY", "****************************************************************"),
        "MPESA_ENVIRONMENT": os.getenv("MPESA_ENVIRONMENT", "sandbox"),
    }

def get_database_config_fallback() -> Dict[str, Any]:
    """Get database configuration fallback."""
    config = get_fallback_config()
    return {
        "mongodb_uri": config["MONGODB_URI"],
        "mongodb_url": config["MONGODB_URL"],
        "mongodb_username": config["MONGODB_USERNAME"],
        "mongodb_password": config["MONGODB_PASSWORD"],
        "mongodb_db": config["MONGODB_DB"],
        "database_url": config["DATABASE_URL"],
        "postgres_url": config["POSTGRES_URL"],
        "postgres_host": config["POSTGRES_HOST"],
        "postgres_port": int(config["POSTGRES_PORT"]),
        "postgres_user": config["POSTGRES_USER"],
        "postgres_password": config["POSTGRES_PASSWORD"],
        "postgres_db": config["POSTGRES_DB"],
        "redis_host": config["REDIS_HOST"],
        "redis_port": int(config["REDIS_PORT"]),
        "redis_password": config["REDIS_PASSWORD"],
        "redis_db": int(config["REDIS_DB"])
    }

def get_storage_config_fallback() -> Dict[str, Any]:
    """Get storage configuration fallback."""
    config = get_fallback_config()
    return {
        "minio_endpoint": config["MINIO_ENDPOINT"],
        "minio_access_key": config["MINIO_ACCESS_KEY"],
        "minio_secret_key": config["MINIO_SECRET_KEY"],
        "minio_secure": config["MINIO_SECURE"].lower() == "true",
        "minio_region": config["MINIO_REGION"],
        "file_storage_type": config["FILE_STORAGE_TYPE"],
        "local_storage_path": config["LOCAL_STORAGE_PATH"],
        "models_dir": config["MODELS_DIR"],
        "upload_dir": config["UPLOAD_DIR"]
    }

def get_ai_config_fallback() -> Dict[str, Any]:
    """Get AI configuration fallback."""
    config = get_fallback_config()
    return {
        "default_model": config["DEFAULT_MODEL"],
        "fallback_model": config["FALLBACK_MODEL"],
        "use_local_models": config["USE_LOCAL_MODELS"].lower() == "true",
        "max_loaded_models": int(config["MAX_LOADED_MODELS"]),
        "embedding_model": config["EMBEDDING_MODEL"],
        "hf_home": config["HF_HOME"],
        "hf_hub_cache": config["HF_HUB_CACHE"],
        "transformers_cache": config["TRANSFORMERS_CACHE"],
        "default_system_prompt": config["DEFAULT_SYSTEM_PROMPT"],
        "enable_tools": config["ENABLE_TOOLS"].lower() == "true",
        "local_model_device": config["LOCAL_MODEL_DEVICE"]
    }
