"""
Configuration Manager for SimbaAI Microservices

Manages configuration from multiple sources with priority:
1. Environment variables (highest priority)
2. Vault secrets (your .env configuration migrated to Vault)
3. Default values (lowest priority)

Location: /services/shared-services/vault-consul/app/config_manager.py
"""

import os
import asyncio
from typing import Dict, Any, Optional, Union, List
from pathlib import Path
import logging
from dataclasses import dataclass, field
import json

from .vault_client import vault_client

logger = logging.getLogger(__name__)


@dataclass
class ConfigSource:
    """Configuration source definition."""
    name: str
    priority: int
    enabled: bool = True
    data: Dict[str, Any] = field(default_factory=dict)


class ConfigManager:
    """
    Centralized configuration manager for microservices.
    
    Loads your .env configuration from Vault with the following structure:
    - shared_secrets: SECRET_KEY, passwords, API keys
    - database_config: MongoDB, PostgreSQL, Redis configuration
    - app_config: API, CORS, dashboard configuration
    - ai_config: Model and AI configuration
    - storage_config: MinIO and file storage configuration
    - external_config: Kafka, monitoring, payment configuration
    """
    
    def __init__(self, service_name: str):
        self.service_name = service_name
        self.environment = os.getenv("ENVIRONMENT", "development")
        
        # Configuration sources (ordered by priority)
        self.sources: List[ConfigSource] = [
            ConfigSource("environment", 1),  # Highest priority
            ConfigSource("vault_shared_secrets", 2),
            ConfigSource("vault_database_config", 3),
            ConfigSource("vault_app_config", 4),
            ConfigSource("vault_ai_config", 5),
            ConfigSource("vault_storage_config", 6),
            ConfigSource("vault_external_config", 7),
            ConfigSource("vault_service_secrets", 8),
            ConfigSource("defaults", 9)  # Lowest priority
        ]
        
        # Merged configuration
        self.config: Dict[str, Any] = {}
        
        # Secret keys from your .env file
        self.vault_secret_keys = {
            # Core secrets
            "SECRET_KEY", "API_PASSWORD",
            
            # Database passwords
            "MONGODB_PASSWORD", "POSTGRES_PASSWORD", "REDIS_PASSWORD",
            
            # Storage secrets
            "MINIO_SECRET_KEY", "MINIO_ACCESS_KEY",
            
            # Payment secrets
            "PAYPAL_CLIENT_SECRET", "STRIPE_SECRET_KEY", "STRIPE_WEBHOOK_SECRET",
            "MPESA_CONSUMER_SECRET", "MPESA_PASSKEY",
            
            # External API keys
            "WEAVIATE_API_KEY", "HUGGINGFACE_TOKEN",
            
            # Service secrets
            "JWT_SECRET_KEY", "ENCRYPTION_KEY", "API_SECRET_KEY"
        }
        
        self._initialized = False
    
    async def initialize(self) -> bool:
        """Initialize the configuration manager."""
        try:
            logger.info(f"⚙️ Initializing configuration manager for {self.service_name}")
            
            # Initialize Vault client
            if not vault_client._initialized:
                vault_initialized = await vault_client.initialize()
                if not vault_initialized:
                    logger.warning("⚠️ Vault not available, using environment variables only")
            
            # Load configuration from all sources
            await self._load_all_sources()
            
            # Merge configurations
            self._merge_configurations()
            
            # Validate critical configurations
            self._validate_config()
            
            self._initialized = True
            logger.info(f"✅ Configuration manager initialized for {self.service_name}")
            
            # Start configuration refresh task
            asyncio.create_task(self._config_refresh_task())
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize configuration manager: {e}")
            return False
    
    async def _load_all_sources(self) -> None:
        """Load configuration from all sources."""
        
        # 1. Load environment variables
        await self._load_environment_variables()
        
        # 2. Load from Vault (if available)
        if vault_client._initialized:
            await self._load_vault_configuration()
        
        # 3. Load defaults
        await self._load_defaults()
    
    async def _load_environment_variables(self) -> None:
        """Load configuration from environment variables."""
        try:
            env_config = {}
            
            # Load all environment variables
            for key, value in os.environ.items():
                # Convert string values to appropriate types
                env_config[key] = self._convert_value(value)
            
            self.sources[0].data = env_config
            logger.debug(f"📋 Loaded {len(env_config)} environment variables")
            
        except Exception as e:
            logger.error(f"❌ Error loading environment variables: {e}")
    
    async def _load_vault_configuration(self) -> None:
        """Load configuration from Vault (your .env structure)."""
        try:
            # Load shared secrets (SECRET_KEY, passwords, API keys)
            shared_secrets = await vault_client.get_shared_secrets()
            if shared_secrets:
                self.sources[1].data = shared_secrets
                logger.debug(f"🔐 Loaded {len(shared_secrets)} shared secrets from Vault")
            
            # Load database configuration (MongoDB, PostgreSQL, Redis)
            database_config = await vault_client.get_database_config()
            if database_config:
                self.sources[2].data = database_config
                logger.debug(f"🗄️ Loaded {len(database_config)} database configs from Vault")
            
            # Load application configuration (API, CORS, dashboard)
            app_config = await vault_client.get_app_config()
            if app_config:
                self.sources[3].data = app_config
                logger.debug(f"⚙️ Loaded {len(app_config)} app configs from Vault")
            
            # Load AI configuration (models, HuggingFace)
            ai_config = await vault_client.get_ai_config()
            if ai_config:
                self.sources[4].data = ai_config
                logger.debug(f"🤖 Loaded {len(ai_config)} AI configs from Vault")
            
            # Load storage configuration (MinIO, file storage)
            storage_config = await vault_client.get_storage_config()
            if storage_config:
                self.sources[5].data = storage_config
                logger.debug(f"💾 Loaded {len(storage_config)} storage configs from Vault")
            
            # Load external services configuration (Kafka, monitoring, payments)
            external_config = await vault_client.get_external_config()
            if external_config:
                self.sources[6].data = external_config
                logger.debug(f"🌐 Loaded {len(external_config)} external configs from Vault")
            
            # Load service-specific secrets
            service_secrets = await vault_client.get_service_secrets()
            if service_secrets:
                self.sources[7].data = service_secrets
                logger.debug(f"🔐 Loaded {len(service_secrets)} service secrets from Vault")
            
        except Exception as e:
            logger.error(f"❌ Error loading Vault configuration: {e}")
    
    async def _load_defaults(self) -> None:
        """Load default configuration values."""
        defaults = {
            # Service defaults
            "SERVICE_NAME": self.service_name,
            "ENVIRONMENT": self.environment,
            "LOG_LEVEL": "INFO",
            "DEBUG": "false",
            
            # Server defaults
            "API_HOST": "0.0.0.0",
            "API_PORT": "8000",
            "WORKERS": "1",
            
            # Database defaults (fallback if Vault unavailable)
            "MONGODB_HOST": "localhost",
            "MONGODB_PORT": "27017",
            "POSTGRES_HOST": "localhost",
            "POSTGRES_PORT": "5432",
            "REDIS_HOST": "127.0.0.1",
            "REDIS_PORT": "6379",
            
            # Storage defaults
            "MINIO_ENDPOINT": "localhost:9000",
            "FILE_STORAGE_TYPE": "local",
            
            # AI defaults
            "DEFAULT_MODEL": "distilgpt2",
            "USE_LOCAL_MODELS": "true",
            
            # Security defaults
            "ALGORITHM": "HS256",
            "ACCESS_TOKEN_EXPIRE_MINUTES": "30"
        }
        
        self.sources[8].data = defaults
        logger.debug(f"📋 Loaded {len(defaults)} default values")
    
    def _merge_configurations(self) -> None:
        """Merge configurations from all sources based on priority."""
        merged_config = {}
        
        # Merge in reverse priority order (lowest to highest)
        for source in reversed(self.sources):
            if source.enabled and source.data:
                merged_config.update(source.data)
        
        self.config = merged_config
        logger.debug(f"🔄 Merged configuration from {len(self.sources)} sources")
    
    def _validate_config(self) -> None:
        """Validate critical configuration values."""
        required_configs = [
            "SERVICE_NAME", "ENVIRONMENT", "API_HOST", "API_PORT"
        ]
        
        missing_configs = []
        for config_key in required_configs:
            if config_key not in self.config or not self.config[config_key]:
                missing_configs.append(config_key)
        
        if missing_configs:
            logger.error(f"❌ Missing required configurations: {missing_configs}")
            raise ValueError(f"Missing required configurations: {missing_configs}")
        
        logger.debug("✅ Configuration validation passed")
    
    def _convert_value(self, value: str) -> Union[str, int, float, bool, List, Dict]:
        """Convert string values to appropriate Python types."""
        if not isinstance(value, str):
            return value
        
        # Boolean conversion
        if value.lower() in ('true', 'false'):
            return value.lower() == 'true'
        
        # Integer conversion
        try:
            if '.' not in value:
                return int(value)
        except ValueError:
            pass
        
        # Float conversion
        try:
            return float(value)
        except ValueError:
            pass
        
        # JSON conversion
        if value.startswith(('{', '[')):
            try:
                return json.loads(value)
            except json.JSONDecodeError:
                pass
        
        # Return as string
        return value
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get a configuration value."""
        return self.config.get(key, default)
    
    def get_int(self, key: str, default: int = 0) -> int:
        """Get an integer configuration value."""
        value = self.get(key, default)
        try:
            return int(value)
        except (ValueError, TypeError):
            return default
    
    def get_bool(self, key: str, default: bool = False) -> bool:
        """Get a boolean configuration value."""
        value = self.get(key, default)
        if isinstance(value, bool):
            return value
        if isinstance(value, str):
            return value.lower() in ('true', '1', 'yes', 'on')
        return default
    
    def get_secret(self, key: str, default: str = None) -> Optional[str]:
        """Get a secret value (logs warning if not found)."""
        value = self.get(key, default)
        if not value and key in self.vault_secret_keys:
            logger.warning(f"⚠️ Secret {key} not found in configuration")
        return value
    
    async def refresh_vault_secrets(self) -> None:
        """Refresh secrets from Vault."""
        if vault_client._initialized:
            await self._load_vault_configuration()
            self._merge_configurations()
            logger.info("🔄 Vault configuration refreshed")
    
    async def _config_refresh_task(self) -> None:
        """Background task to refresh configuration periodically."""
        while self._initialized:
            try:
                # Refresh every 5 minutes
                await asyncio.sleep(300)
                await self.refresh_vault_secrets()
                
            except Exception as e:
                logger.error(f"❌ Error in config refresh task: {e}")
                await asyncio.sleep(60)
    
    def get_database_config(self) -> Dict[str, Any]:
        """Get database configuration from your .env structure."""
        return {
            # MongoDB (from your .env)
            "mongodb_uri": self.get("MONGODB_URI"),
            "mongodb_url": self.get("MONGODB_URL"),
            "mongodb_username": self.get("MONGODB_USERNAME"),
            "mongodb_password": self.get_secret("MONGODB_PASSWORD"),
            "mongodb_db": self.get("MONGODB_DB"),
            
            # PostgreSQL (from your .env)
            "database_url": self.get("DATABASE_URL"),
            "postgres_url": self.get("POSTGRES_URL"),
            "postgres_host": self.get("POSTGRES_HOST"),
            "postgres_port": self.get_int("POSTGRES_PORT"),
            "postgres_user": self.get("POSTGRES_USER"),
            "postgres_password": self.get_secret("POSTGRES_PASSWORD"),
            "postgres_db": self.get("POSTGRES_DB"),
            
            # Redis (from your .env)
            "redis_host": self.get("REDIS_HOST"),
            "redis_port": self.get_int("REDIS_PORT"),
            "redis_password": self.get_secret("REDIS_PASSWORD"),
            "redis_db": self.get_int("REDIS_DB")
        }
    
    def get_storage_config(self) -> Dict[str, Any]:
        """Get storage configuration from your .env structure."""
        return {
            # MinIO (from your .env)
            "minio_endpoint": self.get("MINIO_ENDPOINT"),
            "minio_access_key": self.get("MINIO_ACCESS_KEY"),
            "minio_secret_key": self.get_secret("MINIO_SECRET_KEY"),
            "minio_secure": self.get_bool("MINIO_SECURE"),
            "minio_region": self.get("MINIO_REGION"),
            
            # File storage (from your .env)
            "file_storage_type": self.get("FILE_STORAGE_TYPE"),
            "local_storage_path": self.get("LOCAL_STORAGE_PATH"),
            "models_dir": self.get("MODELS_DIR"),
            "upload_dir": self.get("UPLOAD_DIR")
        }
    
    def get_ai_config(self) -> Dict[str, Any]:
        """Get AI configuration from your .env structure."""
        return {
            # Model configuration (from your .env)
            "default_model": self.get("DEFAULT_MODEL"),
            "fallback_model": self.get("FALLBACK_MODEL"),
            "use_local_models": self.get_bool("USE_LOCAL_MODELS"),
            "max_loaded_models": self.get_int("MAX_LOADED_MODELS"),
            "embedding_model": self.get("EMBEDDING_MODEL"),
            
            # HuggingFace (from your .env)
            "hf_home": self.get("HF_HOME"),
            "hf_hub_cache": self.get("HF_HUB_CACHE"),
            "transformers_cache": self.get("TRANSFORMERS_CACHE"),
            
            # Model behavior (from your .env)
            "default_system_prompt": self.get("DEFAULT_SYSTEM_PROMPT"),
            "enable_tools": self.get_bool("ENABLE_TOOLS"),
            "local_model_device": self.get("LOCAL_MODEL_DEVICE")
        }
    
    def get_all_config(self) -> Dict[str, Any]:
        """Get all configuration (excluding secrets for logging)."""
        safe_config = {}
        for key, value in self.config.items():
            if key in self.vault_secret_keys:
                safe_config[key] = "***REDACTED***"
            else:
                safe_config[key] = value
        return safe_config
