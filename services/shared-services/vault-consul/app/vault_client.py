"""
Vault Client for SimbaAI Microservices

Provides secure secret management and configuration retrieval from HashiCorp Vault.
This client handles authentication, secret retrieval, and configuration management
for all microservices in the SimbaAI platform.

Location: /services/shared-services/vault-consul/app/vault_client.py
"""

import os
import asyncio
import json
from typing import Dict, Any, Optional, List
from pathlib import Path
import aiohttp
import logging
from datetime import datetime, timedelta
import base64

logger = logging.getLogger(__name__)


class VaultClient:
    """
    Async Vault client for microservices secret management.

    Features:
    - Kubernetes service account authentication
    - Token-based authentication
    - Secret caching with TTL
    - Automatic token renewal
    - Environment-specific secret paths
    - Configuration templating
    """

    def __init__(self):
        # Vault configuration
        self.vault_url = os.getenv("VAULT_URL", "http://localhost:8200")
        self.vault_namespace = os.getenv("VAULT_NAMESPACE", "simbaai")
        self.environment = os.getenv("ENVIRONMENT", "development")
        self.service_name = os.getenv("SERVICE_NAME", "unknown")

        # Authentication - Use token auth for development by default
        self.vault_token = os.getenv("VAULT_TOKEN", "simbaai-vault-token")  # Default dev token
        self.auth_method = os.getenv("VAULT_AUTH_METHOD", "token" if self.vault_token else "kubernetes")
        self.vault_role = os.getenv("VAULT_ROLE", f"simbaai-{self.service_name}")

        # Kubernetes auth
        self.k8s_service_account_token_path = os.getenv(
            "K8S_SERVICE_ACCOUNT_TOKEN_PATH",
            "/var/run/secrets/kubernetes.io/serviceaccount/token"
        )

        # Secret paths - Updated to match your configuration structure
        self.secret_base_path = f"secret/data/simbaai/{self.environment}"
        self.config_base_path = f"secret/data/simbaai/config/{self.environment}"

        # Client state
        self.session: Optional[aiohttp.ClientSession] = None
        self.token: Optional[str] = None
        self.token_expires_at: Optional[datetime] = None
        self.secret_cache: Dict[str, Dict[str, Any]] = {}
        self.cache_ttl = int(os.getenv("VAULT_CACHE_TTL", "300"))  # 5 minutes

        self._initialized = False
        self._fallback_mode = False

    @property
    def is_fallback_mode(self) -> bool:
        """Check if client is running in fallback mode."""
        return self._fallback_mode

    @property
    def is_vault_connected(self) -> bool:
        """Check if client is connected to Vault."""
        return self._initialized and not self._fallback_mode and bool(self.token)

    async def initialize(self) -> bool:
        """Initialize the Vault client and authenticate."""
        try:
            logger.info(f"🔐 Initializing Vault client for {self.service_name}")
            logger.info(f"🔧 Vault URL: {self.vault_url}")
            logger.info(f"🔧 Auth method: {self.auth_method}")
            logger.info(f"🔧 Environment: {self.environment}")

            # Create HTTP session
            timeout = aiohttp.ClientTimeout(total=10)  # Shorter timeout for initial checks
            self.session = aiohttp.ClientSession(timeout=timeout)

            # First, check if Vault is reachable
            logger.info("🔍 Checking Vault connectivity...")
            if not await self._check_vault_connectivity():
                logger.warning("⚠️ Vault server is not reachable - switching to fallback mode")
                await self._initialize_fallback_mode()
                return True

            # Vault is reachable, now try to authenticate
            logger.info("🔑 Vault is reachable, attempting authentication...")
            if await self._authenticate():
                self._initialized = True
                logger.info(f"✅ Vault client initialized successfully")
                logger.info(f"🔐 Connected to Vault at {self.vault_url}")

                # Start token renewal task
                asyncio.create_task(self._token_renewal_task())

                return True
            else:
                logger.warning("⚠️ Vault authentication failed - switching to fallback mode")
                await self._initialize_fallback_mode()
                return True

        except Exception as e:
            logger.error(f"❌ Failed to initialize Vault client: {e}")
            logger.info("💡 Service will continue in fallback mode")
            await self._initialize_fallback_mode()
            return True

    async def _check_vault_connectivity(self) -> bool:
        """Check if Vault server is reachable."""
        try:
            # Try to reach Vault's health endpoint
            async with self.session.get(f"{self.vault_url}/v1/sys/health") as response:
                if response.status in [200, 429, 472, 473, 501, 503]:
                    # These are valid Vault health status codes
                    logger.info("✅ Vault server is reachable")
                    return True
                else:
                    logger.warning(f"⚠️ Vault health check returned status {response.status}")
                    return False

        except aiohttp.ClientConnectorError as e:
            logger.warning(f"⚠️ Cannot connect to Vault server: {e}")
            return False
        except asyncio.TimeoutError:
            logger.warning("⚠️ Vault server connection timeout")
            return False
        except Exception as e:
            logger.warning(f"⚠️ Vault connectivity check failed: {e}")
            return False

    async def _initialize_fallback_mode(self) -> None:
        """Initialize the client in fallback mode."""
        self._initialized = True
        self._fallback_mode = True

        logger.info("🔄 Initializing in fallback mode...")
        logger.info("💡 Fallback mode uses:")
        logger.info("   - Environment variables from your system")
        logger.info("   - Default configuration values")
        logger.info("   - Local .env file values (if available)")
        logger.info("💡 To enable Vault integration:")
        logger.info("   - Ensure Vault server is running and accessible")
        logger.info("   - Set VAULT_TOKEN environment variable for token auth")
        logger.info("   - Or deploy in Kubernetes for service account auth")
        logger.info("   - Check Vault URL configuration")

        # Show current configuration for debugging
        logger.info(f"🔧 Current Vault URL: {self.vault_url}")
        logger.info(f"🔧 Current Auth Method: {self.auth_method}")
        if self.vault_token:
            logger.info("🔧 VAULT_TOKEN is set")
        else:
            logger.info("🔧 VAULT_TOKEN is not set")

    async def _authenticate(self) -> bool:
        """Authenticate with Vault using the configured method."""
        try:
            if self.auth_method == "kubernetes":
                return await self._authenticate_kubernetes()
            elif self.auth_method == "token":
                return await self._authenticate_token()
            elif self.auth_method == "approle":
                return await self._authenticate_approle()
            else:
                logger.error(f"❌ Unsupported auth method: {self.auth_method}")
                return False

        except Exception as e:
            logger.error(f"❌ Authentication failed: {e}")
            return False

    async def _authenticate_kubernetes(self) -> bool:
        """Authenticate using Kubernetes service account."""
        try:
            # Read service account token
            token_path = Path(self.k8s_service_account_token_path)
            if not token_path.exists():
                logger.info("💡 Running in local development mode - Kubernetes service account not available")

                # Fallback to token auth if available
                if self.vault_token:
                    logger.info("🔄 Falling back to token authentication")
                    return await self._authenticate_token()

                # If no token available, authentication fails
                logger.info("🔄 No VAULT_TOKEN provided for fallback authentication")
                return False

            with open(token_path, 'r') as f:
                jwt_token = f.read().strip()

            # Authenticate with Vault
            auth_data = {
                "role": self.vault_role,
                "jwt": jwt_token
            }

            async with self.session.post(
                f"{self.vault_url}/v1/auth/kubernetes/login",
                json=auth_data
            ) as response:
                if response.status == 200:
                    auth_response = await response.json()
                    self.token = auth_response["auth"]["client_token"]

                    # Calculate token expiration
                    lease_duration = auth_response["auth"]["lease_duration"]
                    self.token_expires_at = datetime.utcnow() + timedelta(seconds=lease_duration - 60)

                    logger.info(f"✅ Kubernetes authentication successful")
                    return True
                else:
                    error_text = await response.text()
                    logger.error(f"❌ Kubernetes auth failed: {response.status} - {error_text}")
                    return False

        except Exception as e:
            logger.error(f"❌ Kubernetes authentication error: {e}")
            return False

    async def _authenticate_token(self) -> bool:
        """Authenticate using a static token."""
        try:
            if not self.vault_token:
                logger.error("❌ VAULT_TOKEN not provided for token authentication")
                return False

            self.token = self.vault_token

            # Verify token is valid
            headers = {"X-Vault-Token": self.token}
            async with self.session.get(
                f"{self.vault_url}/v1/auth/token/lookup-self",
                headers=headers
            ) as response:
                if response.status == 200:
                    token_info = await response.json()

                    # Set expiration if token has TTL
                    ttl = token_info.get("data", {}).get("ttl", 0)
                    if ttl > 0:
                        self.token_expires_at = datetime.utcnow() + timedelta(seconds=ttl - 60)

                    logger.info(f"✅ Token authentication successful")
                    return True
                else:
                    logger.error(f"❌ Token validation failed: {response.status}")
                    return False

        except Exception as e:
            logger.error(f"❌ Token authentication error: {e}")
            return False

    async def get_secret(self, secret_path: str, use_cache: bool = True) -> Optional[Dict[str, Any]]:
        """
        Retrieve a secret from Vault.

        Args:
            secret_path: Path to the secret (relative to base path)
            use_cache: Whether to use cached values

        Returns:
            Secret data or None if not found
        """
        try:
            if not self._initialized:
                logger.error("❌ Vault client not initialized")
                return None

            # If in fallback mode, use fallback configuration
            if self._fallback_mode:
                logger.debug(f"🔄 Using fallback mode for secret: {secret_path}")
                return await self._get_fallback_secret(secret_path)

            # Check cache first
            cache_key = f"{self.secret_base_path}/{secret_path}"
            if use_cache and cache_key in self.secret_cache:
                cached_data = self.secret_cache[cache_key]
                if datetime.utcnow() < cached_data["expires_at"]:
                    logger.debug(f"📋 Using cached secret: {secret_path}")
                    return cached_data["data"]
                else:
                    # Remove expired cache entry
                    del self.secret_cache[cache_key]

            # Ensure token is valid
            if not await self._ensure_valid_token():
                logger.error("❌ Cannot get valid token for secret retrieval")
                return await self._get_fallback_secret(secret_path)

            # Retrieve secret from Vault
            headers = {"X-Vault-Token": self.token}
            full_path = f"{self.vault_url}/v1/{self.secret_base_path}/{secret_path}"

            async with self.session.get(full_path, headers=headers) as response:
                if response.status == 200:
                    secret_response = await response.json()
                    secret_data = secret_response.get("data", {}).get("data", {})

                    # Cache the secret
                    if use_cache:
                        self.secret_cache[cache_key] = {
                            "data": secret_data,
                            "expires_at": datetime.utcnow() + timedelta(seconds=self.cache_ttl)
                        }

                    logger.debug(f"✅ Retrieved secret: {secret_path}")
                    return secret_data

                elif response.status == 404:
                    logger.warning(f"⚠️ Secret not found in Vault: {secret_path}, using fallback")
                    return await self._get_fallback_secret(secret_path)
                else:
                    error_text = await response.text()
                    logger.error(f"❌ Failed to retrieve secret {secret_path}: {response.status} - {error_text}")
                    return await self._get_fallback_secret(secret_path)

        except Exception as e:
            logger.error(f"❌ Error retrieving secret {secret_path}: {e}")
            return await self._get_fallback_secret(secret_path)

    async def get_shared_secrets(self) -> Optional[Dict[str, Any]]:
        """Get shared secrets (your .env secrets)."""
        return await self.get_secret("shared_secrets")

    async def get_database_config(self) -> Optional[Dict[str, Any]]:
        """Get database configuration (your .env database config)."""
        return await self.get_secret("database_config")

    async def get_app_config(self) -> Optional[Dict[str, Any]]:
        """Get application configuration (your .env app config)."""
        return await self.get_secret("app_config")

    async def get_ai_config(self) -> Optional[Dict[str, Any]]:
        """Get AI models configuration (your .env AI config)."""
        return await self.get_secret("ai_config")

    async def get_storage_config(self) -> Optional[Dict[str, Any]]:
        """Get storage configuration (your .env storage config)."""
        return await self.get_secret("storage_config")

    async def get_external_config(self) -> Optional[Dict[str, Any]]:
        """Get external services configuration (your .env external config)."""
        return await self.get_secret("external_config")

    async def get_service_secrets(self) -> Optional[Dict[str, Any]]:
        """Get all secrets for the current service."""
        return await self.get_secret(f"services/{self.service_name}")

    async def _get_fallback_secret(self, secret_path: str) -> Optional[Dict[str, Any]]:
        """Get secret from fallback configuration (environment variables)."""
        try:
            # Import fallback config here to avoid circular imports
            from .fallback_config import get_fallback_config

            fallback_config = get_fallback_config()

            # Map secret paths to fallback config sections
            if secret_path == "shared_secrets":
                # Return all environment-based configuration
                return fallback_config
            elif secret_path == "database_config":
                # Return database-related config
                return {k: v for k, v in fallback_config.items()
                       if any(db in k.lower() for db in ['mongodb', 'postgres', 'database', 'redis'])}
            elif secret_path == "app_config":
                # Return app-related config
                return {k: v for k, v in fallback_config.items()
                       if any(app in k.lower() for app in ['api', 'cors', 'secret_key', 'environment', 'debug'])}
            elif secret_path == "ai_config":
                # Return AI-related config
                return {k: v for k, v in fallback_config.items()
                       if any(ai in k.lower() for ai in ['model', 'hf_', 'embedding', 'whisper', 'tts'])}
            elif secret_path == "storage_config":
                # Return storage-related config
                return {k: v for k, v in fallback_config.items()
                       if any(storage in k.lower() for storage in ['minio', 'storage', 'upload', 'file'])}
            elif secret_path == "external_config":
                # Return external services config
                return {k: v for k, v in fallback_config.items()
                       if any(ext in k.lower() for ext in ['paypal', 'stripe', 'mpesa', 'monitoring'])}
            elif secret_path.startswith("services/"):
                # Return service-specific config
                service_name = secret_path.split("/")[-1]
                return {k: v for k, v in fallback_config.items()
                       if service_name.lower() in k.lower()}
            else:
                # Return all config for unknown paths
                logger.debug(f"🔄 Unknown secret path {secret_path}, returning full fallback config")
                return fallback_config

        except Exception as e:
            logger.error(f"❌ Error getting fallback secret {secret_path}: {e}")
            return None

    async def _ensure_valid_token(self) -> bool:
        """Ensure the current token is valid and not expired."""
        if not self.token:
            return await self._authenticate()

        # Check if token is about to expire
        if self.token_expires_at and datetime.utcnow() >= self.token_expires_at:
            logger.info("🔄 Token expired, re-authenticating...")
            return await self._authenticate()

        return True

    async def _token_renewal_task(self) -> None:
        """Background task to renew tokens before they expire."""
        while self._initialized and not self._fallback_mode:
            try:
                if self.token_expires_at:
                    # Renew token 5 minutes before expiration
                    time_until_expiry = (self.token_expires_at - datetime.utcnow()).total_seconds()
                    if time_until_expiry <= 300:  # 5 minutes
                        logger.info("🔄 Renewing Vault token...")
                        await self._authenticate()

                # Check every minute
                await asyncio.sleep(60)

            except Exception as e:
                logger.error(f"❌ Error in token renewal task: {e}")
                await asyncio.sleep(60)

    async def close(self) -> None:
        """Close the Vault client and cleanup resources."""
        try:
            self._initialized = False
            self._fallback_mode = False
            if self.session:
                await self.session.close()
            logger.info("✅ Vault client closed")

        except Exception as e:
            logger.error(f"❌ Error closing Vault client: {e}")


# Global Vault client instance
vault_client = VaultClient()
