"""
PostgreSQL Cluster HTTP API Service

HTTP API service that provides PostgreSQL operations for microservices.
Acts as a proxy/client to the actual PostgreSQL cluster running in Kubernetes.

Technology: FastAPI + PostgreSQL Cluster
Port: 7201 (HTTP API), connects to PostgreSQL at 5432
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, Header
from fastapi.middleware.cors import CORSMiddleware
from typing import Dict, Any, Optional, List, Union
from pydantic import BaseModel
import logging
import os
import asyncio
import json
from datetime import datetime
import asyncpg
from loguru import logger
from app.vault_integration import initialize_vault

# Configure logging
logging.basicConfig(level=logging.INFO)

# Pydantic models for request/response
class QueryRequest(BaseModel):
    query: str
    params: Optional[List[Any]] = []

class TableCreate(BaseModel):
    table_name: str
    columns: List[Dict[str, str]]  # [{"name": "id", "type": "SERIAL PRIMARY KEY"}, ...]
    if_not_exists: bool = True

class RecordInsert(BaseModel):
    table_name: str
    data: Dict[str, Any]
    returning: Optional[List[str]] = ["*"]

class RecordUpdate(BaseModel):
    table_name: str
    data: Dict[str, Any]
    where_clause: str
    params: Optional[List[Any]] = []
    returning: Optional[List[str]] = ["*"]

class RecordSelect(BaseModel):
    table_name: str
    columns: Optional[List[str]] = ["*"]
    where_clause: Optional[str] = None
    params: Optional[List[Any]] = []
    order_by: Optional[str] = None
    limit: Optional[int] = None
    offset: Optional[int] = None

class UserCreate(BaseModel):
    username: str
    email: str
    password_hash: str
    metadata: Optional[Dict[str, Any]] = {}

class UserUpdate(BaseModel):
    user_id: int
    data: Dict[str, Any]

class UserAuthenticate(BaseModel):
    username: Optional[str] = None
    email: Optional[str] = None
    password: str

class UserAuthResponse(BaseModel):
    success: bool
    user: Optional[Dict[str, Any]] = None
    message: Optional[str] = None

# Create FastAPI app
app = FastAPI(
    title="SimbaAI PostgreSQL Cluster Service",
    description="PostgreSQL operations for microservices",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify actual origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# PostgreSQL connection pool
pg_pool: Optional[asyncpg.Pool] = None

# Service authentication
INTERNAL_SERVICE_TOKEN = os.getenv("INTERNAL_SERVICE_TOKEN", "simbaai-internal-token")

def verify_service_token(x_service_token: str = Header(None)):
    """Verify internal service token."""
    if x_service_token != INTERNAL_SERVICE_TOKEN:
        raise HTTPException(status_code=401, detail="Invalid service token")
    return True

async def get_pg_pool() -> asyncpg.Pool:
    """Get PostgreSQL connection pool."""
    global pg_pool
    if pg_pool is None:
        raise HTTPException(status_code=503, detail="PostgreSQL pool not initialized")
    return pg_pool

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "SimbaAI PostgreSQL Cluster Service",
        "version": "1.0.0",
        "description": "PostgreSQL operations for microservices",
        "status": "running",
        "postgresql_connected": pg_pool is not None,
        "endpoints": [
            "POST /api/v1/query/execute",
            "POST /api/v1/tables/create",
            "GET /api/v1/tables",
            "POST /api/v1/records/insert",
            "POST /api/v1/records/select",
            "PUT /api/v1/records/update",
            "DELETE /api/v1/records/delete",
            "POST /api/v1/users/create",
            "POST /api/v1/users/authenticate",
            "POST /api/v1/users/update-login",
            "GET /api/v1/users/{user_id}",
            "PUT /api/v1/users/update",
            "GET /api/v1/users",
            "GET /api/v1/stats",
            "GET /health"
        ]
    }

@app.get("/health")
async def health_check():
    """Health check endpoint for service discovery"""
    try:
        postgresql_status = False
        if pg_pool:
            async with pg_pool.acquire() as conn:
                await conn.fetchval("SELECT 1")
            postgresql_status = True

        return {
            "status": "healthy",
            "service": "postgresql-cluster",
            "port": int(os.getenv("PORT", "7201")),
            "postgresql_connected": postgresql_status,
            "timestamp": datetime.now().isoformat(),
            "technology": "FastAPI + PostgreSQL Cluster"
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Query Operations
@app.post("/api/v1/query/execute")
async def execute_query(
    request: QueryRequest,
    authenticated: bool = Depends(verify_service_token),
    pool: asyncpg.Pool = Depends(get_pg_pool)
):
    """Execute a raw SQL query."""
    try:
        async with pool.acquire() as conn:
            if request.query.strip().upper().startswith(('SELECT', 'WITH')):
                # Read query
                rows = await conn.fetch(request.query, *request.params)
                result = [dict(row) for row in rows]

                logger.info(f"📥 Executed SELECT query: {len(result)} rows")

                return {
                    "success": True,
                    "query_type": "SELECT",
                    "rows": result,
                    "count": len(result),
                    "timestamp": datetime.now().isoformat()
                }
            else:
                # Write query
                result = await conn.execute(request.query, *request.params)

                logger.info(f"✅ Executed query: {result}")

                return {
                    "success": True,
                    "query_type": "WRITE",
                    "result": result,
                    "timestamp": datetime.now().isoformat()
                }

    except Exception as e:
        logger.error(f"❌ Failed to execute query: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Table Operations
@app.post("/api/v1/tables/create")
async def create_table(
    request: TableCreate,
    authenticated: bool = Depends(verify_service_token),
    pool: asyncpg.Pool = Depends(get_pg_pool)
):
    """Create a new table."""
    try:
        # Build CREATE TABLE query
        columns_sql = ", ".join([f"{col['name']} {col['type']}" for col in request.columns])
        if_not_exists_sql = "IF NOT EXISTS" if request.if_not_exists else ""

        query = f"CREATE TABLE {if_not_exists_sql} {request.table_name} ({columns_sql})"

        async with pool.acquire() as conn:
            await conn.execute(query)

        logger.info(f"✅ Created table: {request.table_name}")

        return {
            "success": True,
            "table_name": request.table_name,
            "created": True,
            "columns": request.columns,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"❌ Failed to create table {request.table_name}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/tables")
async def list_tables(
    authenticated: bool = Depends(verify_service_token),
    pool: asyncpg.Pool = Depends(get_pg_pool)
):
    """List all tables in the database."""
    try:
        query = """
        SELECT table_name, table_type
        FROM information_schema.tables
        WHERE table_schema = 'public'
        ORDER BY table_name
        """

        async with pool.acquire() as conn:
            rows = await conn.fetch(query)
            tables = [dict(row) for row in rows]

        logger.info(f"📋 Listed {len(tables)} tables")

        return {
            "success": True,
            "tables": tables,
            "count": len(tables),
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"❌ Failed to list tables: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Record Operations
@app.post("/api/v1/records/insert")
async def insert_record(
    request: RecordInsert,
    authenticated: bool = Depends(verify_service_token),
    pool: asyncpg.Pool = Depends(get_pg_pool)
):
    """Insert a new record."""
    try:
        # Add timestamp
        data = request.data.copy()
        data["created_at"] = datetime.now()
        data["updated_at"] = datetime.now()

        # Build INSERT query
        columns = list(data.keys())
        placeholders = [f"${i+1}" for i in range(len(columns))]
        values = list(data.values())

        returning_clause = f"RETURNING {', '.join(request.returning)}" if request.returning else ""

        query = f"""
        INSERT INTO {request.table_name} ({', '.join(columns)})
        VALUES ({', '.join(placeholders)})
        {returning_clause}
        """

        async with pool.acquire() as conn:
            if request.returning:
                row = await conn.fetchrow(query, *values)
                result = dict(row) if row else None
            else:
                await conn.execute(query, *values)
                result = None

        logger.info(f"✅ Inserted record into {request.table_name}")

        return {
            "success": True,
            "table_name": request.table_name,
            "record": result,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"❌ Failed to insert record into {request.table_name}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/records/select")
async def select_records(
    request: RecordSelect,
    authenticated: bool = Depends(verify_service_token),
    pool: asyncpg.Pool = Depends(get_pg_pool)
):
    """Select records from a table."""
    try:
        # Build SELECT query
        columns_sql = ", ".join(request.columns) if request.columns != ["*"] else "*"
        query = f"SELECT {columns_sql} FROM {request.table_name}"

        params = []
        if request.where_clause:
            query += f" WHERE {request.where_clause}"
            params.extend(request.params or [])

        if request.order_by:
            query += f" ORDER BY {request.order_by}"

        if request.limit:
            query += f" LIMIT {request.limit}"

        if request.offset:
            query += f" OFFSET {request.offset}"

        async with pool.acquire() as conn:
            rows = await conn.fetch(query, *params)
            records = [dict(row) for row in rows]

        logger.info(f"📥 Selected {len(records)} records from {request.table_name}")

        return {
            "success": True,
            "table_name": request.table_name,
            "records": records,
            "count": len(records),
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"❌ Failed to select records from {request.table_name}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# User Management
@app.post("/api/v1/users/create")
async def create_user(
    request: UserCreate,
    authenticated: bool = Depends(verify_service_token),
    pool: asyncpg.Pool = Depends(get_pg_pool)
):
    """Create a new user."""
    try:
        query = """
        INSERT INTO users (username, email, hashed_password, metadata, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id, username, email, created_at
        """

        async with pool.acquire() as conn:
            row = await conn.fetchrow(
                query,
                request.username,
                request.email,
                request.password_hash,
                json.dumps(request.metadata),
                datetime.now(),
                datetime.now()
            )
            user = dict(row) if row else None

        logger.info(f"👤 Created user: {request.username}")

        return {
            "success": True,
            "user": user,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"❌ Failed to create user {request.username}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/users/authenticate")
async def authenticate_user(
    request: UserAuthenticate,
    authenticated: bool = Depends(verify_service_token),
    pool: asyncpg.Pool = Depends(get_pg_pool)
):
    """Authenticate a user by username/email and password."""
    try:
        # Build query to find user by username or email
        if request.email:
            query = """
            SELECT id, username, email, hashed_password, is_active, is_verified, status,
                   failed_login_attempts, last_login, created_at, updated_at
            FROM users
            WHERE email = $1
            """
            identifier = request.email
        elif request.username:
            query = """
            SELECT id, username, email, hashed_password, is_active, is_verified, status,
                   failed_login_attempts, last_login, created_at, updated_at
            FROM users
            WHERE username = $1
            """
            identifier = request.username
        else:
            return UserAuthResponse(
                success=False,
                message="Either username or email must be provided"
            )

        async with pool.acquire() as conn:
            row = await conn.fetchrow(query, identifier)

            if not row:
                logger.warning(f"🚫 Authentication failed: User not found for {identifier}")
                return UserAuthResponse(
                    success=False,
                    message="Invalid credentials"
                )

            user_data = dict(row)

            # Note: Password verification should be done by the calling service
            # This endpoint returns user data for the calling service to verify
            logger.info(f"✅ User found for authentication: {user_data['email']}")

            return UserAuthResponse(
                success=True,
                user=user_data,
                message="User found"
            )

    except Exception as e:
        logger.error(f"❌ Failed to authenticate user: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/users/update-login")
async def update_user_login(
    user_id: str,
    success: bool,
    authenticated: bool = Depends(verify_service_token),
    pool: asyncpg.Pool = Depends(get_pg_pool)
):
    """Update user login information after authentication attempt."""
    try:
        if success:
            # Successful login - update last_login and reset failed attempts
            query = """
            UPDATE users
            SET last_login = $1, failed_login_attempts = 0, updated_at = $2
            WHERE id = $3
            RETURNING id, email, last_login
            """
            params = [datetime.now(), datetime.now(), user_id]
        else:
            # Failed login - increment failed attempts
            query = """
            UPDATE users
            SET failed_login_attempts = COALESCE(failed_login_attempts, 0) + 1, updated_at = $1
            WHERE id = $2
            RETURNING id, email, failed_login_attempts
            """
            params = [datetime.now(), user_id]

        async with pool.acquire() as conn:
            row = await conn.fetchrow(query, *params)

            if row:
                result = dict(row)
                logger.info(f"✅ Updated login info for user {result['email']}")
                return {
                    "success": True,
                    "user": result,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {
                    "success": False,
                    "message": "User not found",
                    "timestamp": datetime.now().isoformat()
                }

    except Exception as e:
        logger.error(f"❌ Failed to update user login: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Startup and shutdown events
@app.on_event("startup")
async def startup_event():
    """Initialize PostgreSQL connection pool on startup"""
    global pg_pool
    try:
        logger.info("🚀 Starting PostgreSQL Cluster Service...")

        # Initialize Vault integration
        try:
            await initialize_vault()
        except Exception as e:
            logger.warning(f"Vault initialization failed: {e}")

        # Get PostgreSQL connection details from environment
        database_url = os.getenv("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/simba_models_database")

        # Initialize connection pool
        pg_pool = await asyncpg.create_pool(
            database_url,
            min_size=5,
            max_size=20,
            command_timeout=60
        )

        # Test connection
        async with pg_pool.acquire() as conn:
            await conn.fetchval("SELECT 1")

        # Initialize complete database with all tables and default data
        try:
            logger.info("🔧 Starting database initialization...")
            from app.services.database.init_database import init_complete_database
            init_result = await init_complete_database()

            if init_result.get("initialized", False):
                logger.info("✅ Complete database initialization successful!")
                logger.info(f"📊 Database: {init_result.get('database_name')}")
                logger.info(f"📋 Tables created: {init_result.get('tables_created', False)}")
                logger.info(f"📝 Default data: {init_result.get('default_data_inserted', False)}")
            else:
                logger.warning(f"⚠️ Database initialization failed: {init_result.get('error', 'Unknown error')}")
                logger.info("🔄 Service will continue in basic mode...")

        except Exception as db_init_error:
            logger.error(f"❌ Database initialization error: {db_init_error}")
            logger.info("🔄 Service will continue in basic mode...")

            # Try to create basic tables as fallback
            try:
                await create_default_tables()
            except Exception as fallback_error:
                logger.warning(f"⚠️ Fallback table creation failed: {fallback_error}")
                logger.info("🔄 Service will continue without table initialization...")

        logger.info("✅ PostgreSQL connection pool initialized successfully")
        logger.info("🎯 PostgreSQL Cluster Service ready to serve microservices")

    except Exception as e:
        logger.error(f"❌ Failed to initialize PostgreSQL Cluster Service: {e}")
        # Don't raise - allow service to start in degraded mode
        pg_pool = None

async def create_default_tables():
    """Create default tables for SimbaAI."""
    try:
        default_tables = [
            {
                "table_name": "users",
                "columns": [
                    {"name": "id", "type": "SERIAL PRIMARY KEY"},
                    {"name": "username", "type": "VARCHAR(255) UNIQUE NOT NULL"},
                    {"name": "email", "type": "VARCHAR(255) UNIQUE NOT NULL"},
                    {"name": "hashed_password", "type": "VARCHAR(255) NOT NULL"},
                    {"name": "metadata", "type": "JSONB DEFAULT '{}'"},
                    {"name": "created_at", "type": "TIMESTAMP DEFAULT NOW()"},
                    {"name": "updated_at", "type": "TIMESTAMP DEFAULT NOW()"}
                ]
            },
            {
                "table_name": "sessions",
                "columns": [
                    {"name": "id", "type": "SERIAL PRIMARY KEY"},
                    {"name": "user_id", "type": "INTEGER REFERENCES users(id)"},
                    {"name": "session_token", "type": "VARCHAR(255) UNIQUE NOT NULL"},
                    {"name": "expires_at", "type": "TIMESTAMP NOT NULL"},
                    {"name": "created_at", "type": "TIMESTAMP DEFAULT NOW()"}
                ]
            }
        ]

        for table_config in default_tables:
            try:
                await create_table(TableCreate(**table_config), True, pg_pool)
            except:
                pass  # Table might already exist

        logger.info("✅ Default tables created/verified")

    except Exception as e:
        logger.warning(f"⚠️ Failed to create default tables: {e}")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    global pg_pool
    try:
        logger.info("🛑 Shutting down PostgreSQL Cluster Service...")

        if pg_pool:
            await pg_pool.close()
            logger.info("✅ PostgreSQL connection pool closed")

        logger.info("✅ PostgreSQL Cluster Service shutdown complete")

    except Exception as e:
        logger.error(f"❌ Error during shutdown: {e}")

if __name__ == "__main__":
    import uvicorn

    # Get configuration from environment
    port = int(os.getenv("PORT", "7201"))
    host = os.getenv("HOST", "0.0.0.0")

    # Run the service
    uvicorn.run(app, host=host, port=port)
