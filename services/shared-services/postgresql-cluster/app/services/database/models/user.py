"""
User and user profile models.
"""
from datetime import datetime
from enum import Enum
from typing import Optional
from uuid import uuid4

from sqlalchemy import <PERSON>olean, Column, DateTime, Enum as SQLE<PERSON>, ForeignKey, Integer, String, Text, Table
from sqlalchemy.dialects.postgresql import UUID as PG_UUID, JSONB
from sqlalchemy.orm import relationship

from .base import Base

class UserStatus(str, Enum):
    """User status enumeration."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    PENDING = "pending_verification"

class UserRole(str, Enum):
    """User role enumeration."""
    SUPER_ADMIN = "super_admin"
    ADMIN = "admin"
    MANAGER = "manager"
    USER = "user"
    GUEST = "guest"


# Import the user_role association table from role.py to avoid duplication
# This will be imported when needed

class User(Base):
    """User account model."""
    __tablename__ = "users"

    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    email = Column(String(255), unique=True, nullable=False, index=True)
    username = Column(String(50), unique=True, nullable=True, index=True)
    hashed_password = Column(String(255), nullable=True)  # Nullable for OAuth users
    first_name = Column(String(50), nullable=True)
    last_name = Column(String(50), nullable=True)

    # Authentication
    is_active = Column(Boolean(), default=True)
    is_verified = Column(Boolean(), default=False)
    status = Column(SQLEnum(UserStatus), default=UserStatus.PENDING)
    last_login = Column(DateTime, nullable=True)
    failed_login_attempts = Column(Integer, default=0)

    # Password reset
    reset_password_token = Column(String(100), nullable=True, index=True)
    reset_password_sent_at = Column(DateTime, nullable=True)

    # Email verification
    email_verification_token = Column(String(100), nullable=True, index=True)
    email_verification_sent_at = Column(DateTime, nullable=True)

    # OAuth2
    oauth_provider = Column(String(50), nullable=True)  # e.g., 'google', 'github', 'apple'
    oauth_account_id = Column(String(255), nullable=True, index=True)
    oauth_account_data = Column(JSONB, nullable=True)  # Store additional OAuth data

    # User preferences and settings
    preferences = Column(JSONB, nullable=True, default=dict)  # Store user preferences including payment provider IDs

    # Relationships (user_role table is defined in role.py)
    roles = relationship("Role", secondary="user_roles", back_populates="users")
    profile = relationship("UserProfile", back_populates="user", uselist=False, cascade="all, delete-orphan")
    dashboard_widgets = relationship("AdminDashboardWidget", back_populates="admin", cascade="all, delete-orphan")

    # API Keys
    api_keys = relationship("APIKey", back_populates="user", cascade="all, delete-orphan")
    api_key_usage = relationship("APIKeyUsage", back_populates="user", cascade="all, delete-orphan")

    # Billing relationships
    subscriptions = relationship("Subscription", back_populates="user", cascade="all, delete-orphan")
    payment_methods = relationship("PaymentMethod", back_populates="user", cascade="all, delete-orphan")
    payments = relationship("Payment", back_populates="user", cascade="all, delete-orphan")

    @property
    def full_name(self) -> str:
        """Return the full name of the user."""
        return f"{self.first_name or ''} {self.last_name or ''}".strip()

    def has_role(self, role_name: str) -> bool:
        """Check if user has the specified role."""
        return any(role.name == role_name for role in self.roles)

    def is_admin(self) -> bool:
        """Check if user is an admin or super admin."""
        return any(role.name in [UserRole.ADMIN, UserRole.SUPER_ADMIN] for role in self.roles)

    def increment_failed_login_attempt(self) -> None:
        """Increment the failed login attempts counter."""
        self.failed_login_attempts = (self.failed_login_attempts or 0) + 1

        # Lock account after 5 failed attempts
        if self.failed_login_attempts >= 5:
            self.status = UserStatus.SUSPENDED

    def reset_login_attempts(self) -> None:
        """Reset failed login attempts counter and unlock account if needed."""
        self.failed_login_attempts = 0
        if self.status == UserStatus.SUSPENDED:
            self.status = UserStatus.ACTIVE

    @property
    def is_locked(self) -> bool:
        """Check if the account is locked due to failed login attempts."""
        return self.status == UserStatus.SUSPENDED and (self.failed_login_attempts or 0) >= 5


# Note: Role and Permission models are defined in role.py to avoid circular imports

class UserProfile(Base):
    """Extended user profile information."""
    __tablename__ = "user_profiles"

    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid4)
    user_id = Column(PG_UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), unique=True)

    # Contact Information
    phone_number = Column(String(20), nullable=True)
    company = Column(String(100), nullable=True)
    job_title = Column(String(100), nullable=True)

    # Location
    address = Column(Text, nullable=True)
    city = Column(String(100), nullable=True)
    state = Column(String(100), nullable=True)
    country = Column(String(100), nullable=True)
    postal_code = Column(String(20), nullable=True)
    timezone = Column(String(50), nullable=True)

    # Preferences
    language = Column(String(10), default="en")
    theme = Column(String(20), default="light")
    notifications_enabled = Column(Boolean, default=True)
    email_notifications = Column(Boolean, default=True)

    # Social Media
    website = Column(String(255), nullable=True)
    linkedin_url = Column(String(255), nullable=True)
    twitter_handle = Column(String(50), nullable=True)
    github_username = Column(String(50), nullable=True)

    # Additional metadata
    bio = Column(Text, nullable=True)
    avatar_url = Column(String(255), nullable=True)
    metadata_ = Column("metadata", JSONB, default=dict)

    # Relationships
    user = relationship("User", back_populates="profile")
