"""
API Key models for PostgreSQL database.

Handles user-generated API keys for third-party integrations.
"""

from sqlalchemy import Column, String, Boolean, DateTime, Integer, Text, ForeignKey, JSON
from sqlalchemy.dialects.postgresql import UUID as PG_UUID
from sqlalchemy.orm import relationship
from uuid import uuid4
from datetime import datetime, timezone

from .base import Base


class APIKey(Base):
    """
    API Key model for user-generated keys.
    
    Follows industry standards from OpenAI, Anthropic, etc.
    """
    __tablename__ = "api_keys"
    
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    user_id = Column(PG_UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True)
    
    # Key information
    name = Column(String(255), nullable=False)  # Human-readable name
    key_hash = Column(String(64), nullable=False, unique=True, index=True)  # SHA256 hash of the key
    
    # Permissions and limits
    permissions = Column(JSON, nullable=False, default=list)  # List of allowed permissions
    monthly_token_limit = Column(Integer, nullable=False, default=1000)  # Monthly token limit
    
    # Status and timestamps
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), nullable=False)
    last_used_at = Column(DateTime(timezone=True), nullable=True)
    expires_at = Column(DateTime(timezone=True), nullable=True)
    revoked_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="api_keys")
    usage_records = relationship("APIKeyUsage", back_populates="api_key", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<APIKey(id={self.id}, name='{self.name}', user_id={self.user_id})>"
    
    @property
    def is_expired(self) -> bool:
        """Check if the API key is expired."""
        if self.expires_at is None:
            return False
        return datetime.now(timezone.utc) > self.expires_at
    
    @property
    def is_valid(self) -> bool:
        """Check if the API key is valid (active and not expired)."""
        return self.is_active and not self.is_expired


class APIKeyUsage(Base):
    """
    API Key usage tracking model.
    
    Records every API call made with user-generated keys.
    """
    __tablename__ = "api_key_usage"
    
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    api_key_id = Column(PG_UUID(as_uuid=True), ForeignKey("api_keys.id"), nullable=False, index=True)
    user_id = Column(PG_UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True)
    
    # Usage information
    tokens_used = Column(Integer, nullable=False, default=0)
    endpoint = Column(String(255), nullable=False)  # API endpoint used
    request_metadata = Column(JSON, nullable=True, default=dict)  # Additional request info
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), nullable=False, index=True)
    
    # Relationships
    api_key = relationship("APIKey", back_populates="usage_records")
    user = relationship("User", back_populates="api_key_usage")
    
    def __repr__(self):
        return f"<APIKeyUsage(id={self.id}, api_key_id={self.api_key_id}, tokens_used={self.tokens_used})>"


class APIKeyPermission(Base):
    """
    API Key permissions model.
    
    Defines available permissions for API keys.
    """
    __tablename__ = "api_key_permissions"
    
    id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid4, index=True)
    
    # Permission information
    name = Column(String(255), nullable=False, unique=True, index=True)  # e.g., "chat.completions"
    description = Column(Text, nullable=True)  # Human-readable description
    category = Column(String(100), nullable=False, index=True)  # e.g., "chat", "models", "admin"
    
    # Access control
    min_role_required = Column(String(50), nullable=False, default="free")  # Minimum role required
    is_admin_only = Column(Boolean, default=False, nullable=False)  # Admin-only permission
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), nullable=False)
    
    def __repr__(self):
        return f"<APIKeyPermission(name='{self.name}', category='{self.category}')>"


# Note: API key relationships are defined directly in the User model in user.py
